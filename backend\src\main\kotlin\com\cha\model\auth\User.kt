package com.cha.model.auth

import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Email
import java.time.Instant

@Serdeable
@Schema(name = "User", description = "User data transfer object")
data class User(
    @Schema(description = "Unique identifier of the user", example = "123")
    val id: String,
    @Schema(description = "Username of the user", example = "johndoe")
    val username: String,
    @field:Email
    @Schema(description = "Email address of the user", example = "<EMAIL>")
    val email: String,
    @Schema(description = "Whether the email is verified", example = "true")
    val emailVerified: <PERSON><PERSON>an,
    @Schema(description = "First name of the user", example = "John")
    val firstName: String,
    @Schema(description = "Last name of the user", example = "Doe")
    val lastName: String,
    @Schema(
        description = "URL to profile picture",
        example = "https://example.com/profile.jpg",
        required = false,
        nullable = true
    )
    val pictureUrl: String = "",
    @Schema(description = "Timestamp when the user was created", example = "2025-03-10T09:00:00Z")
    val createdAt: Instant,
    @Schema(description = "Timestamp when the user was last updated", example = "2025-03-15T10:00:00Z")
    val updatedAt: Instant,
    @Schema(description = "Roles assigned to the user", example = "[\"ROLE_USER\", \"ROLE_ADMIN\"]")
    val roles: List<String>,
)
