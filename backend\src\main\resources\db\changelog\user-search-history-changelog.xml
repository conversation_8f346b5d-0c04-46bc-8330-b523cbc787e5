<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.31.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">

    <changeSet id="create-user-search-history-table" author="copilot">
        <createTable tableName="user_search_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_user_search_history"/>
            </column>
            <column name="user_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="search_term" type="VARCHAR(500)">
                <constraints nullable="false"/>
            </column>
            <column name="search_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="create-user-search-history-indexes" author="copilot">
        <createIndex indexName="idx_user_search_history_user_id" tableName="user_search_history">
            <column name="user_id"/>
        </createIndex>
        <createIndex indexName="idx_user_search_history_created_at" tableName="user_search_history">
            <column name="created_at" descending="true"/>
        </createIndex>
        <createIndex indexName="idx_user_search_history_user_created" tableName="user_search_history">
            <column name="user_id"/>
            <column name="created_at" descending="true"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>