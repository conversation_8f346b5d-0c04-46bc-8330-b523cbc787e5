name: "Copilot Setup Steps"

# Automatically run the setup steps when they are changed to allow for easy validation, and
# allow manual testing through the repository's "Actions" tab
on:
  workflow_dispatch:
  push:
    paths:
      - copilot-setup-steps.yml
  pull_request:
    paths:
      - copilot-setup-steps.yml

jobs:
  # The job MUST be called `copilot-setup-steps` or it will not be picked up by Copilot.
  copilot-setup-steps:
    runs-on: ubuntu-latest

    # Grant GITHUB_TOKEN permissions for OIDC
    permissions:
      id-token: write # Required for requesting the JWT
      contents: read # Required for checking out code

    # You can define any steps you want, and they will run before the agent starts.
    # If you do not check out your code, <PERSON><PERSON><PERSON> will do this for you.
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install the latest version of uv
        uses: astral-sh/setup-uv@v6

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: corretto

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: <PERSON>ache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Make Gradle wrapper executable
        run: chmod +x ./gradlew

      - name: Install AWS CDK
        run: npm install -g aws-cdk

      - name: Configure AWS Credentials (Staging)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          audience: sts.amazonaws.com
          role-to-assume: arn:aws:iam::841607358390:role/GitHubAction-AssumeRole-ViewerBackend-staging
          aws-region: "us-east-1"
          role-session-name: GitHubAction-Staging-${{ github.run_id }}
