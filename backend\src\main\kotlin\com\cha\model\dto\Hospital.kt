package com.cha.model.dto

import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

/**
 * Data model representing a hospital and considered a tenant on the app's
 * server.
 */
@Schema(
    name = "Hospital",
    description = "Represents a healthcare facility or hospital and considered a tenant on the app's server."
)
@Serdeable
data class Hospital(
    @Schema(description = "Hospital name", example = "Mercy General Hospital")
    val name: String,

    @Schema(description = "URL to the hospital's logo image", example = "https://example.com/logos/mercy.png")
    val logoUrl: String,

    @Schema(description = "Hospital's website URL", example = "https://mercygeneral.org")
    val website: String,

    @Schema(description = "Physical address of the hospital")
    val address: Address,

    @Schema(description = "Primary contact telephone number", example = "******-123-4567")
    val telephone: String,

    @Schema(description = "Fax number", example = "******-123-4568", required = false)
    val fax: String,

    @Schema(description = "List of systems (EMR sources) used by the hospital")
    val systems: List<System>,

    @Schema(
        description = "Date when the hospital was archived (if applicable)",
        example = "2024-12-31"
    )
    val archived: LocalDate,

    @Schema(description = "Total number of patients in the hospital", example = "25000")
    val totalPatients: Int,

    @Schema(description = "Hospital's local timezone", example = "America/New_York")
    val timezone: String,

    @Schema(description = "Unique identifier for the tenant", example = "lrh-123")
    val tenantId: String,

    @Schema(description = "Short code used for tenant identification", example = "lrh")
    val tenantCode: String,
)
