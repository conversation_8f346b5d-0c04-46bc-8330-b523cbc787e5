name: Deploy to AWS Production Environment

on:
  release:
    types: [ created ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production
    env:
      AWS_ACCOUNT_ID: "************"
      AWS_REGION: "us-east-1"

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: corretto

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Install AWS CDK
        run: npm install -g aws-cdk

      - name: Configure AWS Credentials (Production)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          audience: sts.amazonaws.com
          role-to-assume: arn:aws:iam::${{ env.AWS_ACCOUNT_ID }}:role/GitHubAction-AssumeRole-ViewerBackend-production
          aws-region: ${{ env.AWS_REGION }}
          role-session-name: GitHubAction-Production-${{ github.run_id }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Build backend app
        env:
          # Database Configuration
          DATABASE_USERNAME: ${{ secrets.DATABASE_USERNAME }}
          DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
          DATABASE_HOST: ${{ vars.DATABASE_HOST }}
          DATABASE_PORT: ${{ vars.DATABASE_PORT }}

          # Security
          JWT_SECRET: ${{ secrets.JWT_SECRET }}

          # OAuth Configuration
          OAUTH_CLIENT_ID: ${{ vars.OAUTH_CLIENT_ID }}
          OAUTH_CLIENT_SECRET: ${{ vars.OAUTH_CLIENT_SECRET }}
          COGNITO_REGION: ${{ vars.COGNITO_REGION }}
          COGNITO_POOL_ID: ${{ vars.COGNITO_POOL_ID }}

          # AWS Resources
          VPC_ID: ${{ vars.VPC_ID }}
          PRIVATE_SUBNET_IDS: ${{ vars.PRIVATE_SUBNET_IDS }}
          RDS_SECURITY_GROUP_ID: ${{ vars.RDS_SECURITY_GROUP_ID }}
          ALLOWED_CORS_ORIGINS: ${{ vars.ALLOWED_CORS_ORIGINS }}

          # Environment specific
          MICRONAUT_ENVIRONMENTS: production
        run: ./gradlew :backend:build # Corrected to run from root like staging

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY_NAME: viewer-app-repo-${{ env.CDK_DEPLOY_ENVIRONMENT }}
          IMAGE_TAG: ${{ github.event.release.tag_name }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY_NAME:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY_NAME:$IMAGE_TAG

      - name: Install dependencies
        run: ./gradlew :infrastructure:build # Corrected to run from root like staging

      - name: Synthesize CDK stack
        env:
          # Pass environment variables to CDK synthesis
          CDK_DEPLOY_ENVIRONMENT: production
          DATABASE_USERNAME: ${{ secrets.DATABASE_USERNAME }}
          DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
          DATABASE_HOST: ${{ vars.DATABASE_HOST }}
          DATABASE_PORT: ${{ vars.DATABASE_PORT }}
          JWT_SECRET: ${{ secrets.JWT_SECRET }}
          APP_IMAGE_URI: ${{ steps.login-ecr.outputs.registry }}/viewer-app-repo-${{ env.CDK_DEPLOY_ENVIRONMENT }}:${{ github.event.release.tag_name }}
        run: |
          cd infrastructure
          cdk synth

      - name: Deploy CDK stack
        env:
          # Pass environment variables to CDK deployment
          CDK_DEPLOY_ENVIRONMENT: production
          APP_IMAGE_URI: ${{ steps.login-ecr.outputs.registry }}/viewer-app-repo-${{ env.CDK_DEPLOY_ENVIRONMENT }}:${{ github.event.release.tag_name }}
        run: |
          cd infrastructure
          cdk deploy --require-approval never
