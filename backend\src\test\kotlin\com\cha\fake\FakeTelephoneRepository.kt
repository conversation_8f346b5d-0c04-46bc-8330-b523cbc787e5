package com.cha.fake

import com.cha.model.entity.TelephoneEntity
import com.cha.repository.TelephoneRepository
import kotlinx.coroutines.flow.flowOf

class FakeTelephoneRepository : TelephoneRepository {
    override suspend fun findUniqueByEmpiAndType(empi: Int) = emptyList<TelephoneEntity>()
    override suspend fun findByEmpi(empi: Int) = emptyList<TelephoneEntity>()
    override suspend fun count() = 0L
    override suspend fun delete(entity: TelephoneEntity) = 0
    override suspend fun deleteAll() = 0
    override suspend fun deleteAll(entities: Iterable<TelephoneEntity>) = 0
    override suspend fun deleteById(id: Long) = 0
    override suspend fun existsById(id: Long) = false
    override fun findAll() = flowOf<TelephoneEntity>()
    override suspend fun findById(id: Long) = null
    override suspend fun <S : TelephoneEntity> save(entity: S) = entity
    override fun <S : TelephoneEntity> saveAll(entities: Iterable<S>) = flowOf<S>()
    override suspend fun <S : TelephoneEntity> update(entity: S) = entity
    override fun <S : TelephoneEntity> updateAll(entities: Iterable<S>) = flowOf<S>()
}