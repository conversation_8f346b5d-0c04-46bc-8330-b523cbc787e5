package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDate

@MappedEntity("mpi")
data class PatientEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val indexId: Int? = null,
    val systemId: Int? = null,
    val empi: Int,
    val mrn: String?,
    val name: String,
    val lastName: String,
    val firstName: String,
    val middleName: String? = null,
    val bthTs: LocalDate,
    val sex: String,
    val mpiIndex: String? = null,
    val systemName: String? = null,
    val securityLevel: Boolean? = false,
)
