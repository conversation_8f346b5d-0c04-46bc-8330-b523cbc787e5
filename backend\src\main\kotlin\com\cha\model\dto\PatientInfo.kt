package com.cha.model.dto

import com.cha.model.entity.PatientEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

/**
 * Detailed patient data for the patient info endpoint.
 *
 * This model includes all summary fields plus address and telephone
 * fields. For summary-only data, use [Patient] in [Patient.kt].
 */
@Serdeable
data class PatientInfo(
    override val empi: Int,
    override val firstName: String,
    override val lastName: String,
    override val middleName: String?,
    override val sex: String,
    override val birthdate: LocalDate,
    val systems: List<System>,
    @Schema(description = "List of addresses associated with the patient")
    val addresses: List<Address>,
    @Schema(description = "List of telephone numbers associated with the patient")
    val telephones: List<Telephone>,
) : PatientBase(empi, firstName, lastName, middleName, sex, birthdate)

/**
 * Helper function to create a PatientInfo from a fields map and lists of
 * addresses and telephones.
 */
private fun createPatientInfoFromFields(
    fields: Map<String, Any?>,
    addresses: List<Address>,
    telephones: List<Telephone>,
): PatientInfo {
    // Handle non-nullable sex field safely
    val sex = fields["sex"] as? String
        ?: throw IllegalArgumentException("Sex field is required and must be a String")

    // Handle non-nullable systems field safely
    @Suppress("UNCHECKED_CAST")
    val systems = when (val systemsValue = fields["systems"]) {
        is List<*> -> {
            // Verify each element is a System instance
            if (systemsValue.isNotEmpty() && systemsValue.any { it !is System }) {
                throw IllegalArgumentException("Systems list contains non-System elements")
            }
            systemsValue as List<System>
        }

        null -> throw IllegalArgumentException("Systems field is required")
        else -> throw IllegalArgumentException("Systems field must be a List<System>")
    }

    return PatientInfo(
        empi = fields["empi"] as Int,
        lastName = fields["lastName"] as String,
        firstName = fields["firstName"] as String,
        middleName = fields["middleName"] as? String,
        sex = sex,
        birthdate = fields["birthdate"] as LocalDate,
        systems = systems,
        addresses = addresses,
        telephones = telephones
    )
}

/**
 * Consolidate a List<PatientEntity> into a single detailed PatientInfo DTO
 * (for patient info endpoint) - Note: addresses and telephones must be
 * provided separately.
 */
fun List<PatientEntity>.consolidateToPatientInfoDto(
    addresses: List<Address>,
    telephones: List<Telephone>,
): PatientInfo? {
    if (this.isEmpty()) {
        return null
    }

    val firstEntity = this.first()
    val systems = PatientConverter.consolidateSystems(this)
    val fields = PatientConverter.extractBasicFields(firstEntity, systems)

    return createPatientInfoFromFields(fields, addresses, telephones)
}

