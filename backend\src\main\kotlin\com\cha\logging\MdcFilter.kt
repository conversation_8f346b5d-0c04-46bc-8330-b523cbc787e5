package com.cha.logging

import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpRequest
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Filter
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.utils.SecurityService
import jakarta.inject.Singleton
import org.reactivestreams.Publisher
import org.slf4j.MDC
import reactor.core.publisher.Flux
import java.util.UUID
import java.util.concurrent.TimeUnit

/**
 * Filter that populates MDC with request context information for logging.
 * This ensures all logs within a request have consistent tracing
 * information.
 */
@Singleton
@Filter("/**")
@Requires(beans = [SecurityService::class])
class MdcFilter(private val securityService: SecurityService) : HttpServerFilter {

    override fun doFilter(request: HttpRequest<*>, chain: ServerFilterChain): Publisher<MutableHttpResponse<*>> {
        val startTime = System.nanoTime()

        // Generate a unique request ID if not present
        val requestId = request.headers.get("X-Request-ID") ?: UUID.randomUUID().toString()

        // Generate a correlation ID for tracing across services
        val correlationId = request.headers.get("X-Correlation-ID") ?: UUID.randomUUID().toString()

        // Extract session ID if available
        val sessionId = request.headers.get("X-Session-ID") ?: ""

        // Extract client information
        val clientIp = request.remoteAddress.address?.hostAddress ?: ""
        val userAgent = request.headers.get("User-Agent") ?: ""

        // Extract request details
        val endpoint = request.path
        val httpMethod = request.method.name

        // Initialize MDC with request context
        MDC.put("requestId", requestId)
        MDC.put("correlationId", correlationId)
        MDC.put("sessionId", sessionId)
        MDC.put("clientIp", clientIp)
        MDC.put("userAgent", userAgent)
        MDC.put("endpoint", endpoint)
        MDC.put("httpMethod", httpMethod)
        MDC.put("requestPath", request.path)

        // Extract user information if authenticated
        securityService.authentication.ifPresent { auth: Authentication ->
            MDC.put("userId", auth.name)

            // Extract additional user attributes if available
            auth.attributes.let { attrs ->
                attrs["tenantId"]?.toString()?.let { MDC.put("tenantId", it) }
                attrs["userRole"]?.toString()?.let { MDC.put("userRole", it) }
            }
        }

        // Generate a transaction ID for this request
        MDC.put("transactionId", UUID.randomUUID().toString())

        return Flux.from(chain.proceed(request))
            .doOnNext { response ->
                // Add response information to MDC
                MDC.put("statusCode", response.status.code.toString())

                // Calculate execution time
                val executionTime = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startTime)
                MDC.put("executionTime", executionTime.toString())
            }
            .doFinally { _ ->
                // Clear MDC to prevent memory leaks
                MDC.clear()
            }
    }
}
