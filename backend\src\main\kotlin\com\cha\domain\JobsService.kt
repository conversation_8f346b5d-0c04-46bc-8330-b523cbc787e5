package com.cha.domain

import com.cha.model.auth.toUser
import com.cha.model.dto.Job
import com.cha.model.dto.JobStatus
import com.cha.model.dto.ReportRequest
import com.cha.model.dto.ReportType
import com.cha.model.dto.toDto
import com.cha.model.entity.JobEntity
import com.cha.repository.JobRepository
import com.cha.repository.ReportRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.data.model.CursoredPageable
import io.micronaut.data.model.Pageable
import io.micronaut.data.model.Slice
import io.micronaut.data.model.Sort
import io.micronaut.multitenancy.tenantresolver.TenantResolver
import io.micronaut.security.authentication.Authentication
import jakarta.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.time.LocalDateTime

private val log = KotlinLogging.logger {}

/** Service for managing background jobs for report generation. */
@Singleton
class JobsService(
    private val reportsService: ReportsService,
    private val tenantResolver: TenantResolver,
    private val jobRepository: JobRepository,
    private val reportRepository: ReportRepository,
) {
    // Coroutine scope for background processing
    private val jobScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * Creates a new job for report generation and starts processing it
     * asynchronously.
     */
    suspend fun createJob(request: ReportRequest, authentication: Authentication): Job {
        val user = authentication.toUser()

        // Create the entity first without saving
        val newJobEntity = JobEntity(
            userId = user.id,
            status = JobStatus.QUEUED,
            reportType = request.reportType,
            progress = 0,
            completedAt = null,
            errorMessage = null,
            reportId = null,
            parameters = mapOf(
                "empi" to request.empi,
                "visitId" to (request.visitId ?: ""),
                "mrn" to request.mrn,
                "includeSections" to request.includeSections.map { it.name },
                "title" to (request.title ?: generateReportTitle(request.reportType, request.empi)),
                "patientName" to (request.patientName ?: ""),
                "requesterUsername" to user.username,
            ),
            lastMessage = "Job created and queued"
        )

        // Save the entity and capture the returned entity which should have the ID
        val jobEntity = jobRepository.save(newJobEntity)

        log.info { "Created job ${jobEntity.id} for ${jobEntity.reportType} report for user ${jobEntity.userId}" }

        // Start processing the job asynchronously
        jobScope.launch {
            try {
                // Update status to PROCESSING
                jobEntity.status = JobStatus.PROCESSING
                // Store the message in memory but don't save to DB yet
                jobEntity.lastMessage = "Job processing started"
                jobRepository.update(jobEntity)
                log.info { "Job ${jobEntity.id} status updated to PROCESSING" }

                // Resolve tenant and generate report with progress updates
//                val tenant = tenantResolver.resolveTenant() // Assuming this is how you get the tenant
                reportsService.generateReport(
                    jobId = jobEntity.id,
                    empi = request.empi,
                    visitId = request.visitId,
                    dateRange = request.dateRange,
                    mrn = request.mrn,
                    reportType = request.reportType,
                    includeSections = request.includeSections,
                    fileTitle = request.title ?: generateReportTitle(request.reportType, request.empi),
//                    tenantId = tenantId
                )
                    // Sample the flow to limit collection frequency to once per second at most
                    // This prevents database overload from rapid progress updates
//                    .sample(Duration.ofSeconds(1))
                    .collect { progress ->
                        // Update job entity with progress updates
                        jobEntity.progress = progress.progress.toInt()
                        jobEntity.status = progress.status
                        // Store message in memory only until column is added to DB
                        jobEntity.lastMessage = progress.message

                        if (progress.reportId != null) {
                            // The job will be linked to the report via reportId
                            jobEntity.reportId = progress.reportId
                        }
                        if (progress.status == JobStatus.COMPLETED) {
                            jobEntity.completedAt = LocalDateTime.now()
                            // Force database update for completion status regardless of sampling
                            jobRepository.update(jobEntity)
                            log.info { "Job ${jobEntity.id} COMPLETED: ${progress.progress}% - ${progress.message}" }
                        } else {
                            // Regular progress update within the sampling interval
                            jobRepository.update(jobEntity)
                            log.info { "Job ${jobEntity.id} progress: ${progress.progress}% - ${progress.message}" }
                        }
                    }

                log.info { "Job ${jobEntity.id} COMPLETED. Report will be available via reportId in job entity" }
            } catch (e: Exception) {
                log.error(e) { "Error processing job ${jobEntity.id}" }
                // Update status to FAILED
                jobEntity.status = JobStatus.FAILED
                jobEntity.errorMessage = e.message
                // Store the error message in memory only until column is added to DB
                jobEntity.lastMessage = "Error: ${e.message}"
                jobEntity.completedAt = LocalDateTime.now()
                jobRepository.update(jobEntity)
            }
        }

        return jobEntity.toDto()
    }

    /** Retrieves a job by its ID for a specific user. */
    suspend fun getJob(jobId: String, userId: String): Job? {
        jobRepository.findByIdAndUserId(jobId, userId)?.let { job ->
            // If the job has a reportId, fetch the report entity
            job.reportId?.let { reportId ->
                val reportEntity = reportRepository.findById(reportId)
                return job.toDto(reportEntity)
            }
            return job.toDto()
        }

        throw IllegalArgumentException("Job with ID $jobId not found for user $userId")
    }

    /** Retrieves all jobs for a specific user. */
    suspend fun getAllJobs(
        userId: String,
        pageable: Pageable = CursoredPageable.from(
            10,
            Sort.of(Sort.Order("createdAt", Sort.Order.Direction.DESC, false))
        ),
    ): Slice<Job> {
        val slice = jobRepository.findByUserId(userId, pageable)
        val mapped = slice.content.map { job ->
            val reportId = job.reportId
            if (reportId != null) {
                val reportEntity = reportRepository.findById(reportId)
                job.toDto(reportEntity)
            } else {
                job.toDto()
            }
        }
        // Rebuild the slice with mapped content
        return object : Slice<Job> {
            override fun getContent(): List<Job> = mapped
            override fun getPageable() = slice.pageable
            override fun getSize() = slice.size
            override fun hasNext() = slice.hasNext()
            override fun nextPageable() = slice.nextPageable()
        }
    }

    /** Retrieves a job by its ID (internal use, e.g. for background worker). */
    suspend fun getJobInternal(jobId: String): Job? {
        return jobRepository.findById(jobId)?.toDto()
    }

    /** Updates a job (internal use, e.g. for background worker). */
    suspend fun updateJob(job: JobEntity): JobEntity {
        return jobRepository.update(job)
    }
//
//    /** Gets the report for a completed job. */
//    suspend fun getJobReport(jobId: String, userId: String): Report? {
//        // First verify the job belongs to the user and is completed
//        val jobEntity = jobRepository.findByIdAndUserId(jobId, userId)
//        if (jobEntity == null || jobEntity.status != JobStatus.COMPLETED) {
//            return null
//        }
//
//        // The JobEntity now directly holds the ReportEntity if the job is completed and report is generated.
//        // The reportId is the foreign key in the JobEntity table.
//        // We need to ensure the report object is loaded if it's LAZY fetched.
//        // However, if JobEntity.report is populated correctly upon job completion, we can use it directly.
//
//        // Assuming JobEntity.report is populated when the job completes:
//        return jobEntity.report?.toDto()
//    }

    private fun generateReportTitle(reportType: ReportType, empi: Int): String {
        return when (reportType) {
            ReportType.VISIT_SUMMARY -> "Visit Summary - $empi"
            ReportType.MRN -> "MRN Report - $empi"
            ReportType.ROI -> "Release of Information Report - $empi"
        }
    }
}
