package com.cha.domain

import com.cha.domain.exception.InvalidRequestException
import com.cha.domain.exception.ResourceNotFoundException
import com.cha.model.dto.Patient
import com.cha.model.dto.PatientInfo
import com.cha.model.dto.SearchType
import com.cha.model.dto.consolidateToPatientDto
import com.cha.model.dto.consolidateToPatientInfoDto
import com.cha.model.dto.toDto
import com.cha.repository.AddressRepository
import com.cha.repository.PatientRepository
import com.cha.repository.TelephoneRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException

private val log = KotlinLogging.logger {}

@Singleton
class PatientService(
    private val patientMpiRepository: PatientRepository,
    private val patientAddressRepository: AddressRepository,
    private val patientTelephoneRepository: TelephoneRepository,
) : PatientLookupService {
    // Define a formatter for parsing input dates (e.g., YYYY-MM-DD)
    // You could add more formatters if needed, but starting with one is simpler.
    private val dateFormatter = DateTimeFormatter.ISO_LOCAL_DATE

    suspend fun searchPatients(search: String, type: SearchType, pageable: Pageable): Page<Patient> {

        if (search.isBlank() || search.length < 3)
            throw InvalidRequestException(
                message = "Search term must be at least 3 characters long.",
            )

        val results = when (type) {
            SearchType.NAME -> patientMpiRepository.searchByName("%${cleanUserInput(search)}%", pageable)
            SearchType.MRN -> patientMpiRepository.searchByMrn(search, pageable)
            SearchType.DOB -> {
                try {
                    val birthDate = LocalDate.parse(search, dateFormatter)
                    patientMpiRepository.searchDistinctByBthTs(birthDate, pageable)
                } catch (e: DateTimeParseException) {
                    log.error(e) { "Invalid date format provided: $search" }
                    throw InvalidRequestException(
                        field = "q",
                        rejectedValue = search,
                        message = "The provided date '$search' is not in a valid format (expected YYYY-MM-DD).",
                        cause = e
                    )
                }
            }
        }
        return results.map { it.toDto() }
    }

    /** Normalizes user input into a standard, searchable format. */
    private fun cleanUserInput(input: String): String {
        // 1. Convert to lowercase.
        //    'hopkins,linda' -> 'hopkins,linda'
        //    'LINDAHOPKINS'  -> 'lindahopkins'

        // 2. Remove all characters that are not letters or numbers.
        //    'hopkins,linda' -> 'hopkinslinda'
        //    'lindahopkins'  -> 'lindahopkins'

        // NOTE: The order of the names is still different at this stage.
        // The SQL query handles rearranging the database field to match this.
        // But what if the user types 'linda,hopkins'?
        // We should reorder the cleaned input as well.

        val lowercased = input.lowercase()
        val parts = lowercased.split(",").map { it.replace(Regex("[^a-z0-9]"), "") }

        return if (parts.size > 1) {
            // If there was a comma, assume format is "last,first" and reverse it to "firstlast"
            // "hopkins,linda" -> parts ["hopkins", "linda"] -> "lindahopkins"
            parts[1] + "%" + parts[0]
        } else {
            // If no comma, assume it's already in a combined or natural order.
            // "lindahopkins" -> parts ["lindahopkins"] -> "lindahopkins"
            parts[0]
        }
    }

    override suspend fun getPatient(userId: String, empi: Int): Patient {
        val patientEntities = patientMpiRepository.findByEmpi(empi)

        // Consolidate the list of entities into a single Patient DTO
        // Handle the case where the list is empty (patient not found)
        return patientEntities.consolidateToPatientDto()
            ?: throw NoSuchElementException("Patient not found with EMPI: $empi")
    }

    /**
     * Get detailed patient information including addresses and telephone
     * numbers.
     *
     * @param empi Enterprise Master Patient Index to look up
     * @return [PatientInfo] object with all patient details
     */
    suspend fun getPatientInfo(empi: Int): PatientInfo {
        val patientEntities = patientMpiRepository.findByEmpi(empi)

        patientEntities.ifEmpty {
            throw ResourceNotFoundException(
                message = "Patient not found with EMPI: $empi",
                resourceType = "PatientInfo",
                identifier = empi.toString()
            )
        }

        // Fetch all addresses for this patient
        val addresses = patientAddressRepository.findByEmpi(empi).map { it.toDto() }

        // Fetch unique telephone records (already filtered for duplicates at the database level)
        val telephones = patientTelephoneRepository.findUniqueByEmpiAndType(empi).map { it.toDto() }

        // Consolidate the list of patient entities into a single PatientInfo DTO
        return patientEntities.consolidateToPatientInfoDto(addresses, telephones)
            ?: throw ResourceNotFoundException("Patient", empi.toString(), "Patient not found with EMPI: $empi")
    }
}
