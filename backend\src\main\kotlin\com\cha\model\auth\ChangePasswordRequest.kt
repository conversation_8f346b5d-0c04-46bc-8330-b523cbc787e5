package com.cha.model.auth

import io.micronaut.core.annotation.Introspected
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size

@Introspected
@Serdeable
@Schema(name = "ChangePasswordRequest", description = "Change password request for authenticated users")
data class ChangePasswordRequest(
    @field:NotBlank
    @field:Size(min = 8, max = 100)
    @Schema(description = "Current password", example = "CurrentPassword123!")
    val currentPassword: String,
    @field:NotBlank
    @field:Size(min = 8, max = 100)
    @Schema(description = "New password", example = "NewPassword123!")
    val newPassword: String
)