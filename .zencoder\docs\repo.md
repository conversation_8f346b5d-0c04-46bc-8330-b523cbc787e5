# CHA Viewer Backend Information

## Summary
CHA Viewer Backend is a Micronaut-based application that hosts the backend API for the CHA Viewer web application. It provides services for user authentication, patient data retrieval, and report generation.

## Structure
- **backend**: Main application module containing the Micronaut backend service
- **infrastructure**: AWS CDK infrastructure code for deploying the application
- **config**: Configuration files including detekt static analysis setup
- **gradle**: Gradle wrapper and dependency management
- **.github**: CI/CD workflows and GitHub configuration

## Language & Runtime
**Language**: Kotlin
**Version**: Java 21 (JDK 21)
**Build System**: Gradle
**Package Manager**: Gradle

## Dependencies
**Main Dependencies**:
- Micronaut 4.8.3 (Web framework)
- AWS SDK v2 (AWS integration)
- Kotlin Coroutines (Asynchronous programming)
- Micronaut Security (Authentication/Authorization)
- Micronaut Data JDBC (Database access)
- Micronaut OpenAPI (API documentation)
- Logback (Logging)

**Development Dependencies**:
- Detekt (Static code analysis)
- JUnit 5 (Testing)
- Kotlin Symbol Processing (KSP)

## Build & Installation
```bash
# Build the application
./gradlew :backend:build

# Run the application locally
./gradlew :backend:run

# Build a deployable JAR
./gradlew :backend:shadowJar

# Build Docker image
docker build -t viewer-backend:latest .
```

## Docker
**Dockerfile**: Dockerfile in project root
**Image**: Eclipse Temurin 22 JRE (Jammy)
**Configuration**: Exposes port 8080, runs as non-root user

## Testing
**Framework**: JUnit 5
**Test Location**: backend/src/test
**Run Command**:
```bash
./gradlew test
```

## Deployment
**Infrastructure as Code**: AWS CDK
**Deployment Target**: AWS Fargate
**Main Components**:
- ECS Fargate service
- Application Load Balancer
- S3 buckets for documents and logging
- RDS database
- Cognito for authentication

**Deployment Command**:
```bash
# Deploy infrastructure
cd infrastructure
cdk deploy
```

## Projects

### Backend (Micronaut Application)
**Configuration File**: backend/build.gradle.kts

#### Language & Runtime
**Language**: Kotlin
**Version**: Java 21
**Framework**: Micronaut 4.8.3
**Main Class**: com.cha.ApplicationKt

#### Dependencies
**Main Dependencies**:
- Micronaut HTTP Client
- Micronaut Security JWT
- AWS SDK integration
- Micronaut Data JDBC
- MS SQL JDBC driver
- Micronaut OpenAPI

#### Build & Installation
```bash
./gradlew :backend:run
./gradlew :backend:shadowJar
```

### Infrastructure (AWS CDK)
**Configuration File**: infrastructure/build.gradle.kts

#### Language & Runtime
**Language**: Kotlin
**Version**: Java 21
**Framework**: AWS CDK 2.202.0
**Main Class**: com.cha.infrastructure.MainKt

#### Dependencies
**Main Dependencies**:
- AWS CDK Lib
- Micronaut Starter AWS CDK
- CDK Nag (Security checks)

#### Build & Installation
```bash
cd infrastructure
cdk synth
cdk deploy
```