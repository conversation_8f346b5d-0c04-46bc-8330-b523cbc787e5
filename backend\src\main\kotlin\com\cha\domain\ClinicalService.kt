package com.cha.domain

import com.cha.model.dto.Alert
import com.cha.model.dto.Allergy
import com.cha.model.dto.Diagnosis
import com.cha.model.dto.HomeMedication
import com.cha.model.dto.Immunization
import com.cha.model.dto.LabResult
import com.cha.model.dto.LabResultReference
import com.cha.model.dto.Medication
import com.cha.model.dto.MicroComment
import com.cha.model.dto.MicroDetail
import com.cha.model.dto.MicroSuscept
import com.cha.model.dto.Notes
import com.cha.model.dto.Orders
import com.cha.model.dto.Procedure
import com.cha.model.dto.TranscriptionResult
import com.cha.model.dto.toDto
import com.cha.repository.AlertRepository
import com.cha.repository.AllergyRepository
import com.cha.repository.DiagnosisRepository
import com.cha.repository.HomeMedicationRepository
import com.cha.repository.ImmunizationRepository
import com.cha.repository.LabResultReferenceRepository
import com.cha.repository.LabResultRepository
import com.cha.repository.MedicationRepository
import com.cha.repository.MicroCommentRepository
import com.cha.repository.MicroDetailRepository
import com.cha.repository.MicroSusceptRepository
import com.cha.repository.NotesRepository
import com.cha.repository.OrderRepository
import com.cha.repository.ProcedureRepository
import com.cha.repository.TranscriptionResultRepository
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import jakarta.inject.Singleton

@Singleton
class ClinicalService(
    private val allergyRepository: AllergyRepository,
    private val diagnosisRepository: DiagnosisRepository,
    private val immunizationRepository: ImmunizationRepository,
    private val labResultRepository: LabResultRepository,
    private val medicationRepository: MedicationRepository,
    private val homeMedicationRepository: HomeMedicationRepository,
    private val orderRepository: OrderRepository,
    private val procedureRepository: ProcedureRepository,
    private val labResultReferenceRepository: LabResultReferenceRepository,
    private val notesRepository: NotesRepository,
    private val microDetailRepository: MicroDetailRepository,
    private val microCommentRepository: MicroCommentRepository,
    private val microSusceptRepository: MicroSusceptRepository,
    private val alertRepository: AlertRepository,
    private val transcriptionResultRepository: TranscriptionResultRepository,
) {

    suspend fun getPatientAllergies(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Allergy> {
        return allergyRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientDiagnosis(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Diagnosis> {
        return diagnosisRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientImmunizations(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Immunization> {
        return immunizationRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientLabResults(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<LabResult> {
        return labResultRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientMedications(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Medication> {
        return medicationRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientHomeMedications(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<HomeMedication> {
        return homeMedicationRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientOrders(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Orders> {
        return orderRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientProcedure(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Procedure> {
        return procedureRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientReferenceLabResult(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<LabResultReference> {
        return labResultReferenceRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientNotes(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Notes> {
        return notesRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientMicroDetail(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<MicroDetail> {
        return microDetailRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientMicroComment(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<MicroComment> {
        return microCommentRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientMicroSuscept(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<MicroSuscept> {
        return microSusceptRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientAlerts(
        empi: Int, mrn: String?, accountNumber: String?, pageable: Pageable,
    ): Page<Alert> {
        return alertRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientTranscriptionResults(
        empi: Int, mrn: String?, accountNumber: String?, pageable: Pageable,
    ): Page<TranscriptionResult> {
        return transcriptionResultRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }
}
