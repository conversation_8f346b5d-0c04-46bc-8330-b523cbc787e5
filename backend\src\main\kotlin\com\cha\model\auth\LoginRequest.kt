package com.cha.model.auth

import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size

@Serdeable
@Schema(
    description = "Login request payload containing username and password"
)
data class LoginRequest(
    @NotBlank
    @Size(min = 3, max = 50)
    @Schema(
        description = "The username of the user",
        example = "johndoe"
    )
    val username: String,

    @NotBlank
    @Size(min = 8, max = 50)
    @Schema(
        description = "The user's password",
        example = "P@ssw0rd!"
    )
    val password: String,
)