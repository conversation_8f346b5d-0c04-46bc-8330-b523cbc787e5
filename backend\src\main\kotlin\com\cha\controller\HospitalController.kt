package com.cha.controller

import com.cha.domain.HospitalService
import com.cha.model.dto.Hospital
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.zalando.problem.Problem

/** Controller for managing hospital information. */
@Secured(SecurityRule.IS_AUTHENTICATED)
@Controller("/v1/hospital")
@Tag(name = "Hospital", description = "Hospital information endpoints")
class HospitalController(
    private val hospitalService: HospitalService,
) {

    /**
     * Hospital Info.
     *
     * Fetches hospital information based on the **tenant ID**.
     */
    @Operation(
        summary = "Get hospital information",
        description = "Retrieves information about the hospital based on the current tenant ID",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Hospital information retrieved successfully",
                content = [Content(schema = Schema(implementation = Hospital::class))]
            ),
            ApiResponse(
                responseCode = "401",
                description = "Unauthorized - Authentication required",
                content = [Content(schema = Schema(implementation = Problem::class))]
            ),
            ApiResponse(
                responseCode = "404",
                description = "Hospital information not found",
                content = [Content(schema = Schema(implementation = Problem::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "Internal server error",
                content = [Content(schema = Schema(implementation = Problem::class))]
            )
        ]
    )
    @Get("/info")
    suspend fun getHospitalInfo(): HttpResponse<Hospital> {
        return HttpResponse.ok(hospitalService.fetchHospitalInfo())
    }
}
