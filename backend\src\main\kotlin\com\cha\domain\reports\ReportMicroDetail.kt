package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.Rectangle
import com.lowagie.text.pdf.PdfPCell
import com.lowagie.text.pdf.PdfPTable

private fun addMicroDetailsSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.microDetail?.let { microDetails ->
        document.add(createSectionTitle("Microbiology Details"))
        if (microDetails.isNotEmpty()) {
            microDetails.forEach { detail ->
                val detailTable = PdfPTable(2)
                detailTable.widthPercentage = 100f
                setupDataItemTable(detailTable)

                val headerText = "${detail.orderCodeDescription ?: "Micro Detail"} (${detail.orderCode ?: "N/A"})"
                detailTable.addCell(createDataItemHeaderCell(headerText, 2))

                // Create a cell for the result text that spans both columns if it exists
                if (!detail.resultText.isNullOrBlank()) {
                    val resultCell = PdfPCell(Paragraph(detail.resultText, FONT_BODY))
                    resultCell.colspan = 2
                    resultCell.border = Rectangle.NO_BORDER
                    resultCell.setPadding(5f)
                    detailTable.addCell(resultCell)
                }

                detailTable.addCell(createLabelValueCell("Accession #:", detail.accessionNumber, inTable = true))
                detailTable.addCell(createLabelValueCell("Isolate:", detail.accessionIsolate, inTable = true))
                detailTable.addCell(createLabelValueCell("Result Type:", detail.resultType, inTable = true))
                detailTable.addCell(
                    createLabelValueCell(
                        "Sequence #:",
                        detail.resultSequenceNumber?.toString(),
                        inTable = true
                    )
                )
                detailTable.addCell(createLabelValueCell("Released By:", detail.releasedBy, inTable = true))
                detailTable.addCell(
                    createLabelValueCell(
                        "Release Date:",
                        detail.releaseDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )

                document.add(detailTable)
            }
        } else {
            val paragraph = Paragraph("No microbiology details recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}