package com.cha.model.dto

import com.cha.model.entity.MicroSusceptEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a microbiology susceptibility result for a patient, including antibiotic, result, and isolate details."
)
data class MicroSuscept(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int?,
    @Schema(description = "System identifier for the microbiology record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the microbiology record.")
    val accountNumber: String?,
    @Schema(description = "Accession number for the microbiology test.")
    val accessionNumber: String?,
    @Schema(description = "Isolate identifier for the accession.")
    val accessionIsolate: String?,
    @Schema(description = "Description of the antibody tested.")
    val antibodyDescription: String?,
    @Schema(description = "Qualifier code for the result (e.g., S, I, R).")
    val resultQualifierCode: String?,
    @Schema(description = "Numeric value of the result, if applicable.")
    val resultValueNumber: String?,
    @Schema(description = "Category code for the result.")
    val categoryCode: String?,
    @Schema(description = "Description of the result category.")
    val categoryDescription: String?,
    @Schema(description = "Name of the person who entered the result.")
    val enteredBy: String?,
    @Schema(description = "Isolate number for the test.")
    val isolateNumber: Int?,
    @Schema(description = "Date and time when the result was entered.")
    val enteredDate: LocalDateTime?,
    @Schema(description = "Name of the system/source where the result was recorded.")
    val systemName: String?,
    @Schema(description = "Indicates if the record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean? = false,
    @Schema(description = "Admission date associated with the record, if relevant.")
    val admitDate: LocalDateTime?,
)

fun MicroSusceptEntity.toDto(): MicroSuscept {
    return MicroSuscept(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        accessionNumber = this.accessionNumber,
        accessionIsolate = this.accessionIsolate,
        antibodyDescription = this.antibodyDescription,
        resultQualifierCode = this.resultQualifierCode,
        resultValueNumber = this.resultValueNumber,
        categoryCode = this.categoryCode,
        categoryDescription = this.categoryDescription,
        enteredBy = this.enteredBy,
        enteredDate = this.enteredDate,
        isolateNumber = this.isolateNumber,
        systemName = this.systemName,
        securityLevel = this.securityLevel ?: false,
        admitDate = this.admitDate
    )
}
