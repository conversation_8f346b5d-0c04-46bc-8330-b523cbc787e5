org.gradle.jvmargs=-Xmx4096M -XX:MaxMetaspaceSize=512m
kotlin.code.style=official
kotlin.daemon.jvmargs=--add-opens=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED\
 --add-opens=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED \
 --add-opens=jdk.compiler/com.sun.tools.javac.comp=ALL-UNNAMED \
 --add-opens=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED \
 --add-opens=jdk.compiler/com.sun.tools.javac.jvm=ALL-UNNAMED \
 --add-opens=jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED \
 --add-opens=jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED \
 --add-opens=jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED \
 --add-opens=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED \
 --add-opens=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED