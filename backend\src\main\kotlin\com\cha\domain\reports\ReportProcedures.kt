package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable

private fun addProceduresSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.procedures?.let { procedures ->
        document.add(createSectionTitle("Procedures"))
        if (procedures.isNotEmpty()) {
            procedures.forEach { procedure ->
                val procedureTable = PdfPTable(2)
                procedureTable.widthPercentage = 100f
                setupDataItemTable(procedureTable)
                val headerText = "${procedure.cptDescription ?: "Procedure"} (${procedure.cptCode ?: "N/A"})"
                procedureTable.addCell(createDataItemHeaderCell(headerText, 2))
                procedureTable.addCell(createLabelValueCell("Caregiver:", procedure.caregiver, inTable = true))
                procedureTable.addCell(
                    createLabelValueCell(
                        "Status:",
                        if (procedure.active == 1) "Active" else "Inactive",
                        inTable = true
                    )
                )
                procedureTable.addCell(createLabelValueCell("Modifier 1:", procedure.modifier1, inTable = true))
                procedureTable.addCell(createLabelValueCell("Modifier 2:", procedure.modifier2, inTable = true))
                procedureTable.addCell(createLabelValueCell("Modifier 3:", procedure.modifier3, inTable = true))
                procedureTable.addCell(createLabelValueCell("Modifier 4:", procedure.modifier4, inTable = true))
                procedureTable.addCell(
                    createLabelValueCell(
                        "Timestamp:",
                        procedure.timestamp?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                document.add(procedureTable)
            }
        } else {
            val paragraph = Paragraph("No procedures recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}