package com.cha.controller

import com.cha.domain.FinancialService
import com.cha.model.dto.Bill
import com.cha.model.dto.Insurance
import com.cha.model.dto.Invoice
import com.cha.model.dto.InvoiceNote
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag

@Secured(SecurityRule.IS_AUTHENTICATED)
@Controller("/v1/patients")
@Tag(name = "Financial", description = "Financial endpoints for patient financial information")
class FinancialController(
    private val financialService: FinancialService,
) {

    @Operation(
        summary = "Get patient bills",
        description = "Retrieves a paginated list of bills for a specific patient",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "List of patient bills",
                content = [Content(schema = Schema(implementation = Page::class))]
            ),
            ApiResponse(responseCode = "401", description = "Unauthorized"),
            ApiResponse(responseCode = "404", description = "Patient not found")
        ]
    )
    @Get("/{empi}/bills")
    suspend fun getPatientBills(
        @Parameter(description = "Patient's EMPI identifier") @PathVariable empi: Int,
        @Parameter(description = "Pagination parameters", required = false) pageable: Pageable
    ): Page<Bill> {
        return financialService.getPatientBills(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient insurance",
        description = "Retrieves a paginated list of insurance records for a specific patient",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "List of patient insurance records",
                content = [Content(schema = Schema(implementation = Page::class))]
            ),
            ApiResponse(responseCode = "401", description = "Unauthorized"),
            ApiResponse(responseCode = "404", description = "Patient not found")
        ]
    )
    @Get("/{empi}/insurance")
    suspend fun getPatientInsurance(
        @Parameter(description = "Patient's EMPI identifier") @PathVariable empi: Int,
        @Parameter(description = "Pagination parameters", required = false) pageable: Pageable
    ): Page<Insurance> {
        return financialService.getPatientInsurance(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient invoices",
        description = "Retrieves a paginated list of invoices for a specific patient",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "List of patient invoices",
                content = [Content(schema = Schema(implementation = Page::class))]
            ),
            ApiResponse(responseCode = "401", description = "Unauthorized"),
            ApiResponse(responseCode = "404", description = "Patient not found")
        ]
    )
    @Get("/{empi}/invoice")
    suspend fun getPatientInvoice(
        @Parameter(description = "Patient's EMPI identifier") @PathVariable empi: Int,
        @Parameter(description = "Pagination parameters", required = false) pageable: Pageable
    ): Page<Invoice> {
        return financialService.getPatientInvoice(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient invoice notes",
        description = "Retrieves a paginated list of invoice notes for a specific patient",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "List of patient invoice notes",
                content = [Content(schema = Schema(implementation = Page::class))]
            ),
            ApiResponse(responseCode = "401", description = "Unauthorized"),
            ApiResponse(responseCode = "404", description = "Patient not found")
        ]
    )
    @Get("/{empi}/invoice-note")
    suspend fun getPatientInvoiceNote(
        @Parameter(description = "Patient's EMPI identifier") @PathVariable empi: Int,
        @Parameter(description = "Pagination parameters", required = false) pageable: Pageable
    ): Page<InvoiceNote> {
        return financialService.getPatientInvoiceNote(empi, null, null, pageable)
    }
}