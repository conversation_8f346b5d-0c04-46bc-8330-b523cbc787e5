<!--
  This logback configuration is optimized for two distinct environments:
  1. Cloud (prod, staging, aws): Outputs structured, asynchronous JSON to the console.
     This is the recommended practice for containerized applications, where a log
     forwarder (like FluentBit or the default ECS/EKS agent) ships stdout to CloudWatch.
  2. Local Development: Outputs colored, human-readable text for easy debugging.

  It uses Logback's native conditional processing to switch between configurations.
-->
<configuration>

    <!--
      PROPERTIES: Define application metadata. These are pulled from environment variables
      with sensible defaults, making the configuration flexible.
    -->
    <property name="APP_NAME" value="${APPLICATION_NAME:-viewer-backend}"/>
    <property name="APP_VERSION" value="${APP_VERSION:-dev}"/>
    <property name="ENVIRONMENT" value="${MICRONAUT_ENVIRONMENTS:-local}"/>


    <!-- ============================================== -->
    <!--                 CORE APPENDERS                 -->
    <!-- ============================================== -->

    <!--
      APPENDER 1: Structured JSON to Console (For CloudWatch)
      This appender formats logs as a single-line JSON object and prints to STDOUT.
      This is the standard approach for services running on AWS ECS, EKS, Fargate, etc.
      It decouples your application from the logging agent.
    -->
    <appender name="JSON_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="ch.qos.logback.contrib.json.classic.JsonLayout">
                <timestampFormat>yyyy-MM-dd'T'HH:mm:ss.SSS'Z'</timestampFormat>
                <timestampFormatTimezoneId>UTC</timestampFormatTimezoneId>

                <jsonFormatter class="ch.qos.logback.contrib.jackson.JacksonJsonFormatter">
                    <prettyPrint>false</prettyPrint>
                </jsonFormatter>

                <!--
                  Custom JSON fields for rich, queryable logs in CloudWatch.
                  This structure makes searching and filtering much more powerful.
                -->
                <appendLineSeparator>true</appendLineSeparator>
                <components>
                    <timestamp>
                        <fieldName>timestamp</fieldName>
                    </timestamp>
                    <severity/>
                    <threadName>
                        <fieldName>thread</fieldName>
                    </threadName>
                    <loggerName>
                        <fieldName>logger</fieldName>
                    </loggerName>
                    <message/>
                    <mdc>
                        <fieldName>mdc</fieldName>
                    </mdc>
                    <stackTrace>
                        <fieldName>stack_trace</fieldName>
                        <throwableConverter class="ch.qos.logback.contrib.json.classic.ShortenedThrowableConverter">
                            <maxDepthPerThrowable>30</maxDepthPerThrowable>
                            <maxLength>2048</maxLength>
                            <rootCauseFirst>true</rootCauseFirst>
                        </throwableConverter>
                    </stackTrace>
                    <!-- Add static application metadata to every log entry -->
                    <context/>
                    <customData>
                        <property>
                            <key>service</key>
                            <value>${APP_NAME}</value>
                        </property>
                        <property>
                            <key>version</key>
                            <value>${APP_VERSION}</value>
                        </property>
                        <property>
                            <key>environment</key>
                            <value>${ENVIRONMENT}</value>
                        </property>
                    </customData>
                </components>
            </layout>
        </encoder>
    </appender>

    <!--
      APPENDER 2: Human-Readable Console (Local Development)
      This enhanced pattern prints the MDC context and stack trace on new, colored lines.
    -->
    <appender name="PRETTY_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <withJansi>true</withJansi>
        <encoder>
            <pattern>%cyan(%d{HH:mm:ss.SSS}) %gray([%thread]) %highlight(%-5level) %magenta(%logger{36})
                [%X{correlationId:-}]- %msg%n%yellow(%X)%n%red(%ex{full})
            </pattern>
        </encoder>
    </appender>


    <!-- ============================================== -->
    <!--               ASYNC WRAPPERS                   -->
    <!-- ============================================== -->
    <!-- Asynchronous appenders improve application performance by handling I/O in a background thread. -->

    <appender name="ASYNC_JSON_CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="JSON_CONSOLE"/>
        <!--
          Don't block the application when the queue is full. This prevents logging from
          impacting application latency, but may result in dropped logs under extreme load.
        -->
        <neverBlock>true</neverBlock>
        <queueSize>512</queueSize> <!-- Increased queue size for better buffering -->
        <includeCallerData>false</includeCallerData> <!-- Caller data is expensive, disable in prod -->
    </appender>

    <appender name="ASYNC_PRETTY_CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="PRETTY_CONSOLE"/>
        <includeCallerData>true</includeCallerData> <!-- Enable caller data ONLY for local async debugging -->
    </appender>


    <!-- ============================================== -->
    <!--      ENVIRONMENT-SPECIFIC CONFIGURATIONS       -->
    <!-- ============================================== -->

    <!-- Conditional logging for Cloud environments (aws, prod, staging) -->
    <if condition='property("ENVIRONMENT").matches("aws|prod|staging")'>
        <then>
            <root level="INFO">
                <appender-ref ref="ASYNC_JSON_CONSOLE"/>
            </root>
            <logger name="io.micronaut" level="INFO"/>
            <logger name="io.netty" level="WARN"/>
            <logger name="software.amazon.awssdk" level="WARN"/>
            <logger name="aws.smithy.kotlin.runtime.http" level="WARN"/>
            <!-- This logger seems verbose for production, but retaining as per original config. -->
            <!-- Consider changing to INFO unless you require detailed debugging logs. -->
            <logger name="com.cha" level="DEBUG"/>
        </then>
        <else>
            <!-- Fallback to Local Development configuration -->
            <root level="INFO">
                <appender-ref ref="ASYNC_PRETTY_CONSOLE"/>
            </root>
            <logger name="com.cha" level="TRACE"/>
            <logger name="io.micronaut" level="DEBUG"/>
            <logger name="software.amazon.awssdk" level="DEBUG"/>
        </else>
    </if>

</configuration>
