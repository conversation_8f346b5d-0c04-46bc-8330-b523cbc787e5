package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.*
import com.lowagie.text.pdf.PdfPCell
import com.lowagie.text.pdf.PdfPTable
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

internal val FONT_BODY = Font(Font.HELVETICA, 12f, Font.NORMAL)
internal val FONT_BODY_BOLD = Font(Font.HELVETICA, 12f, Font.BOLD)

internal fun createSectionTitle(title: String): Paragraph {
    val paragraph = Paragraph(title, FONT_BODY_BOLD)
    paragraph.spacingAfter = 10f
    return paragraph
}

internal fun formatDateTime(dateTime: LocalDateTime): String {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    return dateTime.format(formatter)
}

internal fun setupDataItemTable(table: PdfPTable) {
    table.setWidths(floatArrayOf(1f, 3f))
    table.spacingBefore = 10f
    table.spacingAfter = 10f
}

internal fun createDataItemHeaderCell(text: String, colspan: Int): PdfPCell {
    val cell = PdfPCell(Paragraph(text, FONT_BODY_BOLD))
    cell.colspan = colspan
    cell.horizontalAlignment = Element.ALIGN_CENTER
    cell.border = Rectangle.BOX
    return cell
}

internal fun createLabelValueCell(label: String, value: String?, inTable: Boolean): PdfPCell {
    val content = "$label ${value ?: "N/A"}"
    val cell = PdfPCell(Paragraph(content, FONT_BODY))
    cell.border = if (inTable) Rectangle.BOX else Rectangle.NO_BORDER
    return cell
}

internal fun addProceduresSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.procedures?.let { procedures ->
        document.add(createSectionTitle("Procedures"))
        if (procedures.isNotEmpty()) {
            procedures.forEach { procedure ->
                val procedureTable = PdfPTable(2)
                procedureTable.widthPercentage = 100f
                setupDataItemTable(procedureTable)
                val headerText = "${procedure.cptDescription ?: "Procedure"} (${procedure.cptCode ?: "N/A"})"
                procedureTable.addCell(createDataItemHeaderCell(headerText, 2))
                procedureTable.addCell(createLabelValueCell("Caregiver:", procedure.caregiver, inTable = true))
                procedureTable.addCell(
                    createLabelValueCell(
                        "Status:",
                        if (procedure.active == 1) "Active" else "Inactive",
                        inTable = true
                    )
                )
                procedureTable.addCell(createLabelValueCell("Modifier 1:", procedure.modifier1, inTable = true))
                procedureTable.addCell(createLabelValueCell("Modifier 2:", procedure.modifier2, inTable = true))
                procedureTable.addCell(createLabelValueCell("Modifier 3:", procedure.modifier3, inTable = true))
                procedureTable.addCell(createLabelValueCell("Modifier 4:", procedure.modifier4, inTable = true))
                procedureTable.addCell(
                    createLabelValueCell(
                        "Timestamp:",
                        procedure.timestamp?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                document.add(procedureTable)
            }
        } else {
            val paragraph = Paragraph("No procedures recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}