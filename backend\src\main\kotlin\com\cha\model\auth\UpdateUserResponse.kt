package com.cha.model.auth

import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema

@Serdeable
@Schema(name = "UpdateUserResponse", description = "User update response")
data class UpdateUserResponse(
    @Schema(description = "Username of the updated user", example = "johndoe")
    val username: String,
    @Schema(description = "Message indicating success", example = "User updated successfully")
    val message: String,
)
