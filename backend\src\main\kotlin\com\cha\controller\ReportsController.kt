package com.cha.controller

import com.cha.domain.JobsService
import com.cha.model.auth.toUser
import com.cha.model.dto.Job
import com.cha.model.dto.JobStatus
import com.cha.model.dto.ReportRequest
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.data.model.Pageable
import io.micronaut.data.model.Slice
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.exceptions.HttpStatusException
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.rules.SecurityRule
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag

private val log = KotlinLogging.logger { }

@Secured(SecurityRule.IS_AUTHENTICATED)
@Controller("/v1/reports")
@Tag(name = "Reports", description = "Operations related to generating and retrieving reports")
class ReportsController(
    private val jobsService: JobsService,
) {

    /**
     * Generates a report asynchronously and returns a job that can be polled
     * for status.
     */
    @Secured(SecurityRule.IS_AUTHENTICATED)
    @Post("/generate")
    @Operation(
        summary = "Generate a report",
        description = "Generates a report asynchronously and returns a job that can be polled for status.",
        responses = [
            ApiResponse(
                responseCode = "202",
                description = "Report generation accepted, job created.",
                content = [Content(mediaType = "application/json", schema = Schema(implementation = Job::class))]
            ),
            ApiResponse(responseCode = "400", description = "Invalid request payload."),
            ApiResponse(responseCode = "401", description = "Unauthorized.")
        ]
    )
    suspend fun generateReport(
        @Body request: ReportRequest,
        authentication: Authentication,
    ): HttpResponse<Job> {
        log.info { "Generating ${request.reportType} report for EMPI ${request.empi} for user ${authentication.attributes["sub"]}" }

        // Create a job for the report generation
        val job = jobsService.createJob(request, authentication)

        return HttpResponse.accepted<Job>().body(job)
    }

    /** Retrieves a job by its ID for the authenticated user. */
    @Get("/jobs/{jobId}")
    @Operation(
        summary = "Get job status",
        description = "Retrieves a job by its ID for the authenticated user.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Job details.",
                content = [Content(mediaType = "application/json", schema = Schema(implementation = Job::class))]
            ),
            ApiResponse(responseCode = "401", description = "Unauthorized."),
            ApiResponse(responseCode = "404", description = "Job not found.")
        ]
    )
    suspend fun getJob(
        @Parameter(description = "ID of the job to retrieve") @PathVariable jobId: String,
        authentication: Authentication,
    ): HttpResponse<Job> {
        val user = authentication.toUser()
        log.debug { "Retrieving job $jobId for user ${user.id}" }

        val job = jobsService.getJob(jobId, user.id) ?: throw HttpStatusException(
            HttpStatus.NOT_FOUND,
            "Job not found: $jobId"
        )

        return HttpResponse.ok(job)
    }

    /** Retrieves all jobs for the authenticated user. */
    @Get("/jobs")
    @Operation(
        summary = "Get all jobs",
        description = "Retrieves all jobs for the authenticated user, with cursor-based pagination for endless scrolling.",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "A slice of jobs for the user.",
//                content = [Content(mediaType = "application/json", schema = Schema(implementation = Slice::class))]
            ),
            ApiResponse(responseCode = "401", description = "Unauthorized.")
        ]
    )
    suspend fun getAllJobs(
        authentication: Authentication,
        @Parameter(
            description = """
            Cursor-based pageable object for endless scrolling. For the first page, do not supply any parameters. For subsequent pages, use the `pageable` query parameters from the `nextPageable` object in the previous response.
            """,
//            schema = Schema(implementation = Pageable::class),
            required = false
        ) pageable: Pageable,
    ): Slice<Job> {
        val user = authentication.toUser()
        log.debug { "Retrieving all jobs for user ${user.id}" }

        val jobs: Slice<Job> = jobsService.getAllJobs(user.id, pageable)
        return jobs
    }

    @Get("/jobs/{jobId}/cancel")
    @Operation(
        summary = "Cancel a job",
        description = """
           Requests cancellation of a job by its ID. Note: Actual cancellation logic might be asynchronous or further implementation dependent. 
        """,
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Job cancellation processed or current job state returned.",
                content = [Content(mediaType = "application/json", schema = Schema(implementation = Job::class))]
            ),
            ApiResponse(
                responseCode = "400",
                description = "Bad Request. Job cannot be cancelled (e.g., already completed or failed)."
            ),
            ApiResponse(responseCode = "401", description = "Unauthorized."),
            ApiResponse(responseCode = "404", description = "Job not found.")
        ]
    )
    suspend fun cancelJob(
        @Parameter(description = "ID of the job to cancel") @PathVariable jobId: String,
        authentication: Authentication,
    ): HttpResponse<Job> {
        val user = authentication.toUser()
        log.debug { "Cancelling job $jobId for user ${user.id}" }

        // For now, returning a placeholder. Replace with actual cancelled job.
        val job = jobsService.getJob(jobId, user.id) ?: throw HttpStatusException(
            HttpStatus.NOT_FOUND,
            "Job not found: $jobId"
        )
        if (job.status == JobStatus.PROCESSING || job.status == JobStatus.QUEUED) {
            // Add actual cancellation logic in JobsService
            // For now, we'll just return the job as if cancellation was requested.
            // In a real scenario, you'd update the job status to a CANCELLED or CANCELLING state.
            log.info { "Cancellation requested for job $jobId by user ${user.id}" }
            // TODO: STUB for cancelling a report generation job
        } else {
            throw HttpStatusException(HttpStatus.BAD_REQUEST, "Job cannot be cancelled as it is already ${job.status}")
        }
        // Placeholder response until cancellation is fully implemented
        return HttpResponse.ok(job) // Return the current job state or an updated one after cancellation
    }
}
