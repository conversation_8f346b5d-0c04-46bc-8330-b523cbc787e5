package com.cha.model.dto

import com.cha.model.entity.TelephoneEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema

@Serdeable
@Schema(description = "Types of telephone numbers", enumAsRef = true)
enum class TelephoneType {
    @Schema(description = "Mobile/Cell phone number")
    MOBILE,

    @Schema(description = "Home/Residential phone number")
    HOME,

    @Schema(description = "Work/Business phone number")
    WORK,

    @Schema(description = "Emergency contact number")
    EMERGENCY,

    @Schema(description = "Other/Unspecified phone number type")
    OTHER
}

@Serdeable
data class Telephone(
    @Schema(description = "Telephone number", example = "************")
    val number: String?,
    @Schema(description = "Type of telephone", example = "MOBILE")
    val type: TelephoneType?,
    @Schema(description = "System ID the telephone belongs to", example = "3")
    val systemId: Int?,
)

/**
 * Maps a string telephone type from the database to a TelephoneType enum
 * value. If the string cannot be mapped to a known type, defaults to
 * [TelephoneType.OTHER].
 *
 * @param typeString The telephone type string from the database
 * @return The corresponding TelephoneType enum value
 */
fun mapToTelephoneType(typeString: String?): TelephoneType? {
    if (typeString == null) return null

    return when (typeString.uppercase()) {
        "MOBILE", "MOBIL" -> TelephoneType.MOBILE
        "HOME" -> TelephoneType.HOME
        "WORK" -> TelephoneType.WORK
        "EMERGENCY" -> TelephoneType.EMERGENCY
        else -> TelephoneType.OTHER
    }
}

/**
 * Map a PatientTelephoneEntity to a Telephone DTO. Uses the
 * mapToTelephoneType function to convert string types to enum values.
 */
fun TelephoneEntity.toDto(): Telephone {
    return Telephone(
        number = this.telephone,
        type = mapToTelephoneType(this.type),
        systemId = this.systemId
    )
}
