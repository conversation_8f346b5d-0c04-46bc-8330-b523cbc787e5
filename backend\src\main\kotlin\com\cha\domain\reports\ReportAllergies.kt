package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable

private fun addAllergiesSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.allergies?.let { allergies ->
        document.add(createSectionTitle("Allergies & Adverse Reactions"))
        if (allergies.isNotEmpty()) {
            allergies.forEach { allergy ->
                val allergyTable = PdfPTable(2)
                allergyTable.widthPercentage = 100f
                setupDataItemTable(allergyTable)

                val headerText = "${allergy.allergyDescription} (${allergy.allergyCode ?: "N/A"})"
                allergyTable.addCell(createDataItemHeaderCell(headerText, 2))

                allergyTable.addCell(createLabelValueCell("Reaction:", allergy.reaction, inTable = true))
                allergyTable.addCell(createLabelValueCell("Severity:", allergy.severity, inTable = true))
                allergyTable.addCell(createLabelValueCell("Type:", allergy.allergyType, inTable = true))
                allergyTable.addCell(createLabelValueCell("System:", allergy.systemName, inTable = true))
                allergyTable.addCell(
                    createLabelValueCell(
                        "Start Date:",
                        allergy.allergyStartDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                allergyTable.addCell(
                    createLabelValueCell(
                        "End Date:",
                        allergy.allergyEndDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )

                document.add(allergyTable)
            }
        } else {
            val paragraph = Paragraph("No allergies or adverse reactions recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}