package com.cha.domain

import com.cha.model.dto.ClearPatientHistoryResponse
import com.cha.model.dto.UserPatientHistory
import com.cha.model.entity.UserPatientHistoryEntity
import com.cha.repository.UserPatientHistoryRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.inject.Singleton
import java.time.LocalDateTime
import java.time.ZoneOffset

private val logger = KotlinLogging.logger { }

@Singleton
class UserService(
    private val userPatientHistoryRepository: UserPatientHistoryRepository,
    private val patientLookupService: PatientLookupService,
) {
    suspend fun getUserPatientHistory(userId: String): List<UserPatientHistory> {
        logger.debug { "Fetching patient history for user: $userId" }

        val entityList = userPatientHistoryRepository.findByUserIdOrderByViewedAtDesc(userId)

        return entityList.map { entity ->
            UserPatientHistory(
                id = entity.id!!,
                viewedAt = entity.viewedAt,
                patient = patientLookupService.getPatient(userId, entity.empi.toInt()),
            )
        }
    }

    suspend fun clearUserPatientHistory(userId: String): ClearPatientHistoryResponse {
        logger.info { "Clearing patient history for user: $userId" }

        val deletedCount = userPatientHistoryRepository.deleteByUserId(userId)

        logger.info { "Cleared $deletedCount patient history entries for user: $userId" }

        return ClearPatientHistoryResponse(
            message = "Patient history cleared successfully",
            deletedCount = deletedCount
        )
    }

    suspend fun addPatientHistoryEntry(userId: String, empi: String) {
        logger.debug { "Adding patient history entry for user: $userId, empi: $empi" }

        val historyList = userPatientHistoryRepository.findByUserIdOrderByViewedAtDesc(userId)
        val existing = historyList.find { it.empi == empi }
        if (existing != null) {
            logger.info { "Patient history entry already exists for user: $userId, empi: $empi. Updating viewedAt." }
            val updatedEntity = existing.copy(viewedAt = LocalDateTime.now(ZoneOffset.UTC))
            userPatientHistoryRepository.update(updatedEntity)
            return
        }

        // Enforce max 20 history items per user
        if (historyList.size >= 20) {
            // Find the oldest (last in the list, since it's ordered desc)
            val oldest = historyList.last()
            userPatientHistoryRepository.deleteById(oldest.id!!)
        }

        val entity = UserPatientHistoryEntity(
            userId = userId,
            empi = empi,
        )

        userPatientHistoryRepository.save(entity)
    }
}