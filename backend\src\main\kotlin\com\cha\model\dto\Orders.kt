package com.cha.model.dto

import com.cha.model.entity.OrderEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a clinical order for a patient, such as lab, medication, or procedure orders. Includes order details and relevant dates."
)
data class Orders(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int?,
    @Schema(description = "System identifier for the order record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the order record.")
    val accountNumber: String?,
    val orderNumber: Int?,
    val orderedBy: String?,
    val verifiedBy: String?,
    val discontinuedBy: String?,
    val caregiver: String?,
    val frequency: String?,
    val enteredDatetime: String?,
    val priority: String?,
    val specialInstructions1: String?,
    val specialInstructions2: String?,
    val specialInstructions3: String?,
    val orderStatusCode: String?,
    val orderCode: String?,
    val orderCodeDescription: String?,
    val collectedDatetime: LocalDateTime?,
    val collectedBy: String?,
    val serviceDatetime: LocalDateTime?,
    val receivedDatetime: LocalDateTime?,
    val releasedDatetime: LocalDateTime?,
    val releasedBy: String?,
    val accessionNumber: String?,
    val orderTypeId: Int?,
    val orderTypeDescription: String?,
    val orderArea: String?,
    val systemName: String?,
    @Schema(description = "Indicates if the order record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean?,
    @Schema(description = "Admission date associated with the order record, if relevant.")
    val admitDate: LocalDateTime?
)

fun OrderEntity.toDto(): Orders {
    return Orders(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        orderCode = this.orderCode,
        systemName = this.systemName,
        securityLevel = this.securityLevel,
        admitDate = this.admitDate,
        orderNumber = this.orderNumber,
        orderedBy = this.orderedBy,
        verifiedBy = this.verifiedBy,
        discontinuedBy = this.discontinuedBy,
        caregiver = this.caregiver,
        frequency = this.frequency,
        enteredDatetime = this.enteredDatetime,
        priority = this.priority,
        specialInstructions1 = this.specialInstructions1,
        specialInstructions2 = this.specialInstructions2,
        specialInstructions3 = this.specialInstructions3,
        orderStatusCode = this.orderStatusCode,
        orderCodeDescription = this.orderCodeDescription,
        collectedDatetime = this.collectedDatetime,
        collectedBy = this.collectedBy,
        serviceDatetime = this.serviceDatetime,
        receivedDatetime = this.receivedDatetime,
        releasedDatetime = this.releasedDatetime,
        releasedBy = this.releasedBy,
        accessionNumber = this.accessionNumber,
        orderTypeId = this.orderTypeId,
        orderTypeDescription = this.orderTypeDescription,
        orderArea = this.orderArea
    )
}
