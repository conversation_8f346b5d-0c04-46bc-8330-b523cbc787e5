# Test environment configuration for CI/CD builds
# This profile provides fallback values that don't require AWS services
micronaut:
  application:
    name: viewer-backend-test # Ensure a different name for test app context
  config-client:
    enabled: false  # Disable distributed configuration for tests
  metrics:
    export:
      cloudwatch:
        enabled: false  # Disable CloudWatch exports for CI/CD

aws:
  secretsmanager:
    enabled: false  # Disable AWS Secrets Manager for test builds
  client:
    system-manager:
      parameterstore:
        enabled: false  # Disable Parameter Store for test builds
  distributed-configuration:
    enabled: false  # Disable distributed configuration

# Disable datasources for tests
datasources:
  default:
    enabled: false
  lrh:
    enabled: false
  hhs:
    enabled: false

liquibase:
  enabled: false

# Provide fallback database configuration for build-time (not used when disabled)
database:
  host: localhost
  port: 1433
  username: sa
  password: test
