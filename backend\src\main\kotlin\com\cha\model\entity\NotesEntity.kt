package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("notes")
data class NotesEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val noteDatetime: LocalDateTime?,
    val privateNoteFlag: String?,
    val noteText: String?,
    val systemName: String?,
    val securityLevel: Boolean? = false,
    val admitDate: LocalDateTime?
)
