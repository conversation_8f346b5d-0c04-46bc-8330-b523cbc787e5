# Use an official Eclipse Temurin JRE image as a parent image
# Using Jammy for better compatibility and smaller size than default
FROM eclipse-temurin:22-jre-jammy

ARG APP_VERSION="unknown"

# Add common labels
LABEL maintainer="<EMAIL>" \
      description="Micronaut application that runs the viewer backend" \
      version="${APP_VERSION}"

# Set the working directory in the container
WORKDIR /app

# Create a non-root user and group for security
RUN groupadd --system appgroup && useradd --system --gid appgroup appuser

COPY backend/build/libs/backend-${APP_VERSION}-all.jar application.jar

# Ensure application.jar is owned by appuser before switching.
RUN chown appuser:appgroup application.jar

# Change ownership to the non-root user
RUN chown -R appuser:appgroup /app

# Switch to the non-root user
USER appuser

# Expose the port the application runs on (Micronaut default is 8080)
EXPOSE 8080

# Command to run the application
ENTRYPOINT ["java", "-jar", "/app/application.jar"]
