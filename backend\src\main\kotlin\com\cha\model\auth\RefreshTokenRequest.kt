package com.cha.model.auth

import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema

/**
 * Request object for refreshing an access token using a refresh token.
 *
 * @property refreshToken The refresh token used to obtain a new access
 *    token
 */
@Serdeable
@Schema(name = "RefreshTokenRequest", description = "Request to refresh an access token")
data class RefreshTokenRequest(
    @Schema(
        description = "The refresh token used to obtain a new access token",
        example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    )
    @JsonProperty("refresh_token")
    val refreshToken: String
)
