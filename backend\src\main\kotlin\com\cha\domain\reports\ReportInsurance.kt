package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable

private fun addInsuranceSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.financial?.insurance?.let { insurances ->
        document.add(createSectionTitle("Insurance"))
        if (insurances.isNotEmpty()) {
            insurances.forEach { insurance ->
                val insuranceTable = PdfPTable(2)
                insuranceTable.widthPercentage = 100f
                setupDataItemTable(insuranceTable)

                val headerText = "${insurance.organizationName} (${insurance.insurancePlan})"
                insuranceTable.addCell(createDataItemHeaderCell(headerText, 2))


                insuranceTable.addCell(
                    createLabelValueCell(
                        "Group Name:",
                        insurance.groupName,
                        inTable = true
                    )
                )
                insuranceTable.addCell(createLabelValueCell("Group Number:", insurance.groupNumber, inTable = true))
                insuranceTable.addCell(
                    createLabelValueCell(
                        "Subscriber:",
                        insurance.subscriberNumber,
                        inTable = true
                    )
                )
                insuranceTable.addCell(
                    createLabelValueCell(
                        "Policy Number:",
                        insurance.policyNumber,
                        inTable = true
                    )
                )
                insuranceTable.addCell(
                    createLabelValueCell(
                        "Policy Holder:",
                        insurance.policyHolder,
                        inTable = true
                    )
                )
                insuranceTable.addCell(
                    createLabelValueCell(
                        "Policy Holder SSN:",
                        insurance.policyHolderSsn,
                        inTable = true
                    )
                )
                insuranceTable.addCell(
                    createLabelValueCell(
                        "Certificate #:",
                        insurance.certificateNo,
                        inTable = true
                    )
                )
                insuranceTable.addCell(createLabelValueCell("Sequence:", insurance.insSeq, inTable = true))


                document.add(insuranceTable)
            }
        } else {
            val paragraph = Paragraph("No insurance information recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}