package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("micro_comments")
data class MicroCommentEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val accessionNumber: String?,
    val accessionIsolate: String?,
    val isolateNumber: Int?,
    val commentType: String?,
    val sequenceNumber: Int?,
    val commentDescription: String?,
    val enteredDate: LocalDateTime?,
    val enteredBy: String?,
    val systemName: String?,
    val securityLevel: Boolean? = false,
    val admitDate: LocalDateTime?
)
