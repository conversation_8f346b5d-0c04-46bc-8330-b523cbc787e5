package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.*
import com.lowagie.text.pdf.PdfPCell
import com.lowagie.text.pdf.PdfPTable
import java.awt.Color
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

private val FONT_BODY = Font(Font.HELVETICA, 12f, Font.NORMAL)
private val FONT_BODY_BOLD = Font(Font.HELVETICA, 12f, Font.BOLD)
private val COLOR_BACKGROUND_MUTED = Color(240, 240, 240) // Light gray
private val COLOR_BORDER = Color(0, 0, 0) // Black

private fun addLabResultsSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.labResults?.let { labResults ->
        document.add(createSectionTitle("Lab Results"))
        if (labResults.isNotEmpty()) {
            labResults.distinctBy {
                it.accession to it.orderCode
            }.forEach { lab ->
                val sameAccession = labResults.filter {
                    it.accession == lab.accession &&
                            it.orderCode == lab.orderCode
                }

                val title1 = "Accession: ${lab.accession} Order Code: ${lab.orderCode} Order Description: ${lab.orderDescription}"
                val title2 = "Service Date: ${lab.serviceDatetime?.let { dateTime: LocalDateTime -> formatDateTime(dateTime) }} Collected Date: ${lab.collectedDatetime?.let { dateTime: LocalDateTime ->
                    formatDateTime(
                        dateTime
                    )
                }} Released Date: ${lab.releasedDatetime?.let { dateTime: LocalDateTime -> formatDateTime(dateTime) }}"
                document.add(Paragraph(title1, FONT_BODY))
                document.add(Paragraph(title2, FONT_BODY))
                document.add(Paragraph(" ", FONT_BODY)) // Add a blank line for spacing

                val labTable = PdfPTable(7)
                labTable.widthPercentage = 100f
                labTable.setSpacingAfter(15f)

                // Header row
                labTable.addCell(createTableHeaderCell("ABBR"))
                labTable.addCell(createTableHeaderCell("Description"))
                labTable.addCell(createTableHeaderCell("Result"))
                labTable.addCell(createTableHeaderCell("Units"))
                labTable.addCell(createTableHeaderCell("Flag"))
                labTable.addCell(createTableHeaderCell("Abn Flag"))
                labTable.addCell(createTableHeaderCell("Released By"))

                // Data rows
                sameAccession.forEach { labResult ->
                    labTable.addCell(createTableDataCell(labResult.testAbbr ?: ""))
                    labTable.addCell(createTableDataCell(labResult.testDescription ?: ""))
                    labTable.addCell(createTableDataCell(labResult.numericLabResult ?: ""))
                    labTable.addCell(createTableDataCell(labResult.units ?: ""))
                    labTable.addCell(createTableDataCell(labResult.resultFlag ?: ""))
                    labTable.addCell(createTableDataCell(labResult.abnFlag ?: ""))
                    labTable.addCell(createTableDataCell(labResult.releasedBy ?: ""))
                }

                document.add(labTable)
            }
        } else {
            val paragraph = Paragraph("No lab results recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}

private fun createSectionTitle(title: String): Paragraph {
    val paragraph = Paragraph(title, FONT_BODY)
    paragraph.spacingAfter = 10f
    return paragraph
}

private fun createTableHeaderCell(text: String): PdfPCell {
    val cell = PdfPCell(Paragraph(text, FONT_BODY_BOLD))
    cell.backgroundColor = COLOR_BACKGROUND_MUTED
    cell.border = Rectangle.BOX
    cell.borderColor = COLOR_BORDER
    cell.setPadding(5f)
    cell.horizontalAlignment = Element.ALIGN_CENTER
    return cell
}

private fun createTableDataCell(content: String): PdfPCell {
    val cell = PdfPCell(Paragraph(content, FONT_BODY))
    cell.horizontalAlignment = Element.ALIGN_LEFT
    cell.verticalAlignment = Element.ALIGN_MIDDLE
    return cell
}

private fun formatDateTime(dateTime: LocalDateTime): String {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    return dateTime.format(formatter)
}}