package com.cha.model.dto

import com.cha.model.entity.ProcedureEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a clinical procedure record for a patient, including procedure code, description, and relevant dates."
)
data class Procedure(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int?,
    @Schema(description = "System identifier for the procedure record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the procedure record.")
    val accountNumber: String?,
    @Schema(description = "Procedure code (e.g., CPT, ICD-10-PCS).")
    val cptCode: String?,
    @Schema(description = "Modifier 1 for the procedure code.")
    val modifier1: String?,
    @Schema(description = "Modifier 2 for the procedure code.")
    val modifier2: String?,
    @Schema(description = "Modifier 3 for the procedure code.")
    val modifier3: String?,
    @Schema(description = "Modifier 4 for the procedure code.")
    val modifier4: String?,
    @Schema(description = "Description of the procedure performed.")
    val cptDescription: String?,
    @Schema(description = "Name of the caregiver who performed the procedure.")
    val caregiver: String?,
    @Schema(description = "Date and time when the procedure was performed.")
    val timestamp: LocalDateTime?,
    @Schema(description = "Indicates if the procedure record is active.")
    val active: Int?,
    @Schema(description = "Name of the system/source where the procedure was recorded.")
    val systemName: String?,
    @Schema(description = "Indicates if the procedure record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean?,
    @Schema(description = "Admission date associated with the procedure record, if relevant.")
    val admitDate: LocalDateTime?
)

fun ProcedureEntity.toDto(): Procedure {
    return Procedure(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        cptCode = this.cptCode,
        modifier1 = this.modifier1,
        modifier2 = this.modifier2,
        modifier3 = this.modifier3,
        modifier4 = this.modifier4,
        cptDescription = this.cptDescription,
        caregiver = this.caregiver,
        timestamp = this.timestamp,
        active = this.active,
        systemName = this.systemName,
        securityLevel = this.securityLevel,
        admitDate = this.admitDate
    )
}
