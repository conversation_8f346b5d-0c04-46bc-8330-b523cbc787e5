package com.cha.model.auth

import io.micronaut.security.authentication.Authentication
import java.time.Instant

/**
 * Converts a Micronaut Authentication object into a type-safe User data
 * class. This provides a clean way to work with the authenticated user's
 * details throughout the application.
 *
 * @return A User object populated with claims from the authentication
 *    token.
 * @receiver The Authentication object, typically injected into a
 *    controller.
 */
fun Authentication.toUser(): User {
    val attributes = this.attributes

    // Safely extract roles, which might be a List or a comma-separated String from the JWT claims
    val roles = when (val groups = attributes["cognito:groups"]) {
        is List<*> -> groups.mapNotNull { it?.toString() }
        is String -> groups.split(',').map { it.trim() }.filter { it.isNotEmpty() }
        else -> emptyList()
    }

    // Safely extract 'updated_at' and convert from epoch seconds to Instant
    val updatedAtLong = (attributes["updated_at"] as? Number)?.toLong() ?: 0L

    println("attribute: $attributes")

//    attributes.find { it.name == "given_name" }?.value

    return User(
        id = attributes["sub"]?.toString()
            ?: throw IllegalStateException("Required 'sub' claim is missing from authentication"),
        username = attributes["username"] as? String ?: this.name,
        email = attributes["email"] as? String ?: "",
        emailVerified = attributes["email_verified"] as? Boolean ?: false,
        firstName = attributes["given_name"] as? String ?: "",
        lastName = attributes["family_name"] as? String ?: "",
        pictureUrl = attributes["picture"] as? String ?: "",
        createdAt = Instant.ofEpochSecond((attributes["created_at"] as? Number)?.toLong() ?: 0L),
        updatedAt = Instant.ofEpochSecond(updatedAtLong),
        roles = roles
    )
}
