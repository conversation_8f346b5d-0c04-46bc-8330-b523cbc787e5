package com.cha.model.dto

import com.cha.model.entity.DiagnosisEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a clinical diagnosis record for a patient, including ICD code, description, type, and relevant dates."
)
data class Diagnosis(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int?,
    @Schema(description = "System identifier for the diagnosis record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the diagnosis record.")
    val accountNumber: String?,
    @Schema(description = "ICD code for the diagnosis.")
    val icdCode: String?,
    @Schema(description = "Description of the ICD code.")
    val icdDescription: String?,
    @Schema(description = "Rank or priority of the diagnosis.")
    val rank: Int?,
    @Schema(description = "Type of diagnosis (e.g., admitting, discharge, secondary).")
    val type: String?,
    @Schema(description = "Sequence number for the diagnosis.")
    val sequence: Int?,
    @Schema(description = "Name of the caregiver who recorded the diagnosis.")
    val caregiver: String?,
    @Schema(description = "Date when the procedure or diagnosis was recorded.")
    val procedureDate: LocalDateTime?,
    @Schema(description = "Indicates if the diagnosis is currently active (1) or not (0).")
    val active: Int?,
    @Schema(description = "Admission date associated with the diagnosis record, if relevant.")
    val admitDate: LocalDateTime?,
    @Schema(description = "Name of the system/source where the diagnosis was recorded.")
    val systemName: String?,
    @Schema(description = "Indicates if the diagnosis record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean?
)

fun DiagnosisEntity.toDto(): Diagnosis {
    return Diagnosis(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        icdCode = this.icdCode,
        icdDescription = this.icdDescription,
        rank = this.rank,
        type = this.type,
        sequence = this.sequence,
        caregiver = this.caregiver,
        procedureDate = this.procedureDate,
        active = this.active,
        admitDate = this.admitDate,
        systemName = this.systemName,
        securityLevel = this.securityLevel
    )
}
