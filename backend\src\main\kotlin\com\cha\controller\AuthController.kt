package com.cha.controller

import com.cha.domain.AuthService
import com.cha.domain.exception.AuthorizationException
import com.cha.domain.exception.InvalidRequestException
import com.cha.model.auth.ChangePasswordRequest
import com.cha.model.auth.CreateUserRequest
import com.cha.model.auth.CreateUserResponse
import com.cha.model.auth.ForcePasswordResetRequest
import com.cha.model.auth.LoginRequest
import com.cha.model.auth.LoginResponse
import com.cha.model.auth.NewPasswordRequest
import com.cha.model.auth.RefreshTokenRequest
import com.cha.model.auth.ResendVerificationRequest
import com.cha.model.auth.RespondToChallengeRequest
import com.cha.model.auth.UpdateUserRequest
import com.cha.model.auth.UpdateUserResponse
import com.cha.model.auth.User
import com.cha.model.auth.toUser
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.data.model.CursoredPage
import io.micronaut.data.model.CursoredPageable
import io.micronaut.http.HttpResponse
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Header
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Produces
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.rules.SecurityRule
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.ExampleObject
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.parameters.RequestBody
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.validation.Valid
import org.zalando.problem.Problem

private val logger = KotlinLogging.logger { }

@Controller("/v1/auth")
@Secured(SecurityRule.IS_ANONYMOUS)
@Tag(name = "Authentication", description = "Authentication and authorization endpoints")
open class AuthController(
    private val authService: AuthService,
) {

    @Post("/login")
    @Secured(SecurityRule.IS_ANONYMOUS)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(
        summary = "Authenticate user and obtain JWT token",
        description = "Authenticates a user with their credentials and returns a JWT token for subsequent API calls"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "Authentication successful",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = LoginResponse::class),
                examples = [ExampleObject(
                    name = "Successful Login",
                    value = """
                    {
                        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                        "token_type": "Bearer",
                        "expires_in": 3600,
                        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                    }
                    """
                )]
            )]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Unauthorized - user not authenticated",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "403",
            description = "Forbidden - not an admin user when requesting another user's details",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    open suspend fun login(
        @Body
        @RequestBody(
            description = "User login credentials",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = LoginRequest::class),
                examples = [ExampleObject(
                    name = "Login Example",
                    value = """
                    {
                        "username": "<EMAIL>",
                        "password": "SecurePassword123!"
                    }
                    """
                )]
            )]
        )
        @Valid loginRequest: LoginRequest,
    ): LoginResponse {
        return authService.login(loginRequest)
    }

    /**
     * Logs out a user by invalidating their token with AWS Cognito. This
     * performs a global sign-out which invalidates all tokens issued for this
     * user.
     *
     * @param authentication The authenticated user information (for user
     *    details)
     * @param authorization The Authorization header containing the access
     *    token (for the actual token)
     */
    @Get("/logout")
    @Secured(SecurityRule.IS_AUTHENTICATED)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(
        summary = "Logout user and invalidate tokens",
        description = "Performs a global sign-out which invalidates all tokens issued for this user"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "Logout successful",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                examples = [ExampleObject(
                    name = "Successful Logout",
                    value = """
                    {
                        "message": "User logged out successfully"
                    }
                    """
                )]
            )]
        )
    )
    open suspend fun logout(
        authentication: Authentication,
        @Header("Authorization") authorization: String,
    ): Map<String, String> {
        val username = authentication.name

        // Extract the token from the Authorization header (remove "Bearer " prefix)
        val accessToken = authorization.substringAfter("Bearer ", "")
        if (accessToken.isEmpty()) {
            logger.warn { "Logout attempt with invalid Authorization header for user: $username" }
            throw InvalidRequestException(
                message = "Valid Bearer token is required"
            )
        }

        authService.logout(username, accessToken)
        return mapOf("message" to "Successfully logged out")
    }

    /**
     * Refreshes the access token and ID token using a refresh token.
     *
     * @param refreshTokenRequest The request containing the refresh token
     */
    @Post("/refresh")
    @Secured(SecurityRule.IS_ANONYMOUS)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(
        summary = "Refresh access token",
        description = "Exchanges a refresh token for new access and ID tokens"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "Token refresh successful",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = LoginResponse::class),
                examples = [ExampleObject(
                    name = "Successful Refresh",
                    value = """
                    {
                        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                        "token_type": "Bearer",
                        "expires_in": 3600,
                        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                    }
                    """
                )]
            )]
        ),
        ApiResponse(
            responseCode = "400",
            description = "Invalid refresh token or bad request",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class),
                examples = [ExampleObject(
                    name = "Invalid Refresh Token",
                    value = """
                    {
                        "type": "https://api.viewer.com/problems/invalid-request",
                        "title": "Invalid Request",
                        "status": 400,
                        "detail": "Invalid or expired refresh token",
                        "traceId": "abc123def456"
                    }
                    """
                )]
            )]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    open suspend fun refreshToken(
        @Body
        @RequestBody(
            description = "Refresh token request",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = RefreshTokenRequest::class),
                examples = [ExampleObject(
                    name = "Refresh Token Example",
                    value = """
                    {
                        "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                    }
                    """
                )]
            )]
        )
        refreshTokenRequest: RefreshTokenRequest,
    ): LoginResponse {
        return authService.refreshToken(refreshTokenRequest.refreshToken)
    }

    @Operation(
        summary = "Create a new user",
        description = "Creates a new user account (admin only)"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "201",
            description = "User created successfully",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = CreateUserResponse::class),
                examples = [ExampleObject(
                    name = "Successful Creation",
                    value = """
                    {
                        "userId": "abc123-def456",
                        "username": "johndoe",
                        "email": "<EMAIL>",
                        "createdAt": "2023-01-15T10:30:00Z",
                        "roles": ["ROLE_USER", "ROLE_ADMIN"]
                    }
                    """
                )]
            )]
        ),
        ApiResponse(
            responseCode = "400",
            description = "Invalid user data",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Unauthorized - user not authenticated",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "403",
            description = "Forbidden - not an admin user",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Post("/user")
    @Secured("ADMIN") // Only administrators can create new users
    @Produces(MediaType.APPLICATION_JSON)
    open suspend fun createUser(
        @Body
        @RequestBody(
            description = "User creation request data",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = CreateUserRequest::class)
            )]
        )
        @Valid createUserRequest: CreateUserRequest,
    ): HttpResponse<CreateUserResponse> {
        return HttpResponse.created(authService.createUser(createUserRequest))
    }

    @Operation(
        summary = "Delete a user",
        description = "Deletes an existing user account (admin only)"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "User deleted successfully",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(
                    type = "object"
                ),
                examples = [ExampleObject(
                    name = "Successful Deletion",
                    value = """
                    {
                        "message": "User successfully deleted"
                    }
                    """
                )]
            )]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Unauthorized - user not authenticated",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "403",
            description = "Forbidden - not an admin user",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Delete("/users/{username}")
    @Secured("ADMIN") // Only administrators can delete users
    @Produces(MediaType.APPLICATION_JSON)
    open suspend fun deleteUser(
        @PathVariable @Parameter(description = "Username of the user to delete", required = true)
        username: String,
    ): Map<String, String> {
        authService.deleteUser(username)
        return mapOf("message" to "User successfully deleted")
    }

    @Get("/users")
    @Secured("ADMIN") // Only administrators can view users
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(
        summary = "List users",
        description = "Retrieves a paginated list of users (admin only)"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "Users retrieved successfully",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = CursoredPage::class)
            )]
        ),
        ApiResponse(
            responseCode = "403",
            description = "Unauthorized - not an admin user",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    suspend fun getUsers(
        @Parameter(
            required = false,
            description = "Pagination information (optional)"
        ) pageable: CursoredPageable = CursoredPageable.from(20, null),
    ): CursoredPage<User> {
        return authService.getUsers(pageable)
    }

    /**
     * Get details of the authenticated user or a specific user (admin only).
     *
     * @param authentication The authenticated user information
     * @param userId Optional user ID to get details for (admin only)
     */
    @Operation(
        summary = "Get user details",
        description = "Retrieves details for the current user or a specific user (admin only)"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "User details retrieved successfully",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = User::class)
            )]
        ),
        ApiResponse(
            responseCode = "400",
            description = "Invalid request - validation failed",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Authentication failed - invalid credentials",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Get("/user{/userId}")
    @Secured(SecurityRule.IS_AUTHENTICATED)
    @Produces(MediaType.APPLICATION_JSON)
    open suspend fun getUser(
        authentication: Authentication,
        @Parameter(description = "User ID to retrieve details for (optional, admin only)")
        @PathVariable userId: String? = null,
    ): User {
        val targetUserId = userId ?: authentication.toUser().username

        // If requesting another user's details, check if user is admin
        if (userId != null && userId != authentication.name) {
            val roles = authentication.roles
            if (!roles.contains("ADMIN")) {
                logger.warn { "Non-admin user ${authentication.name} attempted to access user details for $userId" }
                throw AuthorizationException(
                    requiredRole = "ADMIN",
                    resourceType = "User",
                    resourceId = userId,
                    message = "You do not have permission to access this user's details"
                )
            }
        }

        return authService.getUser(targetUserId)
    }

    /**
     * Update the authenticated user's details.
     *
     * @param authentication The authenticated user information
     * @param updateUserRequest The request containing updated user details
     */
    @Operation(
        summary = "Update user details",
        description = "Updates the current authenticated user's details"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "User updated successfully",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = UpdateUserResponse::class)
            )]
        ),
        ApiResponse(
            responseCode = "400",
            description = "Invalid user data",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Unauthorized - user not authenticated",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Put("/user")
    @Secured(SecurityRule.IS_AUTHENTICATED)
    @Produces(MediaType.APPLICATION_JSON)
    open suspend fun updateUser(
        authentication: Authentication,
        @Body
        @RequestBody(
            description = "User update request data",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = UpdateUserRequest::class)
            )]
        )
        @Valid updateUserRequest: UpdateUserRequest,
    ): UpdateUserResponse {
        val username = authentication.name
        return authService.updateUser(username, updateUserRequest)
    }

    /**
     * Handle new password requests for AWS Cognito admin workflow.
     *
     * @param newPasswordRequest The request containing new password details
     *
     * TODO: determine what usecases this endpoint is actually needed for.
     */
    @Operation(
        summary = "Set new password",
        description = "Handles setting a new password as part of the AWS Cognito admin workflow"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "Password set successfully",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(
                    type = "object"
                ),
                examples = [ExampleObject(
                    name = "Successful Password Set",
                    value = """
                    {
                        "message": "Password set successfully"
                    }
                    """
                )]
            )]
        ),
        ApiResponse(
            responseCode = "400",
            description = "Invalid request or password does not meet requirements",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Post("/new-password")
    @Secured(SecurityRule.IS_ANONYMOUS)
    @Produces(MediaType.APPLICATION_JSON)
    open suspend fun handleNewPassword(
        @Body
        @RequestBody(
            description = "New password request data",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = NewPasswordRequest::class)
            )]
        )
        @Valid newPasswordRequest: NewPasswordRequest,
    ): Map<String, String> {
        authService.handleNewPassword(newPasswordRequest)
        return mapOf("message" to "Password updated successfully")
    }

    /**
     * Force a password reset for a user (admin only).
     *
     * @param forcePasswordResetRequest The request containing user details
     */
    @Operation(
        summary = "Force password reset",
        description = "Forces a password reset for a specific user (admin only)"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "Password reset initiated successfully",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(
                    type = "object"
                ),
                examples = [ExampleObject(
                    name = "Successful Password Reset",
                    value = """
                    {
                        "message": "Password reset initiated for user"
                    }
                    """
                )]
            )]
        ),
        ApiResponse(
            responseCode = "400",
            description = "Invalid user data or user not found",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Unauthorized - user not authenticated",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "403",
            description = "Forbidden - not an admin user",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Post("/force-password-reset")
    @Secured("ADMIN") // Only administrators can force password resets
    @Produces(MediaType.APPLICATION_JSON)
    open suspend fun forcePasswordReset(
        @Body
        @RequestBody(
            description = "Force password reset request data",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = ForcePasswordResetRequest::class)
            )]
        )
        @Valid forcePasswordResetRequest: ForcePasswordResetRequest,
    ): Map<String, String> {
        authService.forcePasswordReset(forcePasswordResetRequest)
        return mapOf(
            "message" to "Password reset initiated successfully. User will receive a " +
                    "verification code via email to reset their password."
        )
    }

    /**
     * Respond to authentication challenges.
     *
     * @param challengeRequest The request containing challenge response
     *    details
     */
    @Operation(
        summary = "Respond to authentication challenge",
        description = "Handles responses to AWS Cognito authentication challenges such as MFA verification"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "Challenge response successful",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = LoginResponse::class),
                examples = [ExampleObject(
                    name = "Successful Challenge Response",
                    value = """
                    {
                        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                        "token_type": "Bearer",
                        "expires_in": 3600,
                        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                        "user": {
                            "id": "abc123",
                            "username": "johndoe",
                            "email": "<EMAIL>",
                            "firstName": "John",
                            "lastName": "Doe"
                        }
                    }
                    """
                )]
            )]
        ),
        ApiResponse(
            responseCode = "400",
            description = "Invalid challenge response",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Post("/respond-to-challenge")
    @Secured(SecurityRule.IS_ANONYMOUS)
    @Produces(MediaType.APPLICATION_JSON)
    open suspend fun respondToChallenge(
        @Body
        @RequestBody(
            description = "Challenge response data",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = RespondToChallengeRequest::class)
            )]
        )
        @Valid challengeRequest: RespondToChallengeRequest,
    ): LoginResponse {
        return authService.respondToChallenge(challengeRequest)
    }

    /**
     * Change password for the authenticated user.
     *
     * @param authentication The authenticated user information
     * @param authorization The Authorization header containing the access
     *    token
     * @param changePasswordRequest The request containing old and new
     *    passwords
     */
    @Operation(
        summary = "Change user password",
        description = "Changes password for the currently authenticated user"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "Password changed successfully",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(
                    type = "object"
                ),
                examples = [ExampleObject(
                    name = "Successful Password Change",
                    value = """
                    {
                        "message": "Password changed successfully"
                    }
                    """
                )]
            )]
        ),
        ApiResponse(
            responseCode = "400",
            description = "Invalid password or request data",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Unauthorized - user not authenticated",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "400",
            description = "Invalid request - validation failed",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Post("/change-password")
    @Secured(SecurityRule.IS_AUTHENTICATED)
    @Produces(MediaType.APPLICATION_JSON)
    open suspend fun changePassword(
        authentication: Authentication,
        @Parameter(description = "JWT Bearer token for authorization") @Header("Authorization") authorization: String,
        @Body
        @RequestBody(
            description = "Password change request data",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = ChangePasswordRequest::class)
            )]
        )
        @Valid changePasswordRequest: ChangePasswordRequest,
    ): Map<String, String> {
        val username = authentication.name

        // Extract the token from the Authorization header
        val accessToken = authorization.substringAfter("Bearer ", "")
        if (accessToken.isEmpty()) {
            logger.warn { "Change password attempt with invalid Authorization header for user: $username" }
            throw InvalidRequestException(
                message = "Valid Bearer token is required"
            )
        }

        authService.changePassword(username, accessToken, changePasswordRequest)
        return mapOf("message" to "Password changed successfully")
    }

    /**
     * Resend email verification.
     *
     * @param resendVerificationRequest The request containing email details
     */
    @Operation(
        summary = "Resend verification email",
        description = "Resends the email verification link to the user's email address"
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "Verification email sent successfully",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(
                    type = "object"
                ),
                examples = [ExampleObject(
                    name = "Successful Email Resend",
                    value = """
                    {
                        "message": "Verification email sent successfully"
                    }
                    """
                )]
            )]
        ),
        ApiResponse(
            responseCode = "400",
            description = "Invalid email or user not found",
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Post("/resend-verification")
    @Secured(SecurityRule.IS_ANONYMOUS) // Allow anonymous access for verification emails
    @Produces(MediaType.APPLICATION_JSON)
    open suspend fun resendEmailVerification(
        @Body
        @RequestBody(
            description = "Email verification resend request data",
            required = true,
            content = [Content(
                mediaType = MediaType.APPLICATION_JSON,
                schema = Schema(implementation = ResendVerificationRequest::class)
            )]
        )
        @Valid resendVerificationRequest: ResendVerificationRequest,
    ): Map<String, String> {
        authService.resendEmailVerification(resendVerificationRequest)
        return mapOf("message" to "Verification email sent successfully")
    }
}
