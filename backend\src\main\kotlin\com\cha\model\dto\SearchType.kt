package com.cha.model.dto

import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema

@Serdeable
@Schema(description = "The type of search to perform for a patient.")
enum class SearchType(val value: String) {
    @Schema(description = "Search by patient name.")
    NAME("name"),

    @Schema(description = "Search by patient MRN (Medical Record Number).")
    MRN("mrn"),

    @Schema(description = "Search by patient birthdate.")
    DOB("dob")
}
