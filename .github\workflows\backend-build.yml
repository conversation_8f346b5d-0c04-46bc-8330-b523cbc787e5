name: Backend Build Verification

on:
  pull_request:
    types: [ opened, synchronize, ready_for_review ]
    branches:
      - main
      - develop

jobs:
  build:
    runs-on: ubuntu-latest
    
    # Grant minimal permissions needed for the workflow
    permissions:
      contents: read
      pull-requests: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: corretto

      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Make Gradle wrapper executable
        run: chmod +x ./gradlew

      # TODO: Temporarily disabled due to failing issues - re-enable after fixes
      # - name: Run Detekt static analysis
      #   run: ./gradlew detekt --no-daemon

      - name: Build backend application
        run: ./gradlew :backend:build --no-daemon
        env:
          MICRONAUT_ENVIRONMENTS: test

      - name: Upload build reports
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: build-reports
          path: |
            backend/build/reports/
            infrastructure/build/reports/
          retention-days: 7
    # TODO - also verify the cdk code builds.
