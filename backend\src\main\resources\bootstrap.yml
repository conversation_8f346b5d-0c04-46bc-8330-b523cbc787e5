# https://docs.micronaut.io/latest/guide/#bootstrap
micronaut:
  application:
    name: viewer-backend
  config-client:
    enabled: true

aws:
  region: us-east-1
  secretsmanager:
    enabled: true
    secrets:
      - secret-name: rds
        prefix: database
  client:
    system-manager:
      parameterstore:
        enabled: true
  distributed-configuration:
    search-active-environments: false
