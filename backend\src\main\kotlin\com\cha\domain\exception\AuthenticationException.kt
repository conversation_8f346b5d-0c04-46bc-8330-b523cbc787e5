package com.cha.domain.exception

/**
 * Exception thrown when authentication fails due to invalid credentials
 * or other authentication issues. This will typically be mapped to a 401
 * Unauthorized HTTP response.
 */
class AuthenticationException(
    message: String = "Authentication failed",
    val challengeName: String? = null,
    val session: String? = null,
    cause: Throwable? = null,
) : RuntimeException(message, cause)
