package com.cha.domain.exception

/**
 * Exception thrown when a request is invalid due to bad input parameters or validation failures.
 * This will typically be mapped to a 400 Bad Request HTTP response.
 */
class InvalidRequestException(
    val field: String? = null,
    val rejectedValue: Any? = null,
    message: String = "Invalid request parameters",
    cause: Throwable? = null
) : RuntimeException(message, cause) {
    /**
     * Map of field names to error messages for multiple validation errors
     */
    val fieldErrors: MutableMap<String, String> = mutableMapOf()

    /**
     * Constructor for a single field error
     */
    constructor(
        field: String,
        errorMessage: String,
        rejectedValue: Any? = null,
        cause: Throwable? = null
    ) : this(field, rejectedValue, "Invalid request parameters", cause) {
        fieldErrors[field] = errorMessage
    }

    /**
     * Constructor for multiple field errors
     */
    constructor(
        errors: Map<String, String>,
        message: String = "Invalid request parameters",
        cause: Throwable? = null
    ) : this(null, null, message, cause) {
        fieldErrors.putAll(errors)
    }

    /**
     * Add a field error
     */
    fun addFieldError(field: String, errorMessage: String) {
        fieldErrors[field] = errorMessage
    }
}