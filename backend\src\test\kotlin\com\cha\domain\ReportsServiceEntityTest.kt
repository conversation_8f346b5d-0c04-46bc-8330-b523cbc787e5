package com.cha.domain

import com.cha.model.dto.JobStatus
import com.cha.model.dto.ReportType
import com.cha.model.entity.JobEntity
import com.cha.model.entity.ReportEntity
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.micronaut.test.support.TestPropertyProvider
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.time.LocalDateTime

@MicronautTest(environments = ["test"], startApplication = false)
class ReportsServiceEntityTest : TestPropertyProvider {

    @Test
    fun `should create job entity with correct structure`() {
        val empi = 12345
        val mrn = "MRN123"
        val visitId = "VISIT123"
        val reportType = ReportType.VISIT_SUMMARY

        val jobEntity = JobEntity(
            id = "job123",
            userId = "user123",
            status = JobStatus.QUEUED,
            reportType = reportType,
            progress = 0,
            completedAt = null,
            errorMessage = null,
            reportId = null,
            parameters = mapOf(
                "empi" to empi,
                "mrn" to mrn,
                "visitId" to visitId
            ),
            lastMessage = "Job queued for processing"
        )

        assertEquals("job123", jobEntity.id)
        assertEquals("user123", jobEntity.userId)
        assertEquals(JobStatus.QUEUED, jobEntity.status)
        assertEquals(ReportType.VISIT_SUMMARY, jobEntity.reportType)
        assertEquals(0, jobEntity.progress)
        assertNull(jobEntity.completedAt)
        assertNull(jobEntity.errorMessage)
        assertNull(jobEntity.reportId)
        assertEquals("Job queued for processing", jobEntity.lastMessage)
        assertTrue(jobEntity.parameters.containsKey("empi"))
        assertEquals(empi, jobEntity.parameters["empi"])
    }

    @Test
    fun `should create report entity with correct structure`() {
        val reportEntity = ReportEntity(
            id = "report123",
            jobId = "job123",
            userId = "user123",
            title = "Visit Summary Report",
            storageKey = "visit_summary_12345_20241216_071123_a1b2c3d4.pdf",
            fileFormat = "PDF",
            fileSize = 1024L,
            empi = 12345,
            visitId = "VISIT123",
            mrn = "MRN123"
        )

        assertEquals("report123", reportEntity.id)
        assertEquals("job123", reportEntity.jobId)
        assertEquals("user123", reportEntity.userId)
        assertEquals("Visit Summary Report", reportEntity.title)
        assertEquals("visit_summary_12345_20241216_071123_a1b2c3d4.pdf", reportEntity.storageKey)
        assertEquals("PDF", reportEntity.fileFormat)
        assertEquals(1024L, reportEntity.fileSize)
        assertEquals(12345, reportEntity.empi)
        assertEquals("VISIT123", reportEntity.visitId)
        assertEquals("MRN123", reportEntity.mrn)
    }

    @Test
    fun `should link job and report entities correctly`() {
        val jobEntity = JobEntity(
            id = "job123",
            userId = "user123",
            status = JobStatus.COMPLETED,
            reportType = ReportType.VISIT_SUMMARY,
            progress = 100,
            completedAt = LocalDateTime.now(),
            errorMessage = null,
            reportId = "report123", // Links to report
            parameters = mapOf("empi" to 12345),
            lastMessage = "Report generation completed"
        )

        val reportEntity = ReportEntity(
            id = "report123",
            jobId = "job123", // Links back to job
            userId = "user123",
            title = "Visit Summary Report",
            storageKey = "visit_summary_12345_20241216_071123_a1b2c3d4.pdf",
            fileFormat = "PDF",
            fileSize = 1024L,
            empi = 12345,
            visitId = "VISIT123",
            mrn = "MRN123"
        )

        // Verify the bidirectional relationship
        assertEquals(jobEntity.reportId, reportEntity.id)
        assertEquals(reportEntity.jobId, jobEntity.id)
        assertEquals(jobEntity.userId, reportEntity.userId)
    }

    @Test
    fun `should handle job progression states correctly`() {
        // Initial queued state
        val queuedJob = JobEntity(
            id = "job123",
            userId = "user123",
            status = JobStatus.QUEUED,
            reportType = ReportType.ROI,
            progress = 0,
            completedAt = null,
            errorMessage = null,
            reportId = null,
            parameters = mapOf("empi" to 12345),
            lastMessage = "Job queued for processing"
        )

        assertEquals(JobStatus.QUEUED, queuedJob.status)
        assertEquals(0, queuedJob.progress)
        assertNull(queuedJob.reportId)

        // Processing state
        val processingJob = queuedJob.copy(
            status = JobStatus.PROCESSING,
            progress = 50,
            lastMessage = "Collecting clinical data"
        )

        assertEquals(JobStatus.PROCESSING, processingJob.status)
        assertEquals(50, processingJob.progress)
        assertEquals("Collecting clinical data", processingJob.lastMessage)

        // Completed state with report link
        val completedJob = processingJob.copy(
            status = JobStatus.COMPLETED,
            progress = 100,
            completedAt = LocalDateTime.now(),
            reportId = "report123",
            lastMessage = "Report generation completed"
        )

        assertEquals(JobStatus.COMPLETED, completedJob.status)
        assertEquals(100, completedJob.progress)
        assertEquals("report123", completedJob.reportId)
        assertNotNull(completedJob.completedAt)
    }

    @Test
    fun `should generate unique filenames with correct format`() {
        // Test the filename generation pattern
        val reportTypes = listOf(ReportType.ROI, ReportType.MRN, ReportType.VISIT_SUMMARY)
        val empi = 12345

        reportTypes.forEach { reportType ->
            // Using regex to validate the filename format: {reportType}_{empi}_{timestamp}_{uuid}.pdf
            val filenamePattern = """^${reportType.name.lowercase()}_${empi}_\d{8}_\d{6}_[a-f0-9]{8}\.pdf$""".toRegex()

            // Generate a sample filename (this would normally be done by generateUniqueReportFilename)
            val sampleFilename = "${reportType.name.lowercase()}_${empi}_20241216_071123_a1b2c3d4.pdf"

            assertTrue(
                filenamePattern.matches(sampleFilename),
                "Filename '$sampleFilename' should match pattern for ${reportType.name}"
            )
        }
    }

    @Test
    fun `should validate proper entity table mappings`() {
        // Test that the JobEntity maps to "jobs" table
        val jobEntity = JobEntity(
            id = "job123",
            userId = "user123",
            status = JobStatus.QUEUED,
            reportType = ReportType.VISIT_SUMMARY,
            progress = 0,
            completedAt = null,
            errorMessage = null,
            reportId = null,
            parameters = mapOf("empi" to 12345),
            lastMessage = "Job queued for processing"
        )

        // Verify the entity has correct structure for the "jobs" table
        assertNotNull(jobEntity.id)
        assertNotNull(jobEntity.userId)
        assertNotNull(jobEntity.status)
        assertNotNull(jobEntity.reportType)
        assertNotNull(jobEntity.progress)
        assertNotNull(jobEntity.parameters)

        // Test that the ReportEntity maps to "reports" table
        val reportEntity = ReportEntity(
            id = "report123",
            jobId = "job123",
            userId = "user123",
            title = "Visit Summary Report",
            storageKey = "visit_summary_12345_20241216_071123_a1b2c3d4.pdf",
            fileFormat = "PDF",
            fileSize = 1024L,
            empi = 12345,
            visitId = "VISIT123",
            mrn = "MRN123"
        )

        // Verify the entity has correct structure for the "reports" table
        assertNotNull(reportEntity.id)
        assertNotNull(reportEntity.jobId)
        assertNotNull(reportEntity.userId)
        assertNotNull(reportEntity.storageKey)
        assertNotNull(reportEntity.fileFormat)
        assertNotNull(reportEntity.fileSize)
        assertNotNull(reportEntity.empi)
    }

    @Test
    fun `should use custom file title when provided`() {
        val customTitle = "Custom Patient Report"

        val reportEntity = ReportEntity(
            id = "report123",
            jobId = "job123",
            userId = "user123",
            title = customTitle, // Using custom title
            storageKey = "visit_summary_12345_20241216_071123_a1b2c3d4.pdf",
            fileFormat = "PDF",
            fileSize = 1024L,
            empi = 12345,
            visitId = "VISIT123",
            mrn = "MRN123"
        )

        assertEquals(customTitle, reportEntity.title)
        assertNotEquals("Visit Summary Report", reportEntity.title) // Should not be default
    }

    @Test
    fun `should use default title format when fileTitle is null`() {
        // Test default title generation for different report types
        val testCases = mapOf(
            ReportType.VISIT_SUMMARY to "Visit summary Report",
            ReportType.ROI to "Roi Report",
            ReportType.MRN to "Mrn Report"
        )

        testCases.forEach { (reportType, expectedTitle) ->
            val generatedTitle =
                reportType.name.lowercase().replace("_", " ").replaceFirstChar { it.uppercase() } + " Report"
            assertEquals(expectedTitle, generatedTitle, "Title should match expected format for ${reportType.name}")
        }
    }

    override fun getProperties(): Map<String, String> {
        return emptyMap()
    }
}