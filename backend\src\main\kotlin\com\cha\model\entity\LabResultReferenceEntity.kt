package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("reference_lab_results")
data class LabResultReferenceEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val sequence: String?,
    val resultType: Char?,
    val orderCode: String?,
    val orderCodeDescription: String?,
    val testCode: String?,
    val testDescription: String?,
    val testAbbreviation: String?,
    val resultLine: String?,
    val referenceRange: String?,
    val abnormalFlag: String?,
    val testUnit: String?,
    val eventDate: LocalDateTime?,
    val releaseDate: LocalDateTime?,
    val performedBy: String?,
    val enteredDate: LocalDateTime?,
    val enteredBy: String?,
    val testStatus: String?,
    val correctedResult: String?,
    val abnormalCode: String?,
    val testResultFlag: String?,
    val normalLookupFlag: String?,
    val systemName: String?,
    val securityLevel: Boolean?,
    val admitDate: LocalDateTime?
)
