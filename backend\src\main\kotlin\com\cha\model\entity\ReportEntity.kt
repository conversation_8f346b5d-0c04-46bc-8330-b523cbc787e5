package com.cha.model.entity

import io.micronaut.data.annotation.DateCreated
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime
import java.util.UUID

/**
 * Represents a generated report artifact. This entity stores metadata
 * about the final document, such as its title and storage location.
 */
@MappedEntity("reports")
data class ReportEntity(
    @field:Id
    val id: String = UUID.randomUUID().toString(),

    // Foreign key linking back to the JobEntity that created this report.
    val jobId: String,

    // The user who initiated the report generation.
    val userId: String,

    // An optional, user-defined title for the report.
    val title: String?,

    // The storage key for the file (e.g., the path in an S3 bucket).
    val storageKey: String,

    // The format of the report, e.g., "PDF", "CSV".
    val fileFormat: String,

    // The size of the generated file in bytes.
    val fileSize: Long,

    // Timestamp of when the report was successfully generated.
    @DateCreated
    val createdAt: LocalDateTime = LocalDateTime.now(),

    // Key identifiers to make querying for reports easier.
    val empi: Int,
    val visitId: String?,
    val mrn: String?,
)
