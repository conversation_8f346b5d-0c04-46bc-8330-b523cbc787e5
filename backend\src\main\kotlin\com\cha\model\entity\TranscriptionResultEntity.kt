package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("transcription_results")
data class TranscriptionResultEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val systemId: Int?,
    val empi: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val orderCode: String?,
    val orderDescription: String?,
    val readingCaregiver: String?,
    val signingCaregiver: String?,
    val status: String?,
    val enterDatetime: LocalDateTime?,
    val readDatetime: LocalDateTime?,
    val signDatetime: LocalDateTime?,
    val cancelReason: String?,
    val transcriptionText: String?,
    val systemName: String?,
    val securityLevel: Boolean? = false,
    val admitDate: LocalDateTime?
)
