package com.cha.model.dto

import com.cha.model.entity.InvoiceNoteEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(description = "Invoice note information")
data class InvoiceNote(
    @Schema(description = "Patient's Enterprise Master Patient Index", example = "12345")
    val empi: Int?,
    @Schema(description = "System identifier", example = "1")
    val systemId: Int?,
    @Schema(description = "Medical Record Number", example = "MRN123456")
    val mrn: String?,
    @Schema(description = "Account number", example = "ACC789012")
    val accountNumber: String?,
    @Schema(description = "Note date", example = "2024-01-15T10:30:00")
    val noteDate: LocalDateTime?,
    @Schema(description = "Patient note content", example = "Payment plan arranged for outstanding balance")
    val patientNote: String?,
    @Schema(description = "System name where note originates", example = "HIS")
    val systemName: String?,
    @Schema(description = "Security level flag", example = "false")
    val securityLevel: Boolean?,
    @Schema(description = "Admission date", example = "2024-01-15T08:00:00")
    val admitDate: LocalDateTime?
)

fun InvoiceNoteEntity.toDto(): InvoiceNote {
    return InvoiceNote(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        noteDate = this.noteDate,
        patientNote = this.patientNote,
        systemName = this.systemName,
        securityLevel = this.securityLevel,
        admitDate = this.admitDate
    )
}