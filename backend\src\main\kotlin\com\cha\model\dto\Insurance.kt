package com.cha.model.dto

import com.cha.model.entity.InsuranceEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(description = "Patient insurance information")
data class Insurance(
    @Schema(description = "Patient's Enterprise Master Patient Index", example = "12345")
    val empi: Int?,
    @Schema(description = "System identifier", example = "1")
    val systemId: Int?,
    @Schema(description = "Medical Record Number", example = "MRN123456")
    val mrn: String?,
    @Schema(description = "Account number", example = "ACC789012")
    val accountNumber: String?,
    @Schema(description = "Insurance plan name", example = "Blue Cross Blue Shield")
    val insurancePlan: String?,
    @Schema(description = "Insurance sequence", example = "01")
    val insSeq: String?,
    @Schema(description = "Policy holder name", example = "<PERSON>")
    val policyHolder: String?,
    @Schema(description = "Policy number", example = "POL123456789")
    val policyNumber: String?,
    @Schema(description = "Certificate number", example = "CERT987654")
    val certificateNo: String?,
    @Schema(description = "Insurance organization name", example = "Anthem Inc.")
    val organizationName: String?,
    @Schema(description = "Policy holder Social Security Number", example = "***-**-1234")
    val policyHolderSsn: String?,
    @Schema(description = "Group number", example = "GRP456789")
    val groupNumber: String?,
    @Schema(description = "Group name", example = "Company Health Plan")
    val groupName: String?,
    @Schema(description = "Subscriber number", example = "SUB123456")
    val subscriberNumber: String?,
    @Schema(description = "System name where insurance data originates", example = "HIS")
    val systemName: String?,
    @Schema(description = "Security level flag", example = "false")
    val securityLevel: Boolean?,
    @Schema(description = "Admission date", example = "2024-01-15T10:30:00")
    val admitDate: LocalDateTime?
)

fun InsuranceEntity.toDto(): Insurance {
    return Insurance(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        insurancePlan = this.insurancePlan,
        insSeq = this.insSeq,
        policyHolder = this.policyHolder,
        policyNumber = this.policyNumber,
        certificateNo = this.certificateNo,
        organizationName = this.organizationName,
        policyHolderSsn = this.policyHolderSsn,
        groupNumber = this.groupNumber,
        groupName = this.groupName,
        subscriberNumber = this.subscriberNumber,
        systemName = this.systemName,
        securityLevel = this.securityLevel,
        admitDate = this.admitDate
    )
}
