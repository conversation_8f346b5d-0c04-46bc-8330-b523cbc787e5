package com.cha.infrastructure

import software.amazon.awscdk.CfnOutput
import software.amazon.awscdk.Duration
import software.amazon.awscdk.RemovalPolicy
import software.amazon.awscdk.Stack
import software.amazon.awscdk.StackProps
import software.amazon.awscdk.Tags
import software.amazon.awscdk.services.certificatemanager.ICertificate
import software.amazon.awscdk.services.ec2.IVpc
import software.amazon.awscdk.services.ec2.Peer
import software.amazon.awscdk.services.ec2.Port
import software.amazon.awscdk.services.ec2.SecurityGroup
import software.amazon.awscdk.services.ec2.SubnetSelection
import software.amazon.awscdk.services.ec2.SubnetType
import software.amazon.awscdk.services.ecr.assets.DockerImageAsset
import software.amazon.awscdk.services.ecr.assets.Platform
import software.amazon.awscdk.services.ecs.AwsLogDriverProps
import software.amazon.awscdk.services.ecs.Cluster
import software.amazon.awscdk.services.ecs.ContainerImage
import software.amazon.awscdk.services.ecs.ContainerInsights
import software.amazon.awscdk.services.ecs.CpuArchitecture
import software.amazon.awscdk.services.ecs.DeploymentCircuitBreaker
import software.amazon.awscdk.services.ecs.LogDriver
import software.amazon.awscdk.services.ecs.OperatingSystemFamily
import software.amazon.awscdk.services.ecs.RuntimePlatform
import software.amazon.awscdk.services.ecs.patterns.ApplicationLoadBalancedFargateService
import software.amazon.awscdk.services.ecs.patterns.ApplicationLoadBalancedTaskImageOptions
import software.amazon.awscdk.services.elasticloadbalancingv2.ApplicationProtocol
import software.amazon.awscdk.services.elasticloadbalancingv2.HealthCheck
import software.amazon.awscdk.services.elasticloadbalancingv2.Protocol
import software.amazon.awscdk.services.iam.Effect
import software.amazon.awscdk.services.iam.ManagedPolicy
import software.amazon.awscdk.services.iam.PolicyStatement
import software.amazon.awscdk.services.iam.Role
import software.amazon.awscdk.services.iam.ServicePrincipal
import software.amazon.awscdk.services.logs.LogGroup
import software.amazon.awscdk.services.logs.RetentionDays
import software.amazon.awscdk.services.route53.IHostedZone
import software.amazon.awscdk.services.s3.IBucket
import software.constructs.Construct

class ApplicationStack(
    scope: Construct,
    id: String,
    props: StackProps? = null, // Original props from caller
    val vpc: IVpc,
    documentsBucket: IBucket,
    loggingBucket: IBucket,
    val certificate: ICertificate,
    val hostedZone: IHostedZone,
    envName: String, // envName is already a parameter, used for the description
    environmentVariables: Map<String, String>,
    appVersion: String,
) : Stack(scope, id, buildStackPropsWithDescription(props, envName)) {

    val albDnsName: String
    val ecrRepositoryUri: String
    val fargateSecurityGroup: SecurityGroup // Exposed for potential cross-stack SG rule

    init {
        Tags.of(this).add("project", "viewer-app-application")
        Tags.of(this).add("environment", envName)

        // --- ECS Cluster ---
        val cluster = Cluster.Builder.create(this, "AppEcsCluster")
            .vpc(vpc) // Use the VPC from InfrastructureStack
            .clusterName("viewer-app-cluster-$envName")
            .containerInsightsV2(ContainerInsights.ENHANCED)
            .build()
        Tags.of(cluster).add("project", "viewer-app")
        Tags.of(cluster).add("environment", envName)

        // --- IAM Roles for Fargate Task ---
        val taskExecutionRole = Role.Builder.create(this, "FargateTaskExecutionRole")
            .assumedBy(ServicePrincipal("ecs-tasks.amazonaws.com"))
            .managedPolicies(
                listOf(
                    ManagedPolicy.fromAwsManagedPolicyName("service-role/AmazonECSTaskExecutionRolePolicy")
                )
            )
            .build()
        taskExecutionRole.addToPolicy(
            PolicyStatement.Builder.create()
                .effect(Effect.ALLOW)
                .actions(
                    listOf(
                        "ecr:GetDownloadUrlForLayer",
                        "ecr:BatchGetImage",
                        "ecr:BatchCheckLayerAvailability"
                    )
                )
                .resources(listOf("arn:aws:ecr:$region:$account:repository/*"))
                .build()
        )

        val taskRole = Role.Builder.create(this, "FargateTaskRole")
            .assumedBy(ServicePrincipal("ecs-tasks.amazonaws.com"))
            .description("IAM role for Fargate tasks to access AWS services")
            .build()

        // Grant S3 permissions to the Fargate Task Role using the documentsBucket from InfrastructureStack
        taskRole.addToPolicy(
            PolicyStatement.Builder.create()
                .effect(Effect.ALLOW)
                .actions(listOf("s3:GetObject", "s3:PutObject", "s3:DeleteObject"))
                .resources(
                    listOf(
                        documentsBucket.bucketArn + "/imported-ehr-documents/*",
                        documentsBucket.bucketArn + "/viewer-generated-reports/*"
                    )
                )
                .build()
        )
        taskRole.addToPolicy(
            PolicyStatement.Builder.create()
                .effect(Effect.ALLOW)
                .actions(listOf("s3:ListBucket"))
                .resources(listOf(documentsBucket.bucketArn))
                .conditions(
                    mapOf(
                        "StringLike" to mapOf(
                            "s3:prefix" to listOf(
                                "imported-ehr-documents/",
                                "viewer-generated-reports/"
                            )
                        )
                    )
                )
                .build()
        )
        taskRole.addToPolicy(
            PolicyStatement.Builder.create()
                .actions(
                    listOf(
                        "cognito-idp:GetUser",
                        "cognito-idp:ListUsers",
                        "cognito-idp:AdminGetUser",
                        "cognito-idp:AdminInitiateAuth",
                        "cognito-idp:AdminListGroupsForUser",
                        "cognito-idp:AdminDeleteUser",
                        "cognito-idp:AdminCreateUser",
                        "cognito-idp:AdminAddUserToGroup"
                    )
                )
                .resources(listOf("arn:aws:cognito-idp:$region:$account:userpool/*"))
                .effect(Effect.ALLOW)
                .build()
        )
        taskRole.addToPolicy(
            PolicyStatement.Builder.create()
                .effect(Effect.ALLOW)
                .actions(
                    listOf(
                        "secretsmanager:GetSecretValue",
                        "secretsmanager:DescribeSecret"
                    )
                )
                .resources(listOf("arn:aws:secretsmanager:${region}:${account}:secret:/config/application/*"))
                .build()
        )

        taskRole.addToPolicy(
            PolicyStatement.Builder.create()
                .effect(Effect.ALLOW)
                .actions(listOf("secretsmanager:ListSecrets"))
                .resources(listOf("*")) // ListSecrets is not resource-specific, so we use "*"
//                .resources(listOf("arn:aws:secretsmanager:${region}:${account}:secret:/config/application/*")) // Restrict to secrets under /config/application/
                .build()
        )

        // Add CloudWatch PutMetricData permission
        taskRole.addToPolicy(
            PolicyStatement.Builder.create()
                .effect(Effect.ALLOW)
                .actions(listOf("cloudwatch:PutMetricData"))
                .resources(listOf("*")) // CloudWatch PutMetricData is not resource-specific in the same way as S3
                .build()
        )

        // --- Security Groups for ALB and Fargate Service ---
        val albSecurityGroup = SecurityGroup.Builder.create(this, "AlbSecurityGroup")
            .vpc(vpc)
            .description("Security group for Application Load Balancer")
            .allowAllOutbound(true)
            .build()

        // Allow inbound traffic on HTTP (80) and HTTPS (443) ports from the Internet
        albSecurityGroup.addIngressRule(
            Peer.anyIpv4(),
            Port.tcp(80),
            "Allow HTTP from Internet"
        )
        albSecurityGroup.addIngressRule(
            Peer.anyIpv4(),
            Port.tcp(443),
            "Allow HTTPS from Internet"
        )

        // This is the Fargate Service's own security group
        fargateSecurityGroup = SecurityGroup.Builder.create(this, "FargateSecurityGroup")
            .vpc(vpc) // Use the VPC from InfrastructureStack
            .description("Security group for Fargate service")
            .allowAllOutbound(true)
            .build()
        // Allow traffic from ALB to Fargate tasks on port 8080
        fargateSecurityGroup.addIngressRule(albSecurityGroup, Port.tcp(8080), "Allow traffic from ALB to Fargate Tasks")

        // --- Log Group for Container Logs ---
        val logGroup = LogGroup.Builder.create(this, "FargateAppLogGroup")
            .logGroupName("/ecs/viewer-app-$envName")
            .retention(RetentionDays.ONE_WEEK)
            .removalPolicy(if (envName == "prod") RemovalPolicy.RETAIN else RemovalPolicy.DESTROY)
            .build()

        // --- Docker Image Asset ---
        val dockerImageAsset = DockerImageAsset.Builder.create(this, "AppDockerImage")
            .directory("..") // Points to project root directory where Dockerfile is located
            .platform(Platform.LINUX_AMD64)
            .buildArgs(
                mapOf(
                    "APP_VERSION" to appVersion // Version from libs.versions.toml
                )
            )
            .build()
        ecrRepositoryUri = dockerImageAsset.imageUri

        // --- Create ApplicationLoadBalancedFargateService ---
        // Configure custom ALB properties to avoid port conflicts
        val fargateService = ApplicationLoadBalancedFargateService.Builder.create(this, "AppFargateService")
            .serviceName("viewer-service-$envName")
            .cluster(cluster)
            .cpu(1024)
            .memoryLimitMiB(2048)
            .desiredCount(if (envName == "prod") 2 else 1)
            .minHealthyPercent(0) // TODO: Revisit health percentages for production
            .maxHealthyPercent(200)
            .healthCheckGracePeriod(Duration.seconds(90))
            .circuitBreaker(DeploymentCircuitBreaker.builder().enable(true).rollback(true).build())
            .taskImageOptions(
                ApplicationLoadBalancedTaskImageOptions.builder()
                    .image(ContainerImage.fromDockerImageAsset(dockerImageAsset))
                    .containerPort(8080)
                    .containerName("AppContainer")
                    .environment(environmentVariables)
                    .taskRole(taskRole)
                    .executionRole(taskExecutionRole)
                    .logDriver(
                        LogDriver.awsLogs(
                            AwsLogDriverProps.builder()
                                .logGroup(logGroup)
                                .streamPrefix("ecs")
                                .build()
                        )
                    )
                    .build()
            )
            .publicLoadBalancer(true)
            .assignPublicIp(false)
            .securityGroups(listOf(fargateSecurityGroup))
            .loadBalancerName("viewer-alb-$envName")
            .runtimePlatform(
                RuntimePlatform.builder()
                    .cpuArchitecture(CpuArchitecture.X86_64)
                    .operatingSystemFamily(OperatingSystemFamily.LINUX)
                    .build()
            )
            .taskSubnets(
                SubnetSelection.builder()
                    .subnetType(SubnetType.PRIVATE_WITH_EGRESS)
                    .build()
            )
            .protocol(ApplicationProtocol.HTTPS)
            .redirectHttp(true)
            .certificate(certificate)
            .domainName("api.chaviewer.com")
            .domainZone(hostedZone)
            .build()

        albDnsName = fargateService.loadBalancer.loadBalancerDnsName

        fargateService.targetGroup.configureHealthCheck(
            HealthCheck.builder()
                .path("/api/health")
                .protocol(Protocol.HTTP) // Internal traffic uses HTTP
                .port("8080")
                .healthyThresholdCount(2)
                .interval(Duration.seconds(30))
                .timeout(Duration.seconds(10))
                .build()
        )

        fargateService.loadBalancer.addSecurityGroup(albSecurityGroup) // Associate ALB with its SG

        // Enable ALB access logging to fix AwsSolutions-ELB2
        fargateService.loadBalancer.logAccessLogs(loggingBucket, "alb-access-logs")

        Tags.of(fargateService.loadBalancer).add("project", "viewer-app")
        Tags.of(fargateService.loadBalancer).add("environment", envName)
        Tags.of(fargateService.targetGroup).add("project", "viewer-app")
        Tags.of(fargateService.targetGroup).add("environment", envName)
        Tags.of(fargateService.service).add("project", "viewer-app")
        Tags.of(fargateService.service).add("environment", envName)

        // --- Outputs ---
        CfnOutput.Builder.create(this, "ALBDnsNameOutput")
            .exportName("ALBDnsName-${envName}")
            .value(albDnsName)
            .description("DNS name of the Application Load Balancer for $envName")
            .build()

        CfnOutput.Builder.create(this, "EcrRepositoryUriOutput")
            .exportName("EcrRepositoryUri-${envName}")
            .value(ecrRepositoryUri)
            .description("URI of the ECR repository for $envName")
            .build()
    }

    companion object {
        private fun buildStackPropsWithDescription(
            originalProps: StackProps?,
            envNameForDescription: String,
        ): StackProps {
            val description =
                "Manages the Viewer backend application deployment for the '$envNameForDescription' environment. Includes ECS Fargate service, ALB, ECR image deployment, IAM roles, SGs, and service integrations."
            val builder = StackProps.builder()

            // Copy properties from originalProps if they exist
            originalProps?.let { op ->
                op.env?.let { builder.env(it) }
                op.stackName?.let { builder.stackName(it) }
                op.synthesizer?.let { builder.synthesizer(it) }
                op.tags?.takeIf { it.isNotEmpty() }?.let { builder.tags(it) }
                op.terminationProtection?.let { builder.terminationProtection(it) }
                op.crossRegionReferences?.let { builder.crossRegionReferences(it) }
            }

            // Set/override the description for this ApplicationStack
            builder.description(description)

            return builder.build()
        }
    }
}
