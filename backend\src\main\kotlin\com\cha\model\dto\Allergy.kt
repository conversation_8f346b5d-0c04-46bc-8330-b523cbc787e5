package com.cha.model.dto

import com.cha.model.entity.AllergyEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a clinical allergy record for a patient, including allergen, reaction, severity, and relevant dates."
)
data class Allergy(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int?,
    @Schema(description = "System identifier for the allergy record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the allergy record.")
    val accountNumber: String?,
    @Schema(description = "Severity of the allergy (e.g., mild, moderate, severe).")
    val severity: String?,
    @Schema(description = "Date when the allergy was first recorded.")
    val allergyStartDate: LocalDateTime?,
    @Schema(description = "Date when the allergy ended or was resolved, if applicable.")
    val allergyEndDate: LocalDateTime?,
    @Schema(description = "Description of the patient's reaction to the allergen.")
    val reaction: String?,
    @Schema(description = "Code representing the allergen.")
    val allergyCode: String?,
    @Schema(description = "Type of allergy (e.g., drug, food, environmental).")
    val allergyType: String?,
    @Schema(description = "Human-readable description of the allergen.")
    val allergyDescription: String?,
    @Schema(description = "Name of the system/source where the allergy was recorded.")
    val systemName: String?,
    @Schema(description = "Admission date associated with the allergy record, if relevant.")
    val admitDate: LocalDateTime?,
    @Schema(description = "Indicates if the allergy record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean?,
)

fun AllergyEntity.toDto(): Allergy {
    return Allergy(
        empi = this.empi,
        systemId = this.systemId,
        systemName = this.systemName,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        severity = this.severity,
        allergyStartDate = allergyStartDate,
        allergyEndDate = allergyEndDate,
        reaction = this.reaction,
        allergyCode = this.allergyCode,
        allergyType = this.allergyType,
        allergyDescription = this.allergyDescription,
        admitDate = this.admitDate,
        securityLevel = this.securityLevel
    )
}
