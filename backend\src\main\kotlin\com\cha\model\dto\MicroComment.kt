package com.cha.model.dto

import com.cha.model.entity.MicroCommentEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a microbiology comment for a patient, including comment type, description, and entry details."
)
data class MicroComment(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int,
    @Schema(description = "System identifier for the microbiology record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the microbiology record.")
    val accountNumber: String?,
    @Schema(description = "Accession number for the microbiology test.")
    val accessionNumber: String?,
    @Schema(description = "Isolate identifier for the accession.")
    val accessionIsolate: String?,
    @Schema(description = "Isolate number for the test.")
    val isolateNumber: Int?,
    @Schema(description = "Type of comment (e.g., general, result, interpretive).")
    val commentType: String?,
    @Schema(description = "Sequence number for the comment.")
    val sequenceNumber: Int?,
    @Schema(description = "Description or text of the comment.")
    val commentDescription: String?,
    @Schema(description = "Date and time when the comment was entered.")
    val enteredDate: LocalDateTime?,
    @Schema(description = "Name of the person who entered the comment.")
    val enteredBy: String?,
    @Schema(description = "Name of the system/source where the comment was recorded.")
    val systemName: String?,
    @Schema(description = "Indicates if the record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean?,
    @Schema(description = "Admission date associated with the record, if relevant.")
    val admitDate: LocalDateTime?
)

fun MicroCommentEntity.toDto(): MicroComment {
    return MicroComment(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        accessionNumber = this.accessionNumber,
        accessionIsolate = this.accessionIsolate,
        isolateNumber = this.isolateNumber,
        commentType = this.commentType,
        sequenceNumber = this.sequenceNumber,
        commentDescription = this.commentDescription,
        enteredDate = this.enteredDate,
        enteredBy = this.enteredBy,
        systemName = this.systemName,
        securityLevel = this.securityLevel ?: false,
        admitDate = this.admitDate
    )
}