@file:OptIn(KspExperimental::class)

import com.google.devtools.ksp.KspExperimental
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import java.util.Properties

plugins {
    kotlin("jvm")
    id("com.google.devtools.ksp")
    id("org.jetbrains.kotlin.plugin.allopen")
    id("io.micronaut.application")
    id("com.gradleup.shadow")
    id("io.micronaut.aot")
    alias(libs.plugins.buildconfig)
}

group = "com.cha"
version = libs.versions.viewer.get()

// Configure buildconfig plugin to expose the version from libs.versions.toml
buildConfig {
    packageName("com.cha")
    buildConfigField("String", "VERSION", "\"${libs.versions.viewer.get()}\"")
    useKotlinOutput { internalVisibility = true }
}

dependencies {
    // Kotlin
    implementation(kotlin("stdlib-jdk8"))
    implementation(kotlin("reflect"))
    implementation(mn.kotlinx.coroutines.core)
    implementation(mn.kotlinx.coroutines.reactive)
    implementation(mn.micronaut.kotlin.extension.functions)
    implementation(mn.micronaut.kotlin.runtime)

    // HTTP Client
    ksp("io.micronaut.servlet:micronaut-servlet-processor")
    implementation("io.micronaut:micronaut-http-client")
    compileOnly(mn.micronaut.http.client.jdk)
    implementation(mn.micronaut.reactor.http.client)
    implementation("io.micronaut.problem:micronaut-problem-json")

    // Security
    implementation(mn.micronaut.security.asProvider())
    implementation(mn.micronaut.security.jwt)
    ksp(mn.micronaut.security.annotations)

    // AWS
    implementation(platform(awssdk.bom))
    implementation(mn.micronaut.aws.sdk.v2)
    implementation(mn.micronaut.aws.cloudwatch.logging)
    implementation(mn.micronaut.aws.secretsmanager)
    implementation(mn.micronaut.aws.parameter.store)
    implementation(awssdk.services.cognitoidentityprovider.asProvider())
    implementation(awssdk.services.s3.asProvider())
    implementation(awssdk.services.servicediscovery.asProvider())

    // Data
    ksp(mn.micronaut.data.processor)
    implementation(mn.micronaut.data.runtime)
    implementation(mn.micronaut.data.model)
    implementation(mn.micronaut.data.jdbc)
    implementation("io.micronaut.liquibase:micronaut-liquibase")
//    runtimeOnly("org.postgresql:postgresql")
    runtimeOnly(mn.mssql.jdbc)
    implementation(mn.micronaut.jdbc.hikari)
    implementation(mn.micronaut.crac.asProvider())
    implementation(mn.micronaut.multitenancy.asProvider())

    // Serialization
    runtimeOnly(mn.jackson.module.kotlin)
    ksp(mn.micronaut.serde.processor)

    // Validation
    ksp(mn.micronaut.validation.processor)
    ksp(mn.micronaut.http.validation)
    compileOnly(mn.jakarta.persistence.api)

    // Documentation
    ksp(mn.micronaut.openapi.asProvider())
//    compileOnly(mn.micronaut.openapi.annotations)
    compileOnly("io.micronaut.openapi:micronaut-openapi-annotations:6.17.1")
    implementation("io.swagger.core.v3:swagger-annotations")

    // Logging
    runtimeOnly(mn.logback.classic)
    implementation(libs.kotlin.logging)
    implementation(mn.slf4j.api)
    implementation(mn.kotlinx.coroutines.slf4j)
    implementation(libs.logback.encoder)
    implementation(libs.janino)

    // Logback JSON formatting support
    implementation("ch.qos.logback.contrib:logback-jackson:0.1.5")
    implementation("ch.qos.logback.contrib:logback-json-classic:0.1.5")

    // JANSI for colored console output
    implementation("org.fusesource.jansi:jansi:2.4.1")

    // Metrics
    ksp(mn.micronaut.micrometer.annotation)
    implementation(mn.micrometer.context.propagation)
    implementation(mn.micronaut.micrometer.registry.statsd)
    implementation(mn.micronaut.micrometer.registry.cloudwatch)
    implementation(mn.micronaut.management)

    // Ahead-of-time compilation
    aotPlugins(platform(mn.micronaut.aot.bom))
    aotPlugins(mn.micronaut.security.aot)

    // Misc.
    runtimeOnly(mn.snakeyaml)
    implementation(mn.micronaut.aop)
    implementation(mn.micronaut.reactor.asProvider())
    implementation(libs.openPdf)

    // Test
    testImplementation(mn.micronaut.test.junit5)
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test")
}

application {
    mainClass = "com.cha.ApplicationKt"
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

kotlin {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_21)
    }
}

ksp {
    // TODO: migrate to use KSP2 once micronaut fully supports it.
    useKsp2 = false
    arg("micronaut.openapi.project.dir", projectDir.toString())
}

graalvmNative.toolchainDetection = false

micronaut {
    runtime("netty")
    testRuntime("junit5")
    processing {
        incremental(true)
        annotations("com.cha.*")
    }

    aot {
        // Please review carefully the optimizations enabled below
        // Check https://micronaut-projects.github.io/micronaut-aot/latest/guide/ for more details
        optimizeServiceLoading = false
        convertYamlToJava = false
        precomputeOperations = true
        cacheEnvironment = true
        optimizeClassLoading = true
        deduceEnvironment = true
        optimizeNetty = true
        replaceLogbackXml = false
        configurationProperties.put("micronaut.security.jwks.enabled", "false")
        configurationProperties.put("micronaut.security.openid-configuration.enabled", "false")
    }
}

tasks.named<io.micronaut.gradle.docker.NativeImageDockerfile>("dockerfileNative") {
    jdkVersion = "21"
    args(
        "-XX:MaximumHeapSizePercent=80", "-Dio.netty.allocator.numDirectArenas=0", "-Dio.netty.noPreferDirect=true"
    )
}

tasks.withType(JavaCompile::class.java).configureEach {
    options.compilerArgs.add("-parameters")
}

// Function to load .env file
fun loadEnvFile(file: File): Properties {
    val props = Properties()
    if (file.exists()) {
        file.forEachLine { line ->
            val trimmedLine = line.trim()
            // Skip empty lines and comments
            if (trimmedLine.isNotEmpty() && !trimmedLine.startsWith("#")) {
                val parts = trimmedLine.split("=", limit = 2)
                if (parts.size == 2) {
                    val key = parts[0].trim()
                    // Remove potential surrounding quotes from value
                    val value = parts[1].trim().removeSurrounding("\"").removeSurrounding("'")
                    props[key] = value
                }
            }
        }
    } else {
        // Print a warning if the .env file is not found
        println("Warning: .env file not found at ${file.absolutePath}. Environment variables might be missing.")
    }
    return props
}

// Load environment variables from the root project's .env file
val envProps = loadEnvFile(rootProject.projectDir.resolve(".env"))

// Configure the run task to use these properties
tasks.named<JavaExec>("run") {
    // Convert Properties to Map<String, Any> for systemProperties
    val systemPropsMap = envProps.map { (key, value) -> key.toString() to value }.toMap()
    if (systemPropsMap.isNotEmpty()) {
        println("Applying system properties from .env file to :backend:run task")
        systemProperties(systemPropsMap)
    }
}
