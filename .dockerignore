# General Version Control
.git
.gitignore
.gitattributes

# IDE and Editor files
.idea/
*.iml
*.iws
.vscode/
*.code-workspace

# Build system files (Gradle specific)
.gradle
# Modified: Don't exclude build directory completely, as we need the JAR
# build/ # Ignore root build directory
gradle/
# gradlew
# gradlew.bat
# settings.gradle.kts
# build.gradle.kts

# Docker files (don't ignore the Dockerfile itself)
# Dockerfile is implicitly included as it's the definition file

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS generated files
.DS_Store
Thumbs.db

# Dependencies (if not part of the JAR)
# node_modules/
# bower_components/

# Test reports and temporary files
coverage/
*.tmp
*~
*.swp

# Specific to this project structure
# Modified: Don't exclude backend directory completely
# backend/
# Instead, exclude everything in backend except the JAR files
backend/*
!backend/build/
backend/build/*
!backend/build/libs/
backend/build/libs/*
!backend/build/libs/*.jar

# Ignore everything in 'infrastructure'
infrastructure/

# Ignore scripts if not needed in image
scripts/

# Explicitly include the root .env.example as it's copied by Dockerfile
!.env.example
!.env
