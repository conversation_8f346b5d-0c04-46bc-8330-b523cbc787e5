name: Deploy to AWS Staging Environment

on:
  push:
    branches:
      - main
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: staging
    env:
      AWS_ACCOUNT_ID: "************"
      AWS_REGION: "us-east-1"

    # Grant GITHUB_TOKEN permissions for OIDC
    permissions:
      id-token: write # Required for requesting the JWT
      contents: read # Required for checking out code

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: corretto

      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install AWS CDK
        run: npm install -g aws-cdk

      - name: Configure AWS Credentials (Staging)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          audience: sts.amazonaws.com
          role-to-assume: arn:aws:iam::${{ env.AWS_ACCOUNT_ID }}:role/GitHubAction-AssumeRole-ViewerBackend-staging
          aws-region: ${{ env.AWS_REGION }}
          role-session-name: GitHubAction-Staging-${{ github.run_id }}

      - name: Create .env file
        env:
          # Workflow env
          AWS_ACCOUNT_ID_WORKFLOW: ${{ env.AWS_ACCOUNT_ID }}
          AWS_REGION_WORKFLOW: ${{ env.AWS_REGION }}
          # Static or from vars/secrets
          APP_BUCKET_NAME_VAL: "viewer-documents"
          COGNITO_POOL_ID_SECRET: ${{ secrets.COGNITO_POOL_ID }}
          OAUTH_CLIENT_ID_VAL: ${{ secrets.OAUTH_CLIENT_ID }}
          ALLOWED_CORS_ORIGINS_VAR: ${{ vars.ALLOWED_CORS_ORIGINS }}
          MICRONAUT_ENVIRONMENTS_VAL: "staging"
        run: |
          echo "Creating .env file..."
          echo "AWS_ACCOUNT_ID=${AWS_ACCOUNT_ID_WORKFLOW}" > .env
          echo "AWS_REGION=${AWS_REGION_WORKFLOW}" >> .env
          echo "APP_BUCKET_NAME=${APP_BUCKET_NAME_VAL}" >> .env
          echo "COGNITO_POOL_ID=${COGNITO_POOL_ID_SECRET}" >> .env
          echo "OAUTH_CLIENT_ID=${OAUTH_CLIENT_ID_VAL}" >> .env
          echo "ALLOWED_CORS_ORIGINS=${ALLOWED_CORS_ORIGINS_VAR}" >> .env
          echo "MICRONAUT_ENVIRONMENTS=${MICRONAUT_ENVIRONMENTS_VAL}" >> .env
          echo "CDK_DEPLOY_ENVIRONMENT=${MICRONAUT_ENVIRONMENTS_VAL}" >> .env
          echo ".env file created successfully:"
          cat .env

      - name: Build backend app
        run: ./gradlew :backend:shadowJar

      - name: Install dependencies
        run: ./gradlew :infrastructure:build

      - name: Synthesize CDK stack
        run: |
          cd infrastructure
          cdk synth

      - name: Deploy CDK stack
        run: |
          cd infrastructure
          cdk deploy viewer-application-* --require-approval never
