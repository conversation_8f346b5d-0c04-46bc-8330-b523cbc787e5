package com.cha.model.dto

import com.cha.model.entity.MicroDetailEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a microbiology detail record for a patient, including order, result, and isolate details."
)
data class MicroDetail(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int?,
    @Schema(description = "System identifier for the microbiology record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the microbiology record.")
    val accountNumber: String?,
    @Schema(description = "Order code for the microbiology test.")
    val orderCode: String?,
    @Schema(description = "Description of the order code.")
    val orderCodeDescription: String?,
    @Schema(description = "Accession number for the microbiology test.")
    val accessionNumber: String?,
    @Schema(description = "Isolate identifier for the accession.")
    val accessionIsolate: String?,
    @Schema(description = "Result type code for the microbiology result.")
    val resultTypeCode: String?,
    @Schema(description = "Isolate number for the test.")
    val isolateNumber: Int?,
    @Schema(description = "Sequence number for the result.")
    val resultSequenceNumber: Int?,
    @Schema(description = "Text of the result.")
    val resultText: String?,
    @Schema(description = "Name of the person who released the result.")
    val releasedBy: String?,
    @Schema(description = "Date and time when the result was released.")
    val releaseDate: LocalDateTime?,
    @Schema(description = "Type of result (e.g., organism, comment, interpretation).")
    val resultType: String?,
    @Schema(description = "Name of the system/source where the result was recorded.")
    val systemName: String?,
    @Schema(description = "Indicates if the record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean? = false,
    @Schema(description = "Admission date associated with the record, if relevant.")
    val admitDate: LocalDateTime?,
)

fun MicroDetailEntity.toDto(): MicroDetail {
    return MicroDetail(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        orderCode = this.orderCode,
        resultSequenceNumber = this.resultSequenceNumber,
        resultText = this.resultText,
        releasedBy = this.releasedBy,
        releaseDate = releaseDate,
        resultType = this.resultType,
        orderCodeDescription = this.orderCodeDescription,
        accessionNumber = this.accessionNumber,
        accessionIsolate = this.accessionIsolate,
        isolateNumber = this.isolateNumber,
        resultTypeCode = this.resultTypeCode,
        systemName = this.systemName,
        securityLevel = this.securityLevel,
        admitDate = this.admitDate
    )
}