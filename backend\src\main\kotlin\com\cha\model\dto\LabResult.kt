package com.cha.model.dto

import com.cha.model.entity.LabResultEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a laboratory result for a patient, including test details, result values, and relevant timestamps."
)
data class LabResult(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int?,
    @Schema(description = "System identifier for the lab result record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the lab result.")
    val accountNumber: String?,
    @Schema(description = "Accession number for the lab test.")
    val accession: String?,
    @Schema(description = "Order code for the lab test.")
    val orderCode: String?,
    @Schema(description = "Sequence number for the lab result.")
    val sequence: Short?,
    @Schema(description = "Abbreviation for the lab test.")
    val testAbbr: String?,
    @Schema(description = "Description of the lab test.")
    val testDescription: String?,
    @Schema(description = "Numeric value of the lab result, if applicable.")
    val numericLabResult: String?,
    @Schema(description = "Units of measurement for the lab result.")
    val units: String?,
    @Schema(description = "Status of the lab result (e.g., final, preliminary).")
    val status: String?,
    @Schema(description = "Result flag indicating abnormal or critical results.")
    val resultFlag: String?,
    @Schema(description = "Abnormal flag for the lab result.")
    val abnFlag: String?,
    @Schema(description = "Name of the person who released the result.")
    val releasedBy: String?,
    @Schema(description = "Date and time when the service was provided.")
    val serviceDatetime: LocalDateTime?,
    @Schema(description = "Date and time when the specimen was collected.")
    val collectedDatetime: LocalDateTime?,
    @Schema(description = "Date and time when the specimen was received by the lab.")
    val receivedDatetime: LocalDateTime?,
    @Schema(description = "Date and time when the result was released.")
    val releasedDatetime: LocalDateTime?,
    @Schema(description = "Description of the lab order.")
    val orderDescription: String?,
    @Schema(description = "Name of the system/source where the lab result was recorded.")
    val systemName: String?,
    @Schema(description = "Indicates if the lab result record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean?,
)

fun LabResultEntity.toDto(): LabResult {

    return LabResult(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        accession = this.accession,
        orderCode = this.orderCode,
        sequence = this.sequence,
        testAbbr = this.testAbbr,
        testDescription = this.testDescription,
        numericLabResult = this.numericLabResult,
        units = this.units,
        status = this.status,
        resultFlag = this.resultFlag,
        abnFlag = this.abnFlag,
        releasedBy = this.releasedBy,
        serviceDatetime = this.serviceDatetime,
        collectedDatetime = this.collectedDatetime,
        receivedDatetime = this.receivedDatetime,
        releasedDatetime = this.releasedDatetime,
        orderDescription = this.orderDescription,
        systemName = this.systemName,
        securityLevel = this.securityLevel
    )
}