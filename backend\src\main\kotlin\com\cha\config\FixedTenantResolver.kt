package com.cha.config

import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Requires
import io.micronaut.multitenancy.tenantresolver.TenantResolver
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.io.Serializable
import io.micronaut.multitenancy.tenantresolver.FixedTenantResolver as MicronautFixedTenantResolver

@Singleton
@Primary
@Named("projectTenantResolver")
@Replaces(bean = MicronautFixedTenantResolver::class)
@Requires(property = "micronaut.multitenancy.fixed.enabled", value = "true")
class FixedTenantResolver : TenantResolver {
    override fun resolveTenantIdentifier(): Serializable {
        return "lrh"
    }
}
