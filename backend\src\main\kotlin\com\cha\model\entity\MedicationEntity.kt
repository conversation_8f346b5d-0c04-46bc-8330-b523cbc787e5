package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime // Added

@MappedEntity("medications")
data class MedicationEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val brandName: String?,
    val genericName: String?,
    val dosage: String?,
    val dosageForm: String?,
    val frequency: String?,
    val startDatetime: LocalDateTime?,
    val stopDatetime: LocalDateTime?,
    val caregiver: String?,
    val systemName: String?,
    val securityLevel: Boolean? = false,
    val admitDate: LocalDateTime?
)
