package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.Rectangle
import com.lowagie.text.pdf.PdfPCell
import com.lowagie.text.pdf.PdfPTable

private fun addInvoiceNotesSection(sections: VisitSectionsViewModel?, document: Document) {
    sections?.financial?.invoiceNotes?.let { notes ->
        document.add(createSectionTitle("Invoice Notes"))
        if (notes.isNotEmpty()) {
            notes.forEach { note ->
                val noteTable = PdfPTable(1)
                noteTable.widthPercentage = 100f
                setupDataItemTable(noteTable)

                val headerText = "Note on ${note.noteDate?.let { formatDateTime(it) }}"
                noteTable.addCell(createDataItemHeaderCell(headerText, 1))

                val noteCell = PdfPCell(Paragraph(note.patientNote, FONT_BODY))
                noteCell.border = Rectangle.NO_BORDER
                noteCell.setPadding(5f)
                noteTable.addCell(noteCell)

                document.add(noteTable)
            }
        } else {
            val paragraph = Paragraph("No invoice notes recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}