package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("invoices")
data class InvoiceEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val invoiceNumber: String?,
    val arStatus: String?,
    val billingDate: LocalDateTime?,
    val serviceFromDate: LocalDateTime?,
    val serviceThruDate: LocalDateTime?,
    val totalBilled: Long?,
    val insurancePayments: Long?,
    val coinsurancePayments: Long?,
    val patientPayments: Long?,
    val patientPaymentAmount: Long?,
    val patientAdjustmentAmount: Long?,
    val insurancePaymentAmount: Long?,
    val insuranceAdjustmentAmount: Long?,
    val insurancePlan: String?,
    val balance: Long?,
    val systemName: String?,
    val securityLevel: Boolean? = false,
    val admitDate: LocalDateTime?,
)
