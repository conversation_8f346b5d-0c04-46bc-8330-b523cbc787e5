package com.cha.model.dto

import com.cha.model.entity.AlertEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(description = "Patient alert information")
data class Alert(
    @Schema(description = "Patient's Enterprise Master Patient Index", example = "12345")
    val empi: Int?,
    @Schema(description = "System identifier", example = "1")
    val systemId: Int?,
    @Schema(description = "Medical Record Number", example = "MRN123456")
    val mrn: String?,
    @Schema(description = "Account number", example = "ACC789012")
    val accountNumber: String?,
    @Schema(description = "Alert code", example = "ALLERGY")
    val alertCode: String?,
    @Schema(description = "Alert description", example = "Penicillin allergy - severe reaction")
    val alertDescription: String?,
    @Schema(description = "Alert date", example = "2024-01-15T10:30:00")
    val alertDate: LocalDateTime?,
    @Schema(description = "System name where alert originates", example = "HIS")
    val systemName: String?,
    @Schema(description = "Admission date", example = "2024-01-15T08:00:00")
    val admitDate: LocalDateTime?,
    @Schema(description = "Security level flag", example = "false")
    val securityLevel: Boolean?
)

fun AlertEntity.toDto(): Alert {
    return Alert(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        alertCode = this.alertCode,
        alertDescription = this.alertDescription,
        alertDate = this.alertDate,
        systemName = this.systemName,
        admitDate = this.admitDate,
        securityLevel = this.securityLevel
    )
}
