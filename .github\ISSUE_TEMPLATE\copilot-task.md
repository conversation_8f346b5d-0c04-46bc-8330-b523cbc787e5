---
name: <PERSON><PERSON>lot Task
about: Create task for Co<PERSON>lot Agent to complete
title: ''
labels: ''
assignees: ''

---

Following GitHub Copilot's best practices for working on tasks, ensure all issues include:

Clear Problem Statement: Describe what needs to be built or fixed
Acceptance Criteria: Define what "done" looks like
Context: Provide relevant background information
Scope Boundaries: Clearly define what is and isn't included
Example of Well-Scoped Issue:
```
Title: Add patient document search functionality to dashboard

Description:
Users need to search for patient documents by document type and date range.

Acceptance Criteria:
- [ ] Add search form with document type dropdown and date range picker
- [ ] Integrate with DocumentsService API
- [ ] Display results in paginated table
- [ ] Handle loading and error states
- [ ] Include unit tests for new components

Context:
- Use existing PatientDocumentsTable component as reference
- Follow existing API patterns in src/service/DocumentsService.tsx
- Maintain consistent styling with other dashboard components

Out of Scope:
- Document preview functionality
- Bulk document operations
```
