package com.cha.model.auth

import io.micronaut.core.annotation.Introspected
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Email

@Introspected
@Serdeable
@Schema(name = "UpdateUserRequest", description = "User update request data")
data class UpdateUserRequest(
    @field:Email
    @Schema(description = "Email address of the user", example = "<EMAIL>")
    val email: String? = null,
    @Schema(description = "First name of the user", example = "<PERSON>")
    val firstName: String? = null,
    @Schema(description = "Last name of the user", example = "Doe")
    val lastName: String? = null,
    @Schema(description = "URL to profile picture", example = "https://example.com/profile.jpg")
    val pictureUrl: String? = null,
    @Schema(description = "User roles to assign", example = "[\"FULL_ACCESS\", \"ADMIN\"]")
    val roles: List<String>? = null
)