package com.cha.model.dto

import com.cha.model.entity.HomeMedicationEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a home medication record for a patient, including drug details, dosage, and administration information."
)
data class HomeMedication(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int?,
    @Schema(description = "System identifier for the home medication record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the home medication record.")
    val accountNumber: String?,
    @Schema(description = "Name of the medication.")
    val medication: String?,
    @Schema(description = "Dosage of the medication.")
    val dosage: String?,
    @Schema(description = "Route of administration (e.g., oral, IV).")
    val route: String?,
    @Schema(description = "Frequency of administration.")
    val frequency: String?,
    @Schema(description = "Start date of the medication.")
    val startDate: LocalDateTime?,
    @Schema(description = "End date of the medication, if applicable.")
    val endDate: LocalDateTime?,
    @Schema(description = "Name of the system/source where the medication was recorded.")
    val systemName: String?,
    @Schema(description = "Indicates if the medication record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean?,
    @Schema(description = "Admission date associated with the medication record, if relevant.")
    val admitDate: LocalDateTime?
)

fun HomeMedicationEntity.toDto(): HomeMedication {
    return HomeMedication(
        systemId = this.systemId,
        empi = this.empi,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        medication = this.brandName,
        dosage = this.dosage,
        route = this.dosageForm,
        frequency = this.frequency,
        startDate = this.startDatetime,
        endDate = this.stopDatetime,
        systemName = this.systemName,
        securityLevel = this.securityLevel,
        admitDate = this.admitDate
    )
}
