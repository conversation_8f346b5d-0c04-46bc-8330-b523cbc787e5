## Project Overview

This project is a Kotlin-based application with a multi-module Gradle setup. It consists of a backend service and an
infrastructure-as-code (IaC) component.

- **`backend`**: A Micronaut application responsible for the core application logic.
- **`infrastructure`**: Manages AWS infrastructure using AWS CDK.

The project utilizes Java 21 and Kotlin.

## Key Technologies

### Backend (`backend` module)

- **Framework**: Micronaut
- **Language**: Kotlin
- **Asynchronous Programming**: Kotlin Coroutines
- **Database**: JDBC (MSSQL) with Micronaut Data and Liquibase for migrations.
- **API**: RESTful APIs, documented with OpenAPI.
- **Security**: Micronaut Security (JWT)
- **AWS Integration**: AWS SDK v2, Micronaut AWS (CloudWatch Logging, Secrets Manager, Parameter Store), Cognito, S3.
- **Serialization**: Jackson
- **Templating**: Handlebars for views.
- **Logging**: SLF4J with Logback, Kotlin Logging.
- **Metrics**: Micrometer (StatsD, CloudWatch), Micronaut Management.
- **Build**: Gradle with Shadow JAR for packaging.

### Infrastructure (`infrastructure` module)

- **Framework**: AWS CDK (Cloud Development Kit)
- **Language**: Kotlin
- **Linting/Best Practices**: CDK Nag
- **Build**: Gradle

### General

- **Build Tool**: Gradle
- **Java Version**: 21
- **Kotlin Version**: (Refer to `build.gradle.kts` or `libs.versions.toml`)
- **Environment Configuration**: Uses a `.env` file at the project root for local development settings, which are loaded
  as system properties and environment variables for the `run` tasks in both `backend` and `infrastructure` modules.

## Important Files & Directories

- **`/build.gradle.kts`**: Root Gradle build file, defines top-level plugins.
- **`/gradle/libs.versions.toml`**: Defines dependency versions.
- **`/settings.gradle.kts`**: Gradle settings file, defines included modules.
- **`/backend/build.gradle.kts`**: Build configuration for the backend Micronaut application.
- **`/backend/src/main/kotlin/com/cha/ApplicationKt.kt`**: Main entry point for the backend application.
- **`/backend/src/main/resources/application.yml`**: Micronaut application configuration (if present).
- **`/infrastructure/build.gradle.kts`**: Build configuration for the AWS CDK infrastructure.
- **`/infrastructure/src/main/kotlin/com/cha/infrastructure/MainKt.kt`**: Main entry point for deploying CDK stacks.
- **`/infrastructure/cdk.json`**: CDK toolkit configuration file.
- **`.env`**: (At project root, not checked into VCS) Contains environment-specific variables for local development.
- **`/open-api.yml`**: OpenAPI specification file.

## Coding Conventions & Best Practices

- **Kotlin**: Follow standard Kotlin coding conventions (https://kotlinlang.org/docs/coding-conventions.html).
- **Micronaut**:
    - Utilize dependency injection.
    - Prefer constructor injection.
    - Use Micronaut's AOP for cross-cutting concerns.
    - Follow best practices for reactive programming when using Project Reactor or Kotlin Coroutines.
- **AWS CDK**:
    - Define stacks and constructs clearly.
    - Use CDK Nag for security and best practice checks (`io.github.cdklabs:cdknag`).
    - Parameterize stacks where appropriate (e.g., for different environments).
- **Error Handling**: Use Micronaut Problem JSON for consistent error responses in the backend.
- **Testing**: Write unit tests (JUnit 5 is configured).
- **Immutability**: Prefer immutable data structures where possible.
- **Logging**: Use structured logging and ensure sensitive information is not logged.
- **Security**:
    - Be mindful of security best practices, especially when dealing with AWS services and credentials.
    - Validate inputs.
    - Follow Micronaut Security guidelines.

## Testing Conventions & Design Patterns

### Framework & Dependencies

- **Testing Framework**: JUnit 5 with Micronaut Test extensions (preferred over Kotest due to better Micronaut integration)
- **HTTP Client**: Micronaut HTTP Client for integration tests
- **Test Database**: H2 in-memory database for tests requiring data persistence
- **Build Configuration**: Tests run with JUnit Platform, targeting JVM 21

### Test Design Principles

1. **Prefer Fakes Over Mocks**: Use concrete fake implementations instead of mocking libraries like MockK
   - Example: `FakePatientService` with in-memory storage instead of mocked PatientService
   - Benefits: Better refactoring safety, clearer test failures, easier debugging

2. **Test Isolation**: Each test should be independent and not rely on order of execution
   - Use `@BeforeEach` to set up fresh state
   - Use separate test database instances or in-memory data structures

3. **Descriptive Test Names**: Use clear, behavior-focused test names
   ```kotlin
   @Test
   fun `should return patient by EMPI when patient exists`()
   
   @Test 
   fun `should throw exception when patient not found`()
   ```

### Test Types & Patterns

#### Integration Tests with Micronaut Test

Use `@MicronautTest` for full application context testing:

```kotlin
@MicronautTest(environments = ["test"])
class HealthEndpointTest(
    @Client("/") private val client: HttpClient
) : TestPropertyProvider {
    
    @Test
    fun `health endpoint should return UP status`() {
        val response = client.toBlocking().exchange(
            HttpRequest.GET<Any>("/api/health"), 
            Map::class.java
        )
        assertEquals(HttpStatus.OK, response.status)
        assertEquals("UP", response.body()!!["status"])
    }
    
    override fun getProperties(): Map<String, String> {
        return mapOf(
            "micronaut.server.port" to "-1", // Random port
            "datasources.lrh.enabled" to "false", // Disable real database
            "datasources.hhs.enabled" to "false"
        )
    }
}
```

#### Unit Tests with Fake Services

Create concrete fake implementations for external dependencies:

```kotlin
@Singleton // For dependency injection in tests
class FakePatientService {
    private val patients = ConcurrentHashMap<Int, PatientData>()
    
    init {
        seedTestData() // Pre-populate with known test data
    }
    
    fun getPatient(empi: Int): Patient {
        return patients[empi]?.patient 
            ?: throw NoSuchElementException("Patient not found with EMPI: $empi")
    }
    
    fun addPatient(patient: Patient) {
        // Allow tests to add custom data
    }
}
```

#### Test Configuration

1. **Test Environment Profile**: Use `application-test.yml` to disable external dependencies
   ```yaml
   datasources:
     lrh:
       enabled: false
     hhs:
       enabled: false
   
   aws:
     secretsmanager:
       enabled: false
   ```

2. **Test Property Providers**: Override configuration in individual test classes
   ```kotlin
   override fun getProperties(): Map<String, String> {
       return mapOf(
           "micronaut.server.port" to "-1",
           "datasources.lrh.enabled" to "false"
       )
   }
   ```

### Fake Service Guidelines

1. **Location**: Place fake implementations in `com.cha.fake` package
2. **Naming**: Use `Fake` prefix (e.g., `FakePatientService`)
3. **Data Seeding**: Provide realistic test data in constructor/init blocks
4. **Extensibility**: Allow tests to add custom data via public methods
5. **Behavior Simulation**: Mirror real service behavior including error conditions

### CI/CD Testing

- **GitHub Actions**: Tests run automatically on pull requests
- **Test Reports**: JUnit XML reports uploaded as artifacts
- **Coverage**: Focus on business logic and integration points
- **Performance**: Keep test suite under 2 minutes total execution time

### Example Test Patterns

**Testing API Endpoints:**
```kotlin
@MicronautTest(environments = ["test"])
class PatientControllerTest(@Client("/") private val client: HttpClient) {
    @Test
    fun `should return patient when valid EMPI provided`() {
        val response = client.toBlocking().exchange(
            HttpRequest.GET<Any>("/api/patients/1001"),
            Patient::class.java
        )
        assertEquals(HttpStatus.OK, response.status)
    }
}
```

**Testing Service Logic:**
```kotlin
class PatientServiceTest {
    private lateinit var fakePatientService: FakePatientService
    
    @BeforeEach
    fun setup() {
        fakePatientService = FakePatientService()
    }
    
    @Test
    fun `should search patients by name`() {
        val results = fakePatientService.searchPatients("Doe", SearchType.NAME, Pageable.from(0, 10))
        assertEquals(1, results.totalSize)
    }
}
```

## Environment Setup (Local Development)

- A `.env` file in the project root directory is used to supply environment variables and system properties for the
  `run` tasks of both the `backend` and `infrastructure` modules.
- Ensure this file is created locally with the necessary configurations (e.g., AWS settings, API keys).
- Example content for `.env`:
  ```env
  AWS_REGION="us-east-1"
  # Add other necessary variables
  ```