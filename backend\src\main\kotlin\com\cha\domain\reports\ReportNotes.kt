package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable

private fun addNotesSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.notes?.let { notes ->
        document.add(createSectionTitle("Notes"))
        if (notes.isNotEmpty()) {
            notes.forEach { note ->
                val noteTable = PdfPTable(2)
                noteTable.widthPercentage = 100f
                setupDataItemTable(noteTable)
                noteTable.addCell(
                    createLabelValueCell(
                        "Created:",
                        note.noteDatetime?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                noteTable.addCell(createLabelValueCell("Note:", note.noteText, inTable = true))
                document.add(noteTable)
            }
        } else {
            val paragraph = Paragraph("No procedures recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}