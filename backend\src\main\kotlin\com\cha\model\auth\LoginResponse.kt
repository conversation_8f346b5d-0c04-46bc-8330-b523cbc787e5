package com.cha.model.auth

import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema

@Schema(description = "Response returned after successful authentication")
@Serdeable
data class LoginResponse(
    @field:Schema(
        description = "JWT access token used for authenticating requests",
        example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    )
    val accessToken: String,

    @field:Schema(
        description = "Optional refresh token that can be used to obtain a new access token",
        nullable = true,
        example = "rtok_abc123def456"
    )
    val refreshToken: String?, // Refresh token might not always be returned

    @field:Schema(description = "Number of seconds until the access token expires", example = "3600")
    val expiresIn: Int,

    @field:Schema(description = "Type of authentication token", defaultValue = "Bearer", example = "Bearer")
    val tokenType: String = "Bearer",

    @field:Schema(description = "User information associated with the authentication token", nullable = true)
    val user: User?,
)
