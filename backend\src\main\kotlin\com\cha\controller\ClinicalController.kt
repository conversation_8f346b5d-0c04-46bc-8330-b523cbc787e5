package com.cha.controller

import com.cha.domain.ClinicalService
import com.cha.model.dto.Alert
import com.cha.model.dto.Allergy
import com.cha.model.dto.Diagnosis
import com.cha.model.dto.HomeMedication
import com.cha.model.dto.Immunization
import com.cha.model.dto.LabResult
import com.cha.model.dto.LabResultReference
import com.cha.model.dto.Medication
import com.cha.model.dto.MicroComment
import com.cha.model.dto.MicroDetail
import com.cha.model.dto.MicroSuscept
import com.cha.model.dto.Notes
import com.cha.model.dto.Orders
import com.cha.model.dto.Procedure
import com.cha.model.dto.TranscriptionResult
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.zalando.problem.Problem

@Tag(
    name = "Clinical Data",
    description = "Endpoints for retrieving a patient's clinical information, including allergies, diagnoses, medications, labs, procedures, and more."
)
@Secured(SecurityRule.IS_AUTHENTICATED)
@Controller("/v1/patients")
class ClinicalController(
    private val clinicalService: ClinicalService,
) {
    @Operation(
        summary = "Get patient allergies",
        description = "Retrieves a paginated list of allergies for the specified patient (by EMPI). Supports pagination."
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "List of patient allergies retrieved successfully",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Page::class)
            )]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Unauthorized - Authentication required",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "404",
            description = "Patient not found",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Get("/{empi}/allergies")
    suspend fun getPatientAllergies(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<Allergy> {
        return clinicalService.getPatientAllergies(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient diagnoses",
        description = "Retrieves a paginated list of diagnoses for the specified patient (by EMPI). Supports pagination."
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "List of patient diagnoses retrieved successfully",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Page::class)
            )]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Unauthorized - Authentication required",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "404",
            description = "Patient not found",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Get("/{empi}/diagnosis")
    suspend fun getPatientDiagnosis(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<Diagnosis> {
        return clinicalService.getPatientDiagnosis(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient immunizations",
        description = "Retrieves a paginated list of immunizations for the specified patient (by EMPI). Supports pagination."
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "List of patient immunizations retrieved successfully",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Page::class)
            )]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Unauthorized - Authentication required",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "404",
            description = "Patient not found",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Problem::class)
            )]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(
                mediaType = "application/json",
                schema = Schema(implementation = Problem::class)
            )]
        )
    )
    @Get("/{empi}/immunizations")
    suspend fun getPatientImmunizations(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<Immunization> {
        return clinicalService.getPatientImmunizations(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient lab results",
        description = "Retrieves a paginated list of lab results for the specified patient (by EMPI). Supports pagination."
    )
    @Get("/{empi}/labresult")
    suspend fun getPatientLabResult(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<LabResult> {
        return clinicalService.getPatientLabResults(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient home medications",
        description = "Retrieves a paginated list of home medications for the specified patient (by EMPI). Supports pagination."
    )
    @Get("/{empi}/home-medications")
    suspend fun getPatientHomeMedications(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<HomeMedication> {
        return clinicalService.getPatientHomeMedications(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient medications",
        description = "Retrieves a paginated list of medications for the specified patient (by EMPI). Supports pagination."
    )
    @Get("/{empi}/medications")
    suspend fun getPatientMedications(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<Medication> {
        return clinicalService.getPatientMedications(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient procedures",
        description = "Retrieves a paginated list of procedures for the specified patient (by EMPI). Supports pagination."
    )
    @Get("/{empi}/procedure")
    suspend fun getPatientProcedure(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<Procedure> {
        return clinicalService.getPatientProcedure(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient orders",
        description = "Retrieves a paginated list of orders for the specified patient (by EMPI). Supports pagination."
    )
    @Get("/{empi}/orders")
    suspend fun getPatientOrders(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<Orders> {
        return clinicalService.getPatientOrders(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient notes",
        description = "Retrieves a paginated list of clinical notes for the specified patient (by EMPI). Supports pagination."
    )
    @Get("/{empi}/notes")
    suspend fun getPatientNotes(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<Notes> {
        return clinicalService.getPatientNotes(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient microbiology susceptibilities",
        description = "Retrieves a paginated list of microbiology susceptibilities for the specified patient (by EMPI). Supports pagination."
    )
    @Get("/{empi}/micro-suscept")
    suspend fun getPatientMicroSuscept(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<MicroSuscept> {
        return clinicalService.getPatientMicroSuscept(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient microbiology comments",
        description = "Retrieves a paginated list of microbiology comments for the specified patient (by EMPI). Supports pagination."
    )
    @Get("/{empi}/micro-comment")
    suspend fun getPatientMicroComment(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<MicroComment> {
        return clinicalService.getPatientMicroComment(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient microbiology details",
        description = "Retrieves a paginated list of microbiology details for the specified patient (by EMPI). Supports pagination."
    )
    @Get("/{empi}/micro-detail")
    suspend fun getPatientMicroDetail(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<MicroDetail> {
        return clinicalService.getPatientMicroDetail(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient alerts",
        description = "Retrieves a paginated list of alerts for the specified patient (by EMPI). Supports pagination."
    )
    @Get("/{empi}/alerts")
    suspend fun getPatientAlerts(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<Alert> {
        return clinicalService.getPatientAlerts(empi, null, null, pageable)
    }

    @Operation(
        summary = "Get patient reference lab results",
        description = "Retrieves a paginated list of reference lab results for the specified patient (by EMPI). Supports pagination."
    )
    @Get("/{empi}/reference-lab-result")
    suspend fun getPatientReferenceLabResult(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<LabResultReference> {
        return clinicalService.getPatientReferenceLabResult(empi, null, null, pageable)
    }

    @Get("/{empi}/transcription-results")
    suspend fun getPatientTranscriptionResults(
        @Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int,
        @Parameter(required = false) pageable: Pageable,
    ): Page<TranscriptionResult> {
        return clinicalService.getPatientTranscriptionResults(empi, null, null, pageable)
    }
}
