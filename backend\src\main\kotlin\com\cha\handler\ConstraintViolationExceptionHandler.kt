package com.cha.handler

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.context.annotation.Replaces
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Produces
import io.micronaut.http.server.exceptions.ExceptionHandler
import io.micronaut.problem.HttpStatusType
import jakarta.inject.Singleton
import jakarta.validation.ConstraintViolationException
import org.slf4j.MDC
import org.zalando.problem.Problem
import org.zalando.problem.ThrowableProblem
import java.net.URI

private val logger = KotlinLogging.logger {}

/**
 * Custom exception handler for ConstraintViolationException. Translates
 * validation failures into a 400 Bad Request response with Problem JSON
 * format (RFC 7807).
 */
@Produces
@Singleton
@Replaces(ConstraintViolationExceptionHandler::class)
class ConstraintViolationExceptionHandler :
    ExceptionHandler<ConstraintViolationException, HttpResponse<ThrowableProblem>> {

    companion object {
        private const val TRACE_ID_MDC_KEY = "traceId"
        private const val PROBLEM_BASE_URI = "https://api.viewer.com/problems"
    }

    override fun handle(
        request: HttpRequest<*>,
        exception: ConstraintViolationException,
    ): HttpResponse<ThrowableProblem> {
        val traceId = MDC.get(TRACE_ID_MDC_KEY)
        logger.warn { "Validation failed: ${exception.message} (traceId: $traceId)" }

        // Extract field errors from constraint violations
        val fieldErrors = exception.constraintViolations.associate { violation ->
            val propertyPath = violation.propertyPath.toString()
            val fieldName = propertyPath.substringAfterLast(".", propertyPath)
            fieldName to violation.message
        }

        val problem = Problem.builder()
            .withType(URI.create("$PROBLEM_BASE_URI/validation-error"))
            .withTitle("Validation Error")
            .withStatus(HttpStatusType(HttpStatus.BAD_REQUEST))
            .withDetail("The request contains invalid parameters")
            .with("fieldErrors", fieldErrors)
            .with("traceId", traceId)
            .build()

        return HttpResponse.badRequest<ThrowableProblem>().body(problem)
    }
}
