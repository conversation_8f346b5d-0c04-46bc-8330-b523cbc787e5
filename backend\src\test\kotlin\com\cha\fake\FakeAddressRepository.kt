package com.cha.fake

import com.cha.model.entity.AddressEntity
import com.cha.repository.AddressRepository
import kotlinx.coroutines.flow.flowOf

class FakeAddressRepository : AddressRepository {
    override suspend fun findByEmpi(empi: Int) = emptyList<AddressEntity>()
    override suspend fun count() = 0L
    override suspend fun delete(entity: AddressEntity) = 0
    override suspend fun deleteAll() = 0
    override suspend fun deleteAll(entities: Iterable<AddressEntity>) = 0
    override suspend fun deleteById(id: Long) = 0
    override suspend fun existsById(id: Long) = false
    override fun findAll() = flowOf<AddressEntity>()
    override suspend fun findById(id: Long) = null
    override suspend fun <S : AddressEntity> save(entity: S) = entity
    override fun <S : AddressEntity> saveAll(entities: Iterable<S>) = flowOf<S>()
    override suspend fun <S : AddressEntity> update(entity: S) = entity
    override fun <S : AddressEntity> updateAll(entities: Iterable<S>) = flowOf<S>()
}
