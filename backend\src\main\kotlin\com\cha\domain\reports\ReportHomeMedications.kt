package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable

private fun addHomeMedicationsSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.homeMedications?.let { homeMedications ->
        document.add(createSectionTitle("Home Medications"))
        if (homeMedications.isNotEmpty()) {
            homeMedications.forEach { homeMed ->
                val homeMedTable = PdfPTable(2)
                homeMedTable.widthPercentage = 100f
                setupDataItemTable(homeMedTable)

                val headerText = homeMed.medication ?: "Medication"
                homeMedTable.addCell(createDataItemHeaderCell(headerText, 2))

                homeMedTable.addCell(createLabelValueCell("Dosage:", homeMed.dosage, inTable = true))
                homeMedTable.addCell(createLabelValueCell("Route:", homeMed.route, inTable = true))
                homeMedTable.addCell(createLabelValueCell("Frequency:", homeMed.frequency, inTable = true))
                homeMedTable.addCell(
                    createLabelValueCell(
                        "Start Date:",
                        homeMed.startDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                homeMedTable.addCell(
                    createLabelValueCell(
                        "End Date:",
                        homeMed.endDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )

                document.add(homeMedTable)
            }
        } else {
            val paragraph = Paragraph("No home medications recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}