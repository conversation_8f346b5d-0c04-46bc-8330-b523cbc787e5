package com.cha.domain

import com.cha.model.dto.Bill
import com.cha.model.dto.Insurance
import com.cha.model.dto.Invoice
import com.cha.model.dto.InvoiceNote
import com.cha.model.dto.toDto
import com.cha.repository.BillRepository
import com.cha.repository.InsuranceRepository
import com.cha.repository.InvoiceNotesRepository
import com.cha.repository.InvoiceRepository
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import jakarta.inject.Singleton

@Singleton
class FinancialService(
    private val insuranceRepository: InsuranceRepository,
    private val invoiceNoteRepository: InvoiceNotesRepository,
    private val invoiceRepository: InvoiceRepository,
    private val billRepository: BillRepository,
) {

    suspend fun getPatientInsurance(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Insurance> {
        return insuranceRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientInvoiceNote(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<InvoiceNote> {
        return invoiceNoteRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientInvoice(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Invoice> {
        return invoiceRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getPatientBills(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Bill> {
        return billRepository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }
}