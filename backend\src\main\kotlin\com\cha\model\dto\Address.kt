package com.cha.model.dto

import com.cha.model.entity.AddressEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema

@Serdeable
data class Address(
    @Schema(description = "Street address", example = "123 Main St")
    val street: String?,
    @Schema(description = "City", example = "Boston")
    val city: String?,
    @Schema(description = "State", example = "MA")
    val state: String?,
    @Schema(description = "Zip code", example = "02118")
    val zipCode: String?,
    @Schema(description = "System ID the address belongs to", example = "3")
    val systemId: Int?,
)

/** Map an [AddressEntity] to an [Address] DTO. */
fun AddressEntity.toDto(): Address {
    return Address(
        street = this.address,
        city = this.city,
        state = this.state,
        zipCode = this.zipCode,
        systemId = this.systemId,
    )
}
