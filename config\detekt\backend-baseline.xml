<?xml version='1.0' encoding='UTF-8'?>
<SmellBaseline>
  <ManuallySuppressedIssues>
    <ID>LongParameterList:ReportsService.kt$ReportsService$( private val visitService: PatientVisitService, private val patientService: PatientService, private val clicnicalService: ClinicalService, private val financialService: FinancialService, private val documentsService: DocumentsService, private val hospitalService: HospitalService, private val s3Client: S3Client, @Value("\${aws.s3.bucket}") private val bucketName: String, @Value("\${aws.s3.prefixes.viewer-generated-reports}") private val viewerGeneratedDocsPrefix: String, private val tenantResolver: TenantResolver, private val reportPdfService: ReportPdfService, private val jobRepository: JobRepository, private val reportRepository: ReportRepository )</ID>
    <ID>LongParameterList:ReportsService.kt$ReportsService$( private val visitService: PatientVisitService, private val patientService: PatientService, private val clinicalService: ClinicalService, private val financialService: FinancialService, private val documentsService: DocumentsService, private val hospitalService: HospitalService, private val s3Client: S3Client, @Value("\${aws.s3.bucket}") private val bucketName: String, @Value("\${aws.s3.prefixes.viewer-generated-reports}") private val viewerGeneratedDocsPrefix: String, private val tenantResolver: TenantResolver, private val reportPdfService: ReportPdfService, private val jobRepository: JobRepository, private val reportRepository: ReportRepository )</ID>
  </ManuallySuppressedIssues>
  <CurrentIssues>
    <ID>CyclomaticComplexMethod:AuthService.kt$AuthService$suspend fun getUsers(pageable: CursoredPageable): HttpResponse&lt;CursoredPage&lt;User>></ID>
    <ID>CyclomaticComplexMethod:CognitoClaimsValidator.kt$CognitoClaimsValidator$fun extractUserInfoFromIdToken(idToken: String?): User</ID>
    <ID>CyclomaticComplexMethod:ReportsService.kt$ReportsService$private suspend fun collectClinicalData( empi: Int, mrn: String, visitId: String?, includeSections: Set&lt;ReportSections> ): ClinicalViewModel</ID>
    <ID>ForbiddenComment:AuthService.kt$AuthService$// TODO: add roles and provide them in user response.</ID>
    <ID>ForbiddenComment:CognitoClaimsValidator.kt$CognitoClaimsValidator$// TODO: Cognito does not provide created_at in ID token</ID>
    <ID>ForbiddenComment:Job.kt$Job$// Progress percentage (0-100) TODO: Implement progress tracking</ID>
    <ID>ForbiddenComment:JobsService.kt$JobsService$// TODO: Upload to S3 bucket for later retrieval and reference.</ID>
    <ID>ForbiddenComment:JobsService.kt$JobsService$// TODO: save the job metadata in a database instead of in-memory storage and tie it to the user</ID>
    <ID>ForbiddenComment:PatientRepository.kt$PatientRepository$// TODO: use Distinct</ID>
    <ID>ForbiddenComment:ReportPdfService.kt$ReportPdfService$// TODO: Implement similar loops and table-building logic for all other sections:</ID>
    <ID>ForbiddenComment:ReportRequest.kt$ReportOutputFormat$// TODO: support structured XML (CDA/FHIR) if advanced</ID>
    <ID>ForbiddenComment:ReportsController.kt$ReportsController$// TODO: STUB for cancelling a report generation job</ID>
    <ID>ForbiddenComment:ReportsService.kt$ReportsService$// TODO: set variables for report type like below.</ID>
    <ID>ForbiddenComment:ReportsService.kt$ReportsService$// TODO: tenant information should be fetched from the request context and passed into generateReport function.</ID>
    <ID>LongMethod:AuthService.kt$AuthService$suspend fun createUser(createUserRequest: CreateUserRequest): HttpResponse&lt;Any></ID>
    <ID>LongMethod:AuthService.kt$AuthService$suspend fun getUsers(pageable: CursoredPageable): HttpResponse&lt;CursoredPage&lt;User>></ID>
    <ID>LongMethod:ReportPdfService.kt$ReportPdfService$private fun addVisitSection(document: Document, visit: PatientVisit, sections: ReportSectionsViewModel?)</ID>
    <ID>LongMethod:ReportsService.kt$ReportsService$private suspend fun collectClinicalData( empi: Int, mrn: String, visitId: String?, includeSections: Set&lt;ReportSections> ): ClinicalViewModel</ID>
    <ID>LongParameterList:ClinicalService.kt$ClinicalService$( private val allergyRepository: AllergyRepository, private val diagnosisRepository: DiagnosisRepository, private val immunizationRepository: ImmunizationRepository, private val labResultRepository: LabResultRepository, private val medicationRepository: MedicationRepository, private val homeMedicationRepository: HomeMedicationRepository, private val orderRepository: OrderRepository, private val procedureRepository: ProcedureRepository, private val labOrderReferenceRepository: LabOrderReferenceRepository, private val labResultReferenceRepository: LabResultReferenceRepository, private val notesRepository: NotesRepository, private val microDetailRepository: MicroDetailRepository, private val microOrderRepository: MicroOrderRepository, private val microCommentRepository: MicroCommentRepository, private val microSusceptRepository: MicroSusceptRepository, private val alertRepository: AlertRepository, private val labOrderRepository: LabOrderRepository, )</ID>
    <ID>LongParameterList:Patient.kt$PatientBase$( @Schema(description = "Enterprise Master Patient Index", example = "1001") open val empi: Int?, @Schema(description = "First name of the patient", example = "John") open val firstName: String?, @Schema(description = "Last name of the patient", example = "Doe") open val lastName: String?, @Schema(description = "Middle name of the patient", example = "A.") open val middleName: String?, @Schema(description = "Sex of the patient", example = "Male") open val sex: String, @Schema(description = "Birthdate of the patient", example = "1980-01-01") open val birthdate: LocalDate?, @Schema(description = "List of systems (EMR sources) associated with the patient") open val systems: List&lt;System> )</ID>
    <ID>LongParameterList:PatientVisitService.kt$PatientVisitService$( empi: Int, mrn: String?, accountNumber: String?, startDate: LocalDate?, endDate: LocalDate?, pageable: Pageable )</ID>
    <ID>LongParameterList:ReportsService.kt$ReportsService$( empi: Int, mrn: String, visitId: String?, reportType: ReportType = ReportType.VISIT_SUMMARY, includeSections: Set&lt;ReportSections> = ReportSections.entries.toSet(), dateRange: DateRangeFilter?, authentication: Authentication, // todo get the user information from the request context. tenantId: String )</ID>
    <ID>LongParameterList:ReportsService.kt$ReportsService$( private val visitService: PatientVisitService, private val patientService: PatientService, private val clicnicalService: ClinicalService, private val financialService: FinancialService, private val documentsService: DocumentsService, private val tenantService: TenantService, private val s3Client: S3Client, @Value("\${aws.s3.bucket}") private val bucketName: String, private val reportPdfService: ReportPdfService )</ID>
    <ID>LongParameterList:VisitsRepository.kt$VisitsRepository$( empi: Int, mrn: String?, accountNumber: String?, startDate: LocalDate?, endDate: LocalDate?, pageable: Pageable )</ID>
    <ID>MagicNumber:CognitoClaimsValidator.kt$CognitoClaimsValidator$3</ID>
    <ID>MagicNumber:CognitoClaimsValidator.kt$CognitoClaimsValidator$4</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$100f</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$108</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$10f</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$114</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$126</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$12f</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$136</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$147</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$15f</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$164</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$16f</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$20f</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$228</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$22f</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$232</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$239</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$248</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$27</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$3f</ID>
    <ID>MagicNumber:ReportPdfService.kt$ReportPdfService$5f</ID>
    <ID>MatchingDeclarationName:Application.kt$Api</ID>
    <ID>MatchingDeclarationName:ReferenceLabOrder.kt$LabOrderReference</ID>
    <ID>MatchingDeclarationName:ReferenceLabResult.kt$LabResultReference</ID>
    <ID>MatchingDeclarationName:Visit.kt$PatientVisit</ID>
    <ID>MaxLineLength:Allergy.kt$Allergy$description = "Represents a clinical allergy record for a patient, including allergen, reaction, severity, and relevant dates."</ID>
    <ID>MaxLineLength:AuthController.kt$AuthController$// TODO add getUser that by default will return the authenticated user. Optionally for admin users they can pass in a specific user id to get details of another user.</ID>
    <ID>MaxLineLength:AuthService.kt$AuthService$challengeParameters?.forEach { (key, value) -> logger.debug { "${"Challenge Param: {}={}"} $key ${"***"}" } }</ID>
    <ID>MaxLineLength:AuthService.kt$AuthService$logger.info { "Attempting to retrieve users from user pool: $userPoolId with cursored pagination (size: ${pageable.size})" }</ID>
    <ID>MaxLineLength:AuthService.kt$AuthService$logger.info { "Successfully retrieved ${users.size} users. Has next page: ${response.paginationToken != null}" }</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$@Parameter(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.") @PathVariable empi: Int</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Endpoints for retrieving a patient's clinical information, including allergies, diagnoses, medications, labs, procedures, and more."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of allergies for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of clinical notes for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of diagnoses for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of home medications for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of immunizations for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of lab results for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of medications for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of microbiology comments for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of microbiology details for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of microbiology orders for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of microbiology susceptibilities for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of procedures for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of reference lab orders for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:ClinicalController.kt$ClinicalController$description = "Retrieves a paginated list of reference lab results for the specified patient (by EMPI). Supports pagination."</ID>
    <ID>MaxLineLength:CognitoClaimsValidator.kt$CognitoClaimsValidator$"JWT validation failed: Invalid audience. Expected audience $expectedAudience not found in actual list: $actualAudienceList. Token rejected."</ID>
    <ID>MaxLineLength:CognitoClaimsValidator.kt$CognitoClaimsValidator$"JWT validation failed: Invalid issuer. Expected: $expectedIssuer, Actual: $actualIssuer. Token rejected."</ID>
    <ID>MaxLineLength:CognitoClaimsValidator.kt$CognitoClaimsValidator$logger.warn { "JWT validation failed: Token expired at $expirationTime (current time: $now). Token rejected." }</ID>
    <ID>MaxLineLength:Diagnosis.kt$Diagnosis$description = "Represents a clinical diagnosis record for a patient, including ICD code, description, type, and relevant dates."</ID>
    <ID>MaxLineLength:DocumentsService.kt$DocumentsService$logger.info { "Successfully retrieved document: $objectKey, ETag: ${s3Response.eTag}, ContentType: $mediaType" }</ID>
    <ID>MaxLineLength:HomeMedication.kt$HomeMedication$description = "Represents a home medication record for a patient, including drug details, dosage, and administration information."</ID>
    <ID>MaxLineLength:Immunization.kt$Immunization$description = "Represents a clinical immunization record for a patient, including vaccine, manufacturer, lot, and administration dates."</ID>
    <ID>MaxLineLength:LabResult.kt$LabResult$description = "Represents a laboratory result for a patient, including test details, result values, and relevant timestamps."</ID>
    <ID>MaxLineLength:Medication.kt$Medication$description = "Represents a medication record for a patient, including drug details, dosage, route, and administration information."</ID>
    <ID>MaxLineLength:MicroComment.kt$MicroComment$description = "Represents a microbiology comment for a patient, including comment type, description, and entry details."</ID>
    <ID>MaxLineLength:MicroSuscept.kt$MicroSuscept$description = "Represents a microbiology susceptibility result for a patient, including antibiotic, result, and isolate details."</ID>
    <ID>MaxLineLength:NoSuchElementHandler.kt$NoSuchElementHandler$ExceptionHandler&lt;NoSuchElementException, HttpResponse&lt;ProblemDetail>></ID>
    <ID>MaxLineLength:Notes.kt$Notes$description = "Represents a clinical note for a patient, such as progress notes, discharge summaries, or other documentation."</ID>
    <ID>MaxLineLength:Orders.kt$Orders$description = "Represents a clinical order for a patient, such as lab, medication, or procedure orders. Includes order details and relevant dates."</ID>
    <ID>MaxLineLength:PatientsController.kt$PatientsController$description = "Retrieves a patient by their EMPI with detailed information including addresses and telephone numbers"</ID>
    <ID>MaxLineLength:Procedure.kt$Procedure$description = "Represents a clinical procedure record for a patient, including procedure code, description, and relevant dates."</ID>
    <ID>MaxLineLength:TenantService.kt$TenantService$// Fetches tenant infomation with the expectation there is a &lt;tenentId>.json file within the `src/main/resources/tenants/` directory of the project, which makes them available on the classpath.</ID>
    <ID>NewLineAtEndOfFile:AuthController.kt$com.cha.controller.AuthController.kt</ID>
    <ID>NewLineAtEndOfFile:AuthService.kt$com.cha.domain.AuthService.kt</ID>
    <ID>NewLineAtEndOfFile:Bill.kt$com.cha.model.dto.Bill.kt</ID>
    <ID>NewLineAtEndOfFile:BillEntity.kt$com.cha.model.entity.BillEntity.kt</ID>
    <ID>NewLineAtEndOfFile:CognitoClaimsValidator.kt$com.cha.security.CognitoClaimsValidator.kt</ID>
    <ID>NewLineAtEndOfFile:FinancialController.kt$com.cha.controller.FinancialController.kt</ID>
    <ID>NewLineAtEndOfFile:FinancialService.kt$com.cha.domain.FinancialService.kt</ID>
    <ID>NewLineAtEndOfFile:Invoice.kt$com.cha.model.dto.Invoice.kt</ID>
    <ID>NewLineAtEndOfFile:InvoiceNote.kt$com.cha.model.dto.InvoiceNote.kt</ID>
    <ID>NewLineAtEndOfFile:InvoiceNoteEntity.kt$com.cha.model.entity.InvoiceNoteEntity.kt</ID>
    <ID>NewLineAtEndOfFile:LabOrderRepository.kt$com.cha.repository.LabOrderRepository.kt</ID>
    <ID>NewLineAtEndOfFile:LabResult.kt$com.cha.model.dto.LabResult.kt</ID>
    <ID>NewLineAtEndOfFile:Medication.kt$com.cha.model.dto.Medication.kt</ID>
    <ID>NewLineAtEndOfFile:MicroComment.kt$com.cha.model.dto.MicroComment.kt</ID>
    <ID>NewLineAtEndOfFile:MicroDetail.kt$com.cha.model.dto.MicroDetail.kt</ID>
    <ID>NewLineAtEndOfFile:Notes.kt$com.cha.model.dto.Notes.kt</ID>
    <ID>NewLineAtEndOfFile:NotesRepository.kt$com.cha.repository.NotesRepository.kt</ID>
    <ID>NewLineAtEndOfFile:ReportRequest.kt$com.cha.model.dto.ReportRequest.kt</ID>
    <ID>NewLineAtEndOfFile:SubdomainTenantResolver.kt$com.cha.config.SubdomainTenantResolver.kt</ID>
    <ID>NewLineAtEndOfFile:TenantService.kt$com.cha.domain.TenantService.kt</ID>
    <ID>ReturnCount:AuthService.kt$AuthService$private fun handleCognitoResponse(response: AdminInitiateAuthResponse): HttpResponse&lt;Any></ID>
    <ID>ReturnCount:CognitoClaimsValidator.kt$CognitoClaimsValidator$fun extractUserInfoFromIdToken(idToken: String?): User</ID>
    <ID>ReturnCount:CognitoClaimsValidator.kt$CognitoClaimsValidator$override fun validate( claims: @NonNull Claims?, request: @Nullable HttpRequest&lt;*>? ): Boolean</ID>
    <ID>SpreadOperator:Application.kt$(*args)</ID>
    <ID>SwallowedException:AuthService.kt$AuthService$e: UserNotFoundException</ID>
    <ID>SwallowedException:AuthService.kt$AuthService$e: UsernameExistsException</ID>
    <ID>ThrowsCount:PatientInfo.kt$private fun createPatientInfoFromFields( fields: Map&lt;String, Any?>, addresses: List&lt;Address>, telephones: List&lt;Telephone> ): PatientInfo</ID>
    <ID>ThrowsCount:ReportsController.kt$ReportsController$@Get("/jobs/{jobId}/report") suspend fun getJobReport(@PathVariable jobId: String): HttpResponse&lt;*></ID>
    <ID>TooGenericExceptionCaught:AuthService.kt$AuthService$deleteEx: Exception</ID>
    <ID>TooGenericExceptionCaught:AuthService.kt$AuthService$e: Exception</ID>
    <ID>TooGenericExceptionCaught:CognitoClaimsValidator.kt$CognitoClaimsValidator$e: Exception</ID>
    <ID>TooGenericExceptionCaught:DocumentsService.kt$DocumentsService$e: Exception</ID>
    <ID>TooGenericExceptionCaught:JobsService.kt$JobsService$e: Exception</ID>
    <ID>TooGenericExceptionThrown:DocumentsService.kt$DocumentsService$throw RuntimeException( "Failed to retrieve document: $filenameWithinTenant. Cause: ${e.message}", e )</ID>
    <ID>TooGenericExceptionThrown:DocumentsService.kt$DocumentsService$throw RuntimeException("S3 object body is null for key: $objectKey")</ID>
    <ID>TooManyFunctions:ClinicalController.kt$ClinicalController</ID>
    <ID>TooManyFunctions:ClinicalService.kt$ClinicalService</ID>
    <ID>TooManyFunctions:ReportPdfService.kt$ReportPdfService</ID>
    <ID>UnusedParameter:PatientService.kt$PatientService$type: SearchType</ID>
    <ID>UnusedParameter:ReportsService.kt$ReportsService$tenantId: String</ID>
    <ID>UnusedParameter:TenantController.kt$TenantController$principal: Authentication?</ID>
    <ID>UnusedParameter:TenantController.kt$TenantController$request: HttpRequest&lt;*></ID>
    <ID>UnusedPrivateProperty:PatientInfo.kt$val active = when (val activeValue = fields["active"]) { is Boolean -> activeValue is Int -> activeValue != 0 null -> null else -> throw IllegalArgumentException("Active field must be a Boolean or Int") }</ID>
    <ID>UnusedPrivateProperty:ReportsService.kt$ReportsService$@Value("\${aws.s3.bucket}") private val bucketName: String</ID>
    <ID>UnusedPrivateProperty:ReportsService.kt$ReportsService$private val documentsService: DocumentsService</ID>
    <ID>UnusedPrivateProperty:ReportsService.kt$ReportsService$private val s3Client: S3Client</ID>
    <ID>UseRequire:PatientInfo.kt$throw IllegalArgumentException("Systems list contains non-System elements")</ID>
    <ID>UseRequire:ReportsService.kt$ReportsService$throw IllegalArgumentException("Visit ID must be provided for a visit summary report")</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$// --- Define Fonts --- // Note: You must place these .ttf files in your project's resources folder (e.g., src/main/resources/fonts/) private val FONT_PATH_REGULAR = "/fonts/DMSans-Regular.ttf"</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$// --- Define Formatters --- private val DATE_FORMATTER = DateTimeFormatter.ofPattern("MMMM dd, yyyy", Locale.US)</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$// --- Define Theme Colors (converted from OKLCH to RGB) --- private val COLOR_PRIMARY = Color(27, 147, 136)</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val COLOR_BACKGROUND_MUTED = Color(248, 248, 248)</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val COLOR_BORDER = Color(228, 232, 239)</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val COLOR_FOREGROUND = Color(108, 114, 126)</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val COLOR_MUTED_FOREGROUND = Color(164, 164, 164)</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val DATETIMEZONE_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm a XXX", Locale.US)</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val DATETIME_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm a", Locale.US)</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val FONT_BODY: Font</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val FONT_BODY_BOLD: Font</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val FONT_H1: Font</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val FONT_H2: Font</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val FONT_H3: Font</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val FONT_LABEL: Font</ID>
    <ID>VariableNaming:ReportPdfService.kt$ReportPdfService$private val FONT_PATH_BOLD = "/fonts/DMSans-Bold.ttf"</ID>
  </CurrentIssues>
</SmellBaseline>
