package com.cha.domain

import aws.sdk.kotlin.services.s3.S3Client
import aws.sdk.kotlin.services.s3.model.GetObjectRequest
import aws.smithy.kotlin.runtime.content.toInputStream
import com.cha.model.dto.Document
import com.cha.model.dto.toDto
import com.cha.repository.DocumentsRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.context.annotation.Value
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.http.HttpHeaders
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.exceptions.HttpStatusException
import io.micronaut.http.server.types.files.StreamedFile
import io.micronaut.multitenancy.tenantresolver.TenantResolver
import jakarta.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException

private val logger = KotlinLogging.logger { }

@Singleton
class DocumentsService(
    private val repository: DocumentsRepository,
    private val s3Client: S3Client,
    @Value("\${aws.s3.bucket}") private val bucketName: String,
    @Value("\${aws.s3.prefixes.imported-ehr-documents}") private val importedEhrDocsPrefix: String,
    @Value("\${aws.s3.prefixes.viewer-generated-reports}") private val viewerGeneratedDocsPrefix: String,
    private val tenantResolver: TenantResolver,
) {

    suspend fun getPatientDocuments(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Document> {
        return repository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    // TODO: Race condition with s3Client closing before streamed file is created.
    suspend fun getDocument(
        filenameWithinTenant: String,
        generatedReport: Boolean = false,
    ): HttpResponse<StreamedFile> {
        val docPrefix = if (generatedReport) viewerGeneratedDocsPrefix else importedEhrDocsPrefix
        val tenant = tenantResolver.resolveTenantId()

        val objectKey = "$docPrefix/${tenant}/$filenameWithinTenant"
        logger.info { "Attempting to retrieve document: $objectKey from bucket: $bucketName for tenant: ${tenant}" }

        return withContext(Dispatchers.IO) {
            try {
                val request = GetObjectRequest {
                    bucket = bucketName
                    key = objectKey
                }

                s3Client.getObject(request) { s3Response ->
                    val inputStream = s3Response.body?.toInputStream()
                        ?: throw RuntimeException("S3 object body is null for key: $objectKey")

                    val mediaType =
                        s3Response.contentType?.let { MediaType.of(it) } ?: MediaType.APPLICATION_OCTET_STREAM_TYPE

                    val streamedFile = StreamedFile(inputStream, mediaType).attach(filenameWithinTenant)
                    logger.info { "Successfully retrieved document: $objectKey, ETag: ${s3Response.eTag}, ContentType: $mediaType" }
                    HttpResponse.ok(streamedFile).header(HttpHeaders.ETAG, s3Response.eTag)

                }
            } catch (e: IOException) {
                logger.error(e) { "IO error retrieving document $objectKey from S3 bucket $bucketName" }
                throw HttpStatusException(
                    HttpStatus.SERVICE_UNAVAILABLE,
                    "Failed to retrieve document: $filenameWithinTenant due to network error. Please try again later."
                )
            } catch (e: Exception) {
                logger.error(e) { "Error retrieving document $objectKey from S3 bucket $bucketName" }
                throw RuntimeException(
                    "Failed to retrieve document: $filenameWithinTenant. Cause: ${e.message}",
                    e
                )
            }
        }
    }
}
