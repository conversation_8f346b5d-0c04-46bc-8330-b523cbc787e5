package com.cha.security

import aws.smithy.kotlin.runtime.text.encoding.decodeBase64
import com.cha.model.auth.User
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.context.annotation.Value
import io.micronaut.core.annotation.NonNull
import io.micronaut.core.annotation.Nullable
import io.micronaut.http.HttpRequest
import io.micronaut.json.JsonMapper
import io.micronaut.security.token.Claims
import io.micronaut.security.token.jwt.validator.JwtClaimsValidator
import jakarta.inject.Singleton
import java.time.Instant

private val logger = KotlinLogging.logger { }

/**
 * Custom JWT Claims Validator for AWS Cognito Tokens.
 *
 * This validator ensures that the JWT's 'iss' (issuer) claim matches the
 * expected Cognito User Pool URL and that the 'aud' (audience) claim
 * contains the expected Cognito App Client ID.
 *
 * Also provides utility methods to extract user information from Cognito
 * tokens.
 *
 * Micronaut Security automatically discovers and uses all beans
 * implementing JwtClaimsValidator. All registered validators
 * must return true for the token claims to be considered valid.
 *
 * @see io.micronaut.security.token.jwt.validator.JwtClaimsValidator
 */
@Singleton
class CognitoClaimsValidator(
    private val jsonMapper: JsonMapper
) : JwtClaimsValidator<HttpRequest<*>> {

    @Value("\${aws.cognito.clientId}")
    private lateinit var expectedAudience: String // This is the Cognito App Client ID

    @Value("\${aws.cognito.issuerUrl}")
    private lateinit var expectedIssuer: String

    override fun validate(
        claims: @NonNull Claims?,
        request: @Nullable HttpRequest<*>?
    ): Boolean {
        if (claims == null) {
            logger.warn { "JWT validation received null claims. Token rejected." }
            return false
        }

        // Check token expiration
        val expTime = claims.get("exp")
        if (expTime is Number) {
            val expirationTime = Instant.ofEpochSecond(expTime.toLong())
            val now = Instant.now()

            if (now.isAfter(expirationTime)) {
                logger.warn { "JWT validation failed: Token expired at $expirationTime (current time: $now). Token rejected." }
                return false
            }
        } else if (expTime == null) {
            logger.warn { "JWT validation failed: Missing 'exp' claim. Token rejected." }
            return false
        }

        // Validate the Issuer ('iss') claim
        val actualIssuer = claims.get("iss")
        if (actualIssuer != expectedIssuer) {
            logger.warn {
                "JWT validation failed: Invalid issuer. Expected: $expectedIssuer, Actual: $actualIssuer. Token rejected."
            }
            return false // Issuer does not match
        }

        // Validate the Audience ('aud') claim
        val actualAudience = claims.get("aud")
        val actualAudienceList: List<String>? = try {
            when (actualAudience) {
                is String -> listOf(actualAudience)
                is List<*> -> actualAudience.filterIsInstance<String>()
                else -> null
            }
        } catch (e: Exception) {
            logger.warn { "Failed to parse audience claim: ${e.message}" }
            null
        }

        if (actualAudienceList.isNullOrEmpty() || !actualAudienceList.contains(expectedAudience)) {
            logger.warn {
                "JWT validation failed: Invalid audience. Expected audience $expectedAudience not found in actual list: $actualAudienceList. Token rejected."
            }
            return false // Expected audience (App Client ID) not found
        }

        logger.debug {
            "Custom JWT claims validation successful for issuer {$actualIssuer} and audience {$expectedAudience}"
        }
        return true
    }

    /**
     * Extracts user information from a Cognito ID token by parsing its JWT
     * claims.
     *
     * @param idToken The ID token from Cognito authentication
     * @return User object containing user details extracted from the token
     */
    fun extractUserInfoFromIdToken(idToken: String?): User {
        // Check if token is null or blank
        if (idToken.isNullOrBlank()) {
            logger.error { "Cannot extract user info: ID token is null or empty" }
            return createEmptyUserInfo()
        }

        try {
            // Split the JWT into its parts
            val parts = idToken.split(".")
            if (parts.size < 2) {
                logger.error { "Invalid JWT format. Expected at least 2 parts but found ${parts.size}" }
                return createEmptyUserInfo()
            }

            // The payload is the second part (index 1)
            val payload = parts[1]
            val claims = decodeJwtPayload(payload)

            claims.forEach { (key, value) ->
                logger.info { "Claim: $key = $value" }
            }

            // Extract the user information
            val userId = claims["sub"] as? String ?: ""
            val username = claims["cognito:username"] as? String ?: ""
            val email = claims["email"] as? String ?: ""
            val firstName = claims["given_name"] as? String ?: ""
            val lastName = claims["family_name"] as? String ?: ""
            val updatedAt = claims["updated_at"] as? Long ?: run { logger.warn { "updated_at claim not found" }; 0L }
            val pictureUrl = claims["picture"] as? String ?: ""
            val emailVerified = claims["email_verified"] as? Boolean ?: false
            val cognitoGroups = when (val groupsValue = claims["cognito:groups"]) {
                null -> emptyList()
                is List<*> -> groupsValue.mapNotNull { it?.toString() }
                is String -> groupsValue.split(",").map { it.trim() }
                else -> {
                    logger.warn { "Unexpected type for cognito:groups: ${groupsValue.javaClass.name}" }
                    emptyList()
                }
            }

            return User(
                id = userId,
                username = username,
                email = email,
                emailVerified = emailVerified,
                firstName = firstName,
                lastName = lastName,
                pictureUrl = pictureUrl,
                createdAt = Instant.ofEpochSecond(0), // TODO: Cognito does not provide created_at in ID token
                updatedAt = Instant.ofEpochSecond(updatedAt),
                roles = cognitoGroups,
            )
        } catch (e: Exception) {
            logger.error(e) { "Error extracting user info from ID token: ${e.message}" }
            return createEmptyUserInfo()
        }
    }

    /**
     * Creates an empty UserInfo object for fallback purposes. This is used
     * when token parsing fails.
     */
    private fun createEmptyUserInfo(): User {
        return User(
            id = "",
            username = "",
            email = "",
            roles = emptyList(),
            emailVerified = false,
            firstName = "",
            lastName = "",
            createdAt = Instant.ofEpochSecond(0),
            updatedAt = Instant.ofEpochSecond(0)
        )
    }

    /**
     * Decodes a JWT payload (the middle part of a JWT token) and returns the
     * claims as a Map.
     *
     * @param payload The Base64URL encoded payload part of the JWT
     * @return Map of claims from the JWT payload
     */
    private fun decodeJwtPayload(payload: String): Map<String, Any> {
        try {
            // Normalize the Base64URL payload by adding padding if needed
            val normalizedPayload = when (payload.length % 4) {
                0 -> payload
                2 -> "$payload=="
                3 -> "$payload="
                else -> payload
            }

            // Replace URL-safe characters with standard Base64 characters
            val standardBase64 = normalizedPayload
                .replace('-', '+')
                .replace('_', '/')

            // Decode the Base64 payload
            val decodedBytes = standardBase64.decodeBase64()
            return jsonMapper.readValue(decodedBytes, Map::class.java) as Map<String, Any>
        } catch (e: Exception) {
            logger.error(e) { "Error decoding JWT payload: ${e.message}" }
            return emptyMap()
        }
    }
}