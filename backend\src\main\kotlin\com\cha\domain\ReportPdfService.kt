package com.cha.domain

import aws.sdk.kotlin.services.s3.S3Client
import aws.sdk.kotlin.services.s3.model.GetObjectRequest
import aws.smithy.kotlin.runtime.content.toInputStream
import com.cha.model.dto.Visit
import com.cha.model.report.HospitalInfoViewModel
import com.cha.model.report.PatientInfoViewModel
import com.cha.model.report.ReportViewModel
import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Chunk
import com.lowagie.text.Document
import com.lowagie.text.Element
import com.lowagie.text.Font
import com.lowagie.text.FontFactory
import com.lowagie.text.Image
import com.lowagie.text.PageSize
import com.lowagie.text.Paragraph
import com.lowagie.text.Rectangle
import com.lowagie.text.pdf.PdfPCell
import com.lowagie.text.pdf.PdfPTable
import com.lowagie.text.pdf.PdfReader
import com.lowagie.text.pdf.PdfWriter
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.context.annotation.Value
import io.micronaut.multitenancy.tenantresolver.TenantResolver
import jakarta.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.awt.Color
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.net.URI
import java.nio.ByteBuffer
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale


/*private fun addAllergiesSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportAllergies(sections, document)
}

private fun addLabResultsSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportLaboratory(sections, document)
}

private fun addTranscriptionResults(sections: VisitSectionsViewModel?, document: Document) {
    ReportTranscription(sections, document)
}

private fun addAlertsSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportAlerts(sections, document)
}

private fun addDiagnosesSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportDiagnoses(sections, document)
}

private fun addHomeMedicationsSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportHomeMedications(sections, document)
}

private fun addImmunizationsSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportImmunizations(sections, document)
}

private fun addMicroCommentsSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportMicroComments(sections, document)
}

private fun addMicroDetailsSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportMicroDetails(sections, document)
}

private fun addMicroSusceptibilitySection(sections: VisitSectionsViewModel?, document: Document) {
    ReportMicroSusceptibility(sections, document)
}

private fun addMedicationsSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportMedications(sections, document)
}

private fun addNotesSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportNotes(sections, document)
}

private fun addProceduresSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportProcedures(sections, document)
}

private fun addInsuranceSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportInsurance(sections, document)
}

private fun addBillsSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportBills(sections, document)
}

private fun addInvoicesSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportInvoices(sections, document)
}

private fun addInvoiceNotesSection(sections: VisitSectionsViewModel?, document: Document) {
    ReportInvoiceNotes(sections, document)
}*/



private val log = KotlinLogging.logger { }

// TODO should scope this to just lifecycle of generating a single PDF.
@Singleton
class ReportPdfService(
    @Value("\${aws.s3.bucket}") private val bucketName: String,
    @Value("\${aws.s3.prefixes.imported-ehr-documents}") private val importedEhrDocsPrefix: String,
    private val s3Client: S3Client,
    private val tenantResolver: TenantResolver,
) {

    // --- Define Theme Colors (converted from OKLCH to RGB) ---
    private val COLOR_PRIMARY = Color(27, 147, 136)
    private val COLOR_FOREGROUND = Color(108, 114, 126)
    private val COLOR_MUTED_FOREGROUND = Color(164, 164, 164)
    private val COLOR_BORDER = Color(228, 232, 239)
    private val COLOR_BACKGROUND_MUTED = Color(248, 248, 248)

    // --- Define Fonts ---
    // Note: You must place these .ttf files in your project's resources folder (e.g., src/main/resources/fonts/)
    private val FONT_PATH_REGULAR = "/fonts/DMSans-Regular.ttf"
    private val FONT_PATH_BOLD = "/fonts/DMSans-Bold.ttf"

    private val FONT_BODY: Font
    private val FONT_BODY_BOLD: Font
    private val FONT_H1: Font
    private val FONT_H2: Font
    private val FONT_H3: Font
    private val FONT_LABEL: Font

    // --- Define Formatters ---
    private val DATE_FORMATTER = DateTimeFormatter.ofPattern("MMMM dd, yyyy", Locale.US)
    private val DATETIME_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm a", Locale.US)
    private val DATETIMEZONE_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm a XXX", Locale.US)

    init {
        // Register the fonts with OpenPDF
        FontFactory.register(javaClass.getResource(FONT_PATH_REGULAR)?.toString(), "DMSansRegular")
        FontFactory.register(javaClass.getResource(FONT_PATH_BOLD)?.toString(), "DMSansBold")

        // Initialize Font objects
        FONT_BODY = FontFactory.getFont("DMSansRegular", 10f, Font.NORMAL, COLOR_FOREGROUND)
        FONT_BODY_BOLD = FontFactory.getFont("DMSansBold", 10f, Font.NORMAL, COLOR_FOREGROUND)
        FONT_H1 = FontFactory.getFont("DMSansBold", 22f, Font.NORMAL, COLOR_PRIMARY)
        FONT_H2 = FontFactory.getFont("DMSansBold", 16f, Font.NORMAL, COLOR_PRIMARY)
        FONT_H3 = FontFactory.getFont("DMSansBold", 12f, Font.NORMAL, COLOR_FOREGROUND)
        FONT_LABEL = FontFactory.getFont("DMSansBold", 10f, Font.NORMAL, COLOR_MUTED_FOREGROUND)
    }

    /**
     * Generates a PDF report from the view model.
     *
     * @param model The ReportViewModel containing all report data.
     * @return A Flow of ByteBuffer chunks containing the generated PDF.
     */
    suspend fun generateReportPdf(model: ReportViewModel): Flow<ByteBuffer> {
        val outputStream = ByteArrayOutputStream()
        val document = Document(PageSize.LETTER)
        val pdfWriter = PdfWriter.getInstance(document, outputStream)

        // TODO encrypt the PDF if needed
        // TODO set PDF metadata (title, author, subject, keywords)
        document.addCreationDate()
        document.open()

        // 1. Add Report Header
        addReportHeader(document, model.hospital, model.reportTitle)

        // 2. Add Patient Information
        addPatientInfo(document, model.patient)

        // 3. Add Report Meta-Information
        addReportMetaInfo(document, model)

        // Add a page break before visits
        document.newPage()

//        if (reportType == ReportType.ROI) {
        // TODO: if ROI report, loop through visits ordered by the system. Make sure the patient system is formated properly.
        // 4. Loop through each visit and add its sections
        model.visits.forEach { visit ->
            // Find clinical and financial data specific to this visit
            val visitClinicalData = model.allClinicalData.find { it.visitId == visit.accountNumber }?.data
            val visitFinancialData = model.allFinancialData.find { it.visitId == visit.accountNumber }?.data
            val visitDocuments =
                model.documentsMetadata.visitDocuments?.filter { it.accountNumber == visit.accountNumber }

            val visitSections = VisitSectionsViewModel(
                clinical = visitClinicalData,
                financial = visitFinancialData
            )

            addVisitSection(document, visit, visitSections, visitDocuments, pdfWriter)
            // Add a page break after each visit section
            document.newPage()
        }

        // Append unassigned documents
        model.documentsMetadata.unassignedDocuments?.let { unassignedDocuments ->
            document.add(Paragraph("Unassigned Documents", FONT_H2))
            addDocumentsSection(unassignedDocuments, document, pdfWriter)
        }

        // 5. Add Footer (if needed - page numbers are handled by PdfWriter events)
        // For simplicity, a static footer can be added here, but proper page x of y needs events.

        document.close()

        // Convert ByteArrayOutputStream to a ByteBuffer and return as a Flow
        val byteArray = outputStream.toByteArray()
        val buffer = ByteBuffer.wrap(byteArray)
        return flowOf(buffer)
    }

    private fun addReportHeader(document: Document, hospital: HospitalInfoViewModel, reportTitle: String) {
        val headerTable = PdfPTable(2)
        headerTable.widthPercentage = 100f
        headerTable.setWidths(floatArrayOf(3f, 1f)) // 75% for text, 25% for logo
        headerTable.defaultCell.border = Rectangle.NO_BORDER
        headerTable.setSpacingAfter(20f)

        // Hospital Info Cell
        val infoCell = PdfPCell()
        infoCell.border = Rectangle.NO_BORDER
        infoCell.addElement(Paragraph(reportTitle, FONT_H1))
        infoCell.addElement(Paragraph(hospital.name, FONT_H3))
        infoCell.addElement(Paragraph(hospital.address.street, FONT_BODY))
        infoCell.addElement(
            Paragraph(
                "${hospital.address.city}, ${hospital.address.state} ${hospital.address.zipCode}",
                FONT_BODY
            )
        )
        infoCell.addElement(Paragraph("Phone: ${hospital.telephone}", FONT_BODY))
        hospital.fax?.let { infoCell.addElement(Paragraph("Fax: $it", FONT_BODY)) }
        hospital.website.let { infoCell.addElement(Paragraph("Web: ${hospital.website}", FONT_BODY)) }
        headerTable.addCell(infoCell)

        // Logo Cell
        val logoImage = Image.getInstance(URI.create(hospital.logoUrl).toURL())
        val logoCell = PdfPCell(logoImage, true)
        logoCell.border = Rectangle.NO_BORDER
        logoCell.horizontalAlignment = Element.ALIGN_RIGHT
        headerTable.addCell(logoCell)

        document.add(headerTable)
    }

    private fun addPatientInfo(document: Document, patient: PatientInfoViewModel) {
        document.add(createSectionTitle("Patient Information"))

        val table = PdfPTable(2)
        table.widthPercentage = 100f
        table.defaultCell.border = Rectangle.NO_BORDER
        table.setSpacingAfter(15f)

        table.addCell(
            createLabelValueCell(
                "Name:",
                "${patient.lastName}, ${patient.firstName} ${patient.middleName ?: ""}"
            )
        )
        table.addCell(
            createLabelValueCell(
                "Date of Birth:",
                patient.dob?.let { formatDate(LocalDate.parse(it)) } ?: "N/A"))
        table.addCell(createLabelValueCell("EMPI:", patient.empi.toString()))
        table.addCell(createLabelValueCell("Sex:", patient.sex))

        // Handle addresses
        val addressText = if (patient.address.isNotEmpty()) {
            patient.address.joinToString("\n") { "${it.street}, \n${it.city}, ${it.state} ${it.zipCode}\n" }
        } else {
            "N/A"
        }
        val addressChunk = Chunk(addressText, FONT_BODY)
        table.addCell(createLabelValueCell("Addresses:\n", addressChunk))

        // Handle phones
        val phoneText = if (patient.phone.isNotEmpty()) {
            patient.phone.joinToString("\n") { "${it.type}: ${it.number}" }
        } else {
            "N/A"
        }
        val phoneChunk = Chunk(phoneText, FONT_BODY)
        table.addCell(createLabelValueCell("Phone Numbers:\n", phoneChunk, true))
        document.add(table)
    }

    private fun addReportMetaInfo(document: Document, model: ReportViewModel) {
        document.add(createSectionTitle("Report Information"))

        val table = PdfPTable(2)
        table.widthPercentage = 100f
        table.defaultCell.border = Rectangle.NO_BORDER
        table.setSpacingAfter(15f)

        table.addCell(
            createLabelValueCell(
                "Generated:",
                model.generatedDateTime.format(DATETIMEZONE_FORMATTER)
            )
        )
        table.addCell(
            createLabelValueCell(
                "Requested By:",
                "${model.requestUser.firstName} ${model.requestUser.lastName}"
            )
        )
        table.addCell(createLabelValueCell("Requester Role:", model.requestUser.role ?: "N/A"))
        table.addCell(createLabelValueCell("Requester Email:", model.requestUser.email))

        // Associated Systems
        val systemsChunk = Chunk()
        model.system.forEach { sys ->
            systemsChunk.append("${sys.name} (ID: ${sys.id}, MRN: ${sys.mrn})\n")
        }
        val cell = createLabelValueCell("Associated Systems:", systemsChunk)
        cell.colspan = 2
        table.addCell(cell)

        document.add(table)
    }

    private suspend fun addVisitSection(
        document: Document,
        visit: Visit,
        sections: VisitSectionsViewModel?,
        visitDocuments: List<com.cha.model.dto.Document>?,
        pdfWriter: PdfWriter,
    ) {
        // Add a title for this specific visit
        val visitTitle =
            Paragraph("Visit on ${formatDateTime(visit.admitDate)} (Account: ${visit.accountNumber})", FONT_H2)
        visitTitle.spacingBefore = 20f
        visitTitle.spacingAfter = 10f
        document.add(visitTitle)

        // Add Visit Details Section
        document.add(createSectionTitle("Visit Details"))
        val visitDetailsTable = PdfPTable(2)
        visitDetailsTable.widthPercentage = 100f
        visitDetailsTable.defaultCell.border = Rectangle.NO_BORDER
        visitDetailsTable.setSpacingAfter(15f)

        visitDetailsTable.addCell(createLabelValueCell("Discharge Date:", formatDateTime(visit.dischargeDate)))
        visitDetailsTable.addCell(createLabelValueCell("Visit Status:", visit.visitStatus))
        visitDetailsTable.addCell(createLabelValueCell("Patient Category:", visit.patientCategory))
        visitDetailsTable.addCell(createLabelValueCell("Admit Source:", visit.admitSource))
        visitDetailsTable.addCell(createLabelValueCell("Admit Type:", visit.admitType))
        visitDetailsTable.addCell(createLabelValueCell("Admit Service:", visit.admitService))
        visitDetailsTable.addCell(createLabelValueCell("Admission Diagnosis:", visit.admDiag))
        visitDetailsTable.addCell(createLabelValueCell("Discharge Service:", visit.dischargeService))
        visitDetailsTable.addCell(createLabelValueCell("Discharge Status:", visit.dischargeStatus))
        visitDetailsTable.addCell(createLabelValueCell("Financial Class:", visit.financialClass))
        visitDetailsTable.addCell(createLabelValueCell("Patient Type:", visit.patientType))
        visitDetailsTable.addCell(createLabelValueCell("Visit Type:", visit.visitType))
        visitDetailsTable.addCell(createLabelValueCell("Provider:", visit.provider))
        visitDetailsTable.addCell(createLabelValueCell("Visit Number:", visit.visitNumber?.toString()))
        visitDetailsTable.addCell(createLabelValueCell("Home Phone:", visit.homeTelephone))
        visitDetailsTable.addCell(createLabelValueCell("Mobile Phone:", visit.mobileTelephone))

        val reasonCell = createLabelValueCell("Reason for Visit:", visit.reason)
        reasonCell.colspan = 2
        visitDetailsTable.addCell(reasonCell)

        val notesCell = createLabelValueCell("Visit Notes:", visit.notes)
        notesCell.colspan = 2
        visitDetailsTable.addCell(notesCell)

        document.add(visitDetailsTable)

        addDiagnosesSection(sections, document)
        addProceduresSection(sections, document)
        addLabResultsSection(sections, document)
        addLabResultsReferenceSection(sections, document)
        addMedicationsSection(sections, document)
        addHomeMedicationsSection(sections, document)
        addAllergiesSection(sections, document)
        addImmunizationsSection(sections, document)
        addMicroCommentsSection(sections, document)
        addMicroDetailsSection(sections, document)
        addMicroSusceptibilitySection(sections, document)
        addTranscriptionResults(sections, document)
        addAlertsSection(sections, document)
        addNotesSection(sections, document)

        // Add a page break before financial sections
        if (sections?.financial != null) {
            document.add(Paragraph("Financial Information", FONT_H2))
        }

        addInsuranceSection(sections, document)
        addBillsSection(sections, document)
        addInvoicesSection(sections, document)
        addInvoiceNotesSection(sections, document)

        // Start of documents that are associated with this visit.
        addDocumentsSection(visitDocuments, document, pdfWriter)

        // Add a closing section with disclaimer or footer note
        val visitFooter = Paragraph()
        visitFooter.add(Chunk("End of visit summary for account ${visit.accountNumber}\n", FONT_BODY_BOLD))
//        visitFooter.add(
//            Chunk(
//                "This report was generated automatically and may not include all information available in the medical record. ",
//                FONT_BODY
//            )
//        )
//        visitFooter.add(Chunk("Please consult with your healthcare provider for complete information.", FONT_BODY))
        visitFooter.spacingBefore = 20f
        visitFooter.spacingAfter = 10f
        document.add(visitFooter)
        document.newPage()
    }

    /**
     * Appends external PDF documents to the current PDF document, ensuring
     * that each page is adjusted to match the original PDF's rectangle page
     * size.
     *
     * This method downloads PDF files from S3 (using the provided document
     * metadata), reads each page, and adds it to the destination document.
     * For each page, the destination document's page size is set to match the
     * source page's rectangle before adding, and restored to its previous
     * value after all pages are added.
     *
     * @param documentsMetadata List of document metadata describing the PDFs
     *    to append.
     * @param document The destination Document to which pages will be added.
     * @param writer The PdfWriter associated with the destination document.
     */
    private suspend fun addDocumentsSection(
        documentsMetadata: List<com.cha.model.dto.Document>?,
        document: Document,
        writer: PdfWriter,
    ) {
        documentsMetadata?.let { documentMetadatas ->
            if (documentMetadatas.isNotEmpty()) {
                val tenant = tenantResolver.resolveTenantId()

                // We need to synchronize document operations since they'll be performed from multiple coroutines
                val documentLock = Mutex()

                // Download and append PDFs from S3
                coroutineScope {
                    // Create a list to hold all async jobs
                    val downloadJobs = documentMetadatas.map { documentMetadata ->
                        async(Dispatchers.IO) {
                            val objectKey = "$importedEhrDocsPrefix/${tenant}/${documentMetadata.filename}"
                            log.debug { "Streaming document: $objectKey" }
                            try {
                                val request = GetObjectRequest {
                                    bucket = bucketName
                                    this.key = objectKey
                                }

                                // Process the S3 object as a stream
                                s3Client.getObject(request) { response ->
                                    val inputStream: InputStream? = response.body?.toInputStream()
                                    inputStream?.use { stream ->
                                        // PdfReader consumes the stream directly
                                        val reader = PdfReader(stream)
                                        try {
                                            documentLock.withLock {
                                                val content = writer.directContent

                                                // Save the current page size
                                                val originalPageSize = document.pageSize

                                                // Add all pages from this PDF
                                                for (i in 1..reader.numberOfPages) {
                                                    // Get the source page size
                                                    val pageDict = reader.getPageN(i)
                                                    val mediaBox =
                                                        pageDict.getAsArray(com.lowagie.text.pdf.PdfName.MEDIABOX)
                                                    val pageSize = Rectangle(
                                                        mediaBox.getAsNumber(2).floatValue() - mediaBox.getAsNumber(0)
                                                            .floatValue(),
                                                        mediaBox.getAsNumber(3).floatValue() - mediaBox.getAsNumber(1)
                                                            .floatValue()
                                                    )
                                                    document.pageSize = pageSize
                                                    document.newPage()

                                                    val importedPage = writer.getImportedPage(reader, i)
                                                    content.addTemplate(importedPage, 0f, 0f)
                                                }

                                                // Restore the original page size
                                                document.pageSize = originalPageSize
                                            }
                                        } finally {
                                            reader.close()
                                        }
                                    }
                                }
                            } catch (e: Exception) {
                                log.error(e) { "Failed to process document ${documentMetadata.filename}" }
                                throw e
                            }
                        }
                    }

                    // Wait for all downloads to complete
                    downloadJobs.awaitAll()
                }
            } else {
                val paragraph = Paragraph("No documents found.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addInvoiceNotesSection(sections: VisitSectionsViewModel?, document: Document) {
        sections?.financial?.invoiceNotes?.let { notes ->
            document.add(createSectionTitle("Invoice Notes"))
            if (notes.isNotEmpty()) {
                notes.forEach { note ->
                    val noteTable = PdfPTable(1)
                    noteTable.widthPercentage = 100f
                    setupDataItemTable(noteTable)

                    val headerText = "Note on ${note.noteDate?.let { formatDateTime(it) }}"
                    noteTable.addCell(createDataItemHeaderCell(headerText, 1))

                    val noteCell = PdfPCell(Paragraph(note.patientNote, FONT_BODY))
                    noteCell.border = Rectangle.NO_BORDER
                    noteCell.setPadding(5f)
                    noteTable.addCell(noteCell)

                    document.add(noteTable)
                }
            } else {
                val paragraph = Paragraph("No invoice notes recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addInvoicesSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.financial?.invoices?.let { invoices ->
            document.add(createSectionTitle("Invoices"))
            if (invoices.isNotEmpty()) {
                invoices.forEach { invoice ->
                    val invoiceTable = PdfPTable(2)
                    invoiceTable.widthPercentage = 100f
                    setupDataItemTable(invoiceTable)

                    val headerText = "Invoice #${invoice.invoiceNumber}"
                    invoiceTable.addCell(createDataItemHeaderCell(headerText, 2))

                    invoiceTable.addCell(
                        createLabelValueCell(
                            "Total Amount:",
                            invoice.totalBilled.toString(),
                            inTable = true
                        )
                    )
                    invoiceTable.addCell(
                        createLabelValueCell(
                            "Accounts Receivable Status:",
                            invoice.arStatus,
                            inTable = true
                        )
                    )
                    invoiceTable.addCell(createLabelValueCell("Insurance Plan:", invoice.insurancePlan, inTable = true))
                    invoiceTable.addCell(
                        createLabelValueCell(
                            "Insurance Payment:",
                            invoice.insurancePayments.toString(),
                            inTable = true
                        )
                    )
                    invoiceTable.addCell(
                        createLabelValueCell(
                            "Insurance Payment Amount:",
                            invoice.insurancePaymentAmount.toString(),
                            inTable = true
                        )
                    )
                    invoiceTable.addCell(
                        createLabelValueCell(
                            "Insurance Adjustment Amount:",
                            invoice.insuranceAdjustmentAmount.toString(),
                            inTable = true
                        )
                    )
                    invoiceTable.addCell(
                        createLabelValueCell(
                            "Coinsurance Payment:",
                            invoice.coinsurancePayments.toString(),
                            inTable = true
                        )
                    )
                    invoiceTable.addCell(
                        createLabelValueCell(
                            "Patient Payment:",
                            invoice.patientPayments.toString(),
                            inTable = true
                        )
                    )
                    invoiceTable.addCell(
                        createLabelValueCell(
                            "Patient Payment Amount:",
                            invoice.patientPaymentAmount.toString(),
                            inTable = true
                        )
                    )
                    invoiceTable.addCell(
                        createLabelValueCell(
                            "Patient Adjustment Amount:",
                            invoice.patientAdjustmentAmount.toString(),
                            inTable = true
                        )
                    )
                    invoiceTable.addCell(createLabelValueCell("Balance:", invoice.balance.toString(), inTable = true))
                    invoiceTable.addCell(
                        createLabelValueCell(
                            "Billing Date:",
                            invoice.billingDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    invoiceTable.addCell(
                        createLabelValueCell(
                            "Service Date:",
                            "${invoice.serviceFromDate?.let { formatDateTime(it) }} - ${
                                invoice.serviceThruDate?.let {
                                    formatDateTime(
                                        it
                                    )
                                }
                            }",
                            inTable = true
                        )
                    )

                    document.add(invoiceTable)
                }
            } else {
                val paragraph = Paragraph("No invoice information recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addBillsSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.financial?.bills?.let { bills ->
            document.add(createSectionTitle("Bills"))
            if (bills.isNotEmpty()) {
                bills.forEach { bill ->
                    val billTable = PdfPTable(2)
                    billTable.widthPercentage = 100f
                    setupDataItemTable(billTable)

                    val headerText = "Transaction Code: ${bill.chargeCode} (${bill.chargeCodeDescription ?: "N/A"})"
                    billTable.addCell(createDataItemHeaderCell(headerText, 2))

                    billTable.addCell(createLabelValueCell("Amount:", bill.amount.toString(), inTable = true))
                    billTable.addCell(
                        createLabelValueCell(
                            "Total Amount:",
                            bill.totalAmount.toString(),
                            inTable = true
                        )
                    )
                    billTable.addCell(createLabelValueCell("Invoice #:", bill.invoiceNumber ?: "N/A", inTable = true))
                    billTable.addCell(createLabelValueCell("Insurance:", bill.insurance ?: "N/A", inTable = true))
                    billTable.addCell(
                        createLabelValueCell(
                            "Financial Class:",
                            bill.financialClass ?: "N/A",
                            inTable = true
                        )
                    )
                    billTable.addCell(createLabelValueCell("Department:", bill.department ?: "N/A", inTable = true))
                    billTable.addCell(createLabelValueCell("NRV:", bill.nrv ?: "N/A", inTable = true))
                    billTable.addCell(
                        createLabelValueCell(
                            "Transaction Type:",
                            bill.transactionType ?: "N/A",
                            inTable = true
                        )
                    )
                    billTable.addCell(createLabelValueCell("Visit Status:", bill.visitStatus ?: "N/A", inTable = true))
                    billTable.addCell(createLabelValueCell("Units:", bill.units.toString(), inTable = true))
                    billTable.addCell(createLabelValueCell("Account:", bill.account ?: "N/A", inTable = true))
                    billTable.addCell(
                        createLabelValueCell(
                            "Service Date:",
                            bill.serviceDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    billTable.addCell(
                        createLabelValueCell(
                            "Posting Date:",
                            bill.postingDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    billTable.addCell(
                        createLabelValueCell(
                            "Transaction Date:",
                            bill.transactionDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )

                    document.add(billTable)
                }
            } else {
                val paragraph = Paragraph("No billing information recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addInsuranceSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.financial?.insurance?.let { insurances ->
            document.add(createSectionTitle("Insurance"))
            if (insurances.isNotEmpty()) {
                insurances.forEach { insurance ->
                    val insuranceTable = PdfPTable(2)
                    insuranceTable.widthPercentage = 100f
                    setupDataItemTable(insuranceTable)

                    val headerText = "${insurance.organizationName} (${insurance.insurancePlan})"
                    insuranceTable.addCell(createDataItemHeaderCell(headerText, 2))


                    insuranceTable.addCell(
                        createLabelValueCell(
                            "Group Name:",
                            insurance.groupName,
                            inTable = true
                        )
                    )
                    insuranceTable.addCell(createLabelValueCell("Group Number:", insurance.groupNumber, inTable = true))
                    insuranceTable.addCell(
                        createLabelValueCell(
                            "Subscriber:",
                            insurance.subscriberNumber,
                            inTable = true
                        )
                    )
                    insuranceTable.addCell(
                        createLabelValueCell(
                            "Policy Number:",
                            insurance.policyNumber,
                            inTable = true
                        )
                    )
                    insuranceTable.addCell(
                        createLabelValueCell(
                            "Policy Holder:",
                            insurance.policyHolder,
                            inTable = true
                        )
                    )
                    insuranceTable.addCell(
                        createLabelValueCell(
                            "Policy Holder SSN:",
                            insurance.policyHolderSsn,
                            inTable = true
                        )
                    )
                    insuranceTable.addCell(
                        createLabelValueCell(
                            "Certificate #:",
                            insurance.certificateNo,
                            inTable = true
                        )
                    )
                    insuranceTable.addCell(createLabelValueCell("Sequence:", insurance.insSeq, inTable = true))


                    document.add(insuranceTable)
                }
            } else {
                val paragraph = Paragraph("No insurance information recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addTranscriptionResults(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.transcriptionResults?.let { transcriptionResults ->
            document.add(createSectionTitle("Transcription Results"))
            if (transcriptionResults.isNotEmpty()) {
                transcriptionResults.forEach { result ->
                    val resultTable = PdfPTable(2)
                    resultTable.widthPercentage = 100f
                    setupDataItemTable(resultTable)
                    val headerText = result.orderCode ?: "Transcription Result"
                    resultTable.addCell(createDataItemHeaderCell(headerText, 2))

                    // Create a cell for the result content that spans both columns
                    if (!result.transcriptionText.isNullOrBlank()) {
                        val resultTextCell = PdfPCell(Paragraph(result.transcriptionText, FONT_BODY))
                        resultTextCell.colspan = 2
                        resultTextCell.border = Rectangle.NO_BORDER
                        resultTextCell.setPadding(5f)
                        resultTable.addCell(resultTextCell)
                    }

                    resultTable.addCell(
                        createLabelValueCell(
                            "Order Description:",
                            result.orderDescription,
                            inTable = true
                        )
                    )
                    resultTable.addCell(createLabelValueCell("Status:", result.status, inTable = true))
                    resultTable.addCell(
                        createLabelValueCell(
                            "Reading Caregiver:",
                            result.readingCaregiver,
                            inTable = true
                        )
                    )
                    resultTable.addCell(
                        createLabelValueCell(
                            "Signing Caregiver:",
                            result.signingCaregiver,
                            inTable = true
                        )
                    )
                    resultTable.addCell(createLabelValueCell("Cancel Reason:", result.cancelReason, inTable = true))
                    resultTable.addCell(
                        createLabelValueCell(
                            "Entered Date:",
                            result.enterDatetime?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    resultTable.addCell(
                        createLabelValueCell(
                            "Reading Date:",
                            result.readDatetime?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    resultTable.addCell(
                        createLabelValueCell(
                            "Sign-off Date:",
                            result.signDatetime?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    document.add(resultTable)
                }
            } else {
                val paragraph = Paragraph("No transcription results recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addDiagnosesSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.diagnoses?.let { diagnoses ->
            document.add(createSectionTitle("Diagnoses"))
            if (diagnoses.isNotEmpty()) {
                diagnoses.forEach { diagnosis ->
                    val diagnosisTable = PdfPTable(2)
                    diagnosisTable.widthPercentage = 100f
                    setupDataItemTable(diagnosisTable)

                    val headerText = "ICD Code: ${diagnosis.icdCode ?: "N/A"}"
                    diagnosisTable.addCell(createDataItemHeaderCell(headerText, 2))
                    diagnosisTable.addCell(
                        createLabelValueCell(
                            "ICD Description:",
                            diagnosis.icdDescription,
                            inTable = true
                        )
                    )
                    diagnosisTable.addCell(createLabelValueCell("Caregiver:", diagnosis.caregiver, inTable = true))
                    diagnosisTable.addCell(
                        createLabelValueCell(
                            "Type:",
                            diagnosis.type.toString(),
                            inTable = true
                        )
                    )
                    diagnosisTable.addCell(createLabelValueCell("Rank:", diagnosis.rank.toString(), inTable = true))
                    diagnosisTable.addCell(createLabelValueCell("Active:", diagnosis.active.toString(), inTable = true))
                    diagnosisTable.addCell(
                        createLabelValueCell(
                            "Date:",
                            diagnosis.procedureDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )

                    document.add(diagnosisTable)
                }
            } else {
                val paragraph = Paragraph("No diagnoses recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addMedicationsSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.medications?.let { medications ->
            document.add(createSectionTitle("Medications"))
            if (medications.isNotEmpty()) {
                medications.forEach { med ->
                    val medTable = PdfPTable(2)
                    medTable.widthPercentage = 100f
                    setupDataItemTable(medTable)

                    val headerText = "${med.brandName} (${med.genericName ?: "N/A"})"
                    medTable.addCell(createDataItemHeaderCell(headerText, 2))

                    medTable.addCell(createLabelValueCell("Dose:", med.dosage, inTable = true))
                    medTable.addCell(createLabelValueCell("Dosage Form:", med.dosageForm, inTable = true))
                    medTable.addCell(createLabelValueCell("Frequency:", med.frequency, inTable = true))
                    medTable.addCell(createLabelValueCell("Caregiver:", med.caregiver, inTable = true))
                    medTable.addCell(
                        createLabelValueCell(
                            "Start Date:",
                            med.startDatetime?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    medTable.addCell(
                        createLabelValueCell(
                            "End Date:",
                            med.stopDatetime?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )

                    document.add(medTable)
                }
            } else {
                val paragraph = Paragraph("No medications recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addLabResultsReferenceSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.labResultReferences?.let { labResultRefs ->
            document.add(createSectionTitle("Lab Result References"))
            if (labResultRefs.isNotEmpty()) {
                labResultRefs.forEach { ref ->
                    val refTable = PdfPTable(2)
                    refTable.widthPercentage = 100f
                    setupDataItemTable(refTable)

                    val headerText = "Order Code: ${ref.orderCode ?: "Unknown"}"
                    refTable.addCell(createDataItemHeaderCell(headerText, 2))
                    refTable.addCell(
                        createLabelValueCell(
                            "Order Code Description:",
                            ref.orderCodeDescription,
                            inTable = true
                        )
                    )
                    refTable.addCell(createLabelValueCell("Test Code:", ref.testCode, inTable = true))
                    refTable.addCell(createLabelValueCell("Test Description:", ref.testDescription, inTable = true))
                    refTable.addCell(createLabelValueCell("Test Abbreviation:", ref.testAbbreviation, inTable = true))
                    refTable.addCell(createLabelValueCell("Test Unit:", ref.testUnit, inTable = true))
                    refTable.addCell(createLabelValueCell("Test Status:", ref.testStatus, inTable = true))
                    refTable.addCell(createLabelValueCell("Test Result Flag:", ref.testResultFlag, inTable = true))
                    refTable.addCell(createLabelValueCell("Normal Lookup Flag:", ref.normalLookupFlag, inTable = true))
                    refTable.addCell(createLabelValueCell("Sequence:", ref.sequence, inTable = true))
                    refTable.addCell(createLabelValueCell("Abnormal Flag:", ref.abnormalFlag, inTable = true))
                    refTable.addCell(createLabelValueCell("Abnormal Code:", ref.abnormalCode, inTable = true))
                    refTable.addCell(createLabelValueCell("Corrected Result:", ref.correctedResult, inTable = true))
                    refTable.addCell(createLabelValueCell("Reference Range:", ref.referenceRange, inTable = true))
                    refTable.addCell(createLabelValueCell("Result Type:", ref.resultType.toString(), inTable = true))
                    refTable.addCell(createLabelValueCell("Result Line:", ref.resultLine, inTable = true))
                    refTable.addCell(createLabelValueCell("Entered By:", ref.enteredBy, inTable = true))
                    refTable.addCell(createLabelValueCell("Performed By:", ref.performedBy, inTable = true))
                    refTable.addCell(
                        createLabelValueCell(
                            "Entered Date:",
                            ref.enteredDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    refTable.addCell(
                        createLabelValueCell(
                            "Released Date:",
                            ref.releaseDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )

                    document.add(refTable)
                }
            } else {
                val paragraph = Paragraph("No lab result references recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }


    private fun addLabResultsSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {

        sections?.clinical?.labResults?.let { labResults ->
            document.add(createSectionTitle("Lab Results"))
            if (labResults.isNotEmpty()) {
                labResults.distinctBy { 
                    it.accession to it.orderCode 
                }.forEach { lab ->
                    val sameAccession = labResults.filter {
                        it.accession == lab.accession &&
                                it.orderCode == lab.orderCode
                    }

                    val title1 =
                        "Accession: ${lab.accession} Order Code: ${lab.orderCode} Order Description: ${lab.orderDescription}"
                    val title2 = "Service Date: ${lab.serviceDatetime?.let { formatDateTime(it) }} Collected Date: ${
                        lab.collectedDatetime?.let {
                            formatDateTime(it)
                        }
                    } Released Date: ${lab.releasedDatetime?.let { formatDateTime(it) }}"
                    document.add(Paragraph(title1, FONT_BODY))
                    document.add(Paragraph(title2, FONT_BODY))
                    document.add(Paragraph(" ", FONT_BODY)) // Add a blank line for spacing
//----------------------------------------------------------------------------------------------------
//Accession: 196059966 Order Code: BMP  Order Description: BASIC METABOLIC PROFILE
//Service Date: 2019-10-25 09:35:00 Collected Date: 2019-10-25 09:35:00  Released Date: 2019-10-25 09:35:00
//                 
//ABBR  DESCRIPTION  Result  Units  Flag Abn Flag Released By
//SODIUM  SODIUM   141   mmol/L  F  N   VOWELL,KEVIN
//K   POTASSIUM  4.3   mmol/L  F  N   VOWELL,KEVIN
//CL   CHLORIDE  106   mmol/L  F  N   VOWELL,KEVIN
//CO2   CO2    28   mmol/L  F  N   VOWELL,KEVIN
//CA   CALCIUM   8.7   mg/dL  F  L   VOWELL,KEVIN
//GLU   GLUCOSE   112   mg/dL  F  H   VOWELL,KEVIN

                    val labTable = PdfPTable(7)
                    labTable.widthPercentage = 100f
                    labTable.setSpacingAfter(15f)

                    // Header row
                    labTable.addCell(createTableHeaderCell("ABBR"))
                    labTable.addCell(createTableHeaderCell("Description"))
                    labTable.addCell(createTableHeaderCell("Result"))
                    labTable.addCell(createTableHeaderCell("Units"))
                    labTable.addCell(createTableHeaderCell("Flag"))
                    labTable.addCell(createTableHeaderCell("Abn Flag"))
                    labTable.addCell(createTableHeaderCell("Released By"))

                    sameAccession.forEach { labResult ->
                        labTable.addCell(createTableDataCell(labResult.testAbbr ?: ""))
                        labTable.addCell(createTableDataCell(labResult.testDescription ?: ""))
                        labTable.addCell(createTableDataCell(labResult.numericLabResult ?: ""))
                        labTable.addCell(createTableDataCell(labResult.units ?: ""))
                        labTable.addCell(createTableDataCell(labResult.resultFlag ?: ""))
                        labTable.addCell(createTableDataCell(labResult.abnFlag ?: ""))
                        labTable.addCell(createTableDataCell(labResult.releasedBy ?: ""))
                    }

                    document.add(labTable)
                }
            } else {
                val paragraph = Paragraph("No lab results recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }
    private fun addImmunizationsSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.immunizations?.let { immunizations ->
            document.add(createSectionTitle("Immunizations"))
            if (immunizations.isNotEmpty()) {
                immunizations.forEach { immunization ->
                    val immunizationTable = PdfPTable(2)
                    immunizationTable.widthPercentage = 100f
                    setupDataItemTable(immunizationTable)

                    val headerText = immunization.immunization ?: "Immunization"
                    immunizationTable.addCell(createDataItemHeaderCell(headerText, 2))

                    immunizationTable.addCell(
                        createLabelValueCell(
                            "Lot Number:",
                            immunization.lotNumber,
                            inTable = true
                        )
                    )
                    immunizationTable.addCell(
                        createLabelValueCell(
                            "Manufacturer:",
                            immunization.manufacturer,
                            inTable = true
                        )
                    )
                    immunizationTable.addCell(
                        createLabelValueCell(
                            "Service Date:",
                            immunization.serviceDatetime?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    immunizationTable.addCell(
                        createLabelValueCell(
                            "Immunization Date:",
                            immunization.immunizationDatetime?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )

                    document.add(immunizationTable)
                }
            } else {
                val paragraph = Paragraph("No immunizations recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addNotesSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.notes?.let { notes ->
            document.add(createSectionTitle("Notes"))
            if (notes.isNotEmpty()) {
                notes.forEach { note ->
                    val noteTable = PdfPTable(2)
                    noteTable.widthPercentage = 100f
                    setupDataItemTable(noteTable)
                    noteTable.addCell(
                        createLabelValueCell(
                            "Created:",
                            note.noteDatetime?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    noteTable.addCell(createLabelValueCell("Note:", note.noteText, inTable = true))
                    document.add(noteTable)
                }
            } else {
                val paragraph = Paragraph("No procedures recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addProceduresSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.procedures?.let { procedures ->
            document.add(createSectionTitle("Procedures"))
            if (procedures.isNotEmpty()) {
                procedures.forEach { procedure ->
                    val procedureTable = PdfPTable(2)
                    procedureTable.widthPercentage = 100f
                    setupDataItemTable(procedureTable)
                    val headerText = "${procedure.cptDescription ?: "Procedure"} (${procedure.cptCode ?: "N/A"})"
                    procedureTable.addCell(createDataItemHeaderCell(headerText, 2))
                    procedureTable.addCell(createLabelValueCell("Caregiver:", procedure.caregiver, inTable = true))
                    procedureTable.addCell(
                        createLabelValueCell(
                            "Status:",
                            if (procedure.active == 1) "Active" else "Inactive",
                            inTable = true
                        )
                    )
                    procedureTable.addCell(createLabelValueCell("Modifier 1:", procedure.modifier1, inTable = true))
                    procedureTable.addCell(createLabelValueCell("Modifier 2:", procedure.modifier2, inTable = true))
                    procedureTable.addCell(createLabelValueCell("Modifier 3:", procedure.modifier3, inTable = true))
                    procedureTable.addCell(createLabelValueCell("Modifier 4:", procedure.modifier4, inTable = true))
                    procedureTable.addCell(
                        createLabelValueCell(
                            "Timestamp:",
                            procedure.timestamp?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    document.add(procedureTable)
                }
            } else {
                val paragraph = Paragraph("No procedures recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addHomeMedicationsSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.homeMedications?.let { homeMedications ->
            document.add(createSectionTitle("Home Medications"))
            if (homeMedications.isNotEmpty()) {
                homeMedications.forEach { homeMed ->
                    val homeMedTable = PdfPTable(2)
                    homeMedTable.widthPercentage = 100f
                    setupDataItemTable(homeMedTable)

                    val headerText = homeMed.medication ?: "Medication"
                    homeMedTable.addCell(createDataItemHeaderCell(headerText, 2))

                    homeMedTable.addCell(createLabelValueCell("Dosage:", homeMed.dosage, inTable = true))
                    homeMedTable.addCell(createLabelValueCell("Route:", homeMed.route, inTable = true))
                    homeMedTable.addCell(createLabelValueCell("Frequency:", homeMed.frequency, inTable = true))
                    homeMedTable.addCell(
                        createLabelValueCell(
                            "Start Date:",
                            homeMed.startDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    homeMedTable.addCell(
                        createLabelValueCell(
                            "End Date:",
                            homeMed.endDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )

                    document.add(homeMedTable)
                }
            } else {
                val paragraph = Paragraph("No home medications recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addAllergiesSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.allergies?.let { allergies ->
            document.add(createSectionTitle("Allergies & Adverse Reactions"))
            if (allergies.isNotEmpty()) {
                allergies.forEach { allergy ->
                    val allergyTable = PdfPTable(2)
                    allergyTable.widthPercentage = 100f
                    setupDataItemTable(allergyTable)

                    val headerText = "${allergy.allergyDescription} (${allergy.allergyCode ?: "N/A"})"
                    allergyTable.addCell(createDataItemHeaderCell(headerText, 2))

                    allergyTable.addCell(createLabelValueCell("Reaction:", allergy.reaction, inTable = true))
                    allergyTable.addCell(createLabelValueCell("Severity:", allergy.severity, inTable = true))
                    allergyTable.addCell(createLabelValueCell("Type:", allergy.allergyType, inTable = true))
                    allergyTable.addCell(createLabelValueCell("System:", allergy.systemName, inTable = true))
                    allergyTable.addCell(
                        createLabelValueCell(
                            "Start Date:",
                            allergy.allergyStartDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    allergyTable.addCell(
                        createLabelValueCell(
                            "End Date:",
                            allergy.allergyEndDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )

                    document.add(allergyTable)
                }
            } else {
                val paragraph = Paragraph("No allergies or adverse reactions recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addMicroSusceptibilitySection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.microSuscept?.let { microSuscepts ->
            document.add(createSectionTitle("Microbiology Susceptibility"))
            if (microSuscepts.isNotEmpty()) {
                microSuscepts.forEach { suscept ->
                    val susceptTable = PdfPTable(2)
                    susceptTable.widthPercentage = 100f
                    setupDataItemTable(susceptTable)

                    val headerText = suscept.antibodyDescription ?: "Susceptibility Test"
                    susceptTable.addCell(createDataItemHeaderCell(headerText, 2))

                    susceptTable.addCell(createLabelValueCell("Accession #:", suscept.accessionNumber, inTable = true))
                    susceptTable.addCell(createLabelValueCell("Isolate:", suscept.accessionIsolate, inTable = true))
                    susceptTable.addCell(
                        createLabelValueCell(
                            "Result:",
                            "${suscept.resultQualifierCode ?: ""} ${suscept.resultValueNumber ?: ""}".trim(),
                            inTable = true
                        )
                    )
                    susceptTable.addCell(createLabelValueCell("Category:", suscept.categoryDescription, inTable = true))
                    susceptTable.addCell(createLabelValueCell("Entered By:", suscept.enteredBy, inTable = true))
                    susceptTable.addCell(
                        createLabelValueCell(
                            "Entered Date:",
                            suscept.enteredDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )

                    document.add(susceptTable)
                }
            } else {
                val paragraph = Paragraph("No microbiology susceptibility results recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addMicroDetailsSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.microDetail?.let { microDetails ->
            document.add(createSectionTitle("Microbiology Details"))
            if (microDetails.isNotEmpty()) {
                microDetails.forEach { detail ->
                    val detailTable = PdfPTable(2)
                    detailTable.widthPercentage = 100f
                    setupDataItemTable(detailTable)

                    val headerText = "${detail.orderCodeDescription ?: "Micro Detail"} (${detail.orderCode ?: "N/A"})"
                    detailTable.addCell(createDataItemHeaderCell(headerText, 2))

                    // Create a cell for the result text that spans both columns if it exists
                    if (!detail.resultText.isNullOrBlank()) {
                        val resultCell = PdfPCell(Paragraph(detail.resultText, FONT_BODY))
                        resultCell.colspan = 2
                        resultCell.border = Rectangle.NO_BORDER
                        resultCell.setPadding(5f)
                        detailTable.addCell(resultCell)
                    }

                    detailTable.addCell(createLabelValueCell("Accession #:", detail.accessionNumber, inTable = true))
                    detailTable.addCell(createLabelValueCell("Isolate:", detail.accessionIsolate, inTable = true))
                    detailTable.addCell(createLabelValueCell("Result Type:", detail.resultType, inTable = true))
                    detailTable.addCell(
                        createLabelValueCell(
                            "Sequence #:",
                            detail.resultSequenceNumber?.toString(),
                            inTable = true
                        )
                    )
                    detailTable.addCell(createLabelValueCell("Released By:", detail.releasedBy, inTable = true))
                    detailTable.addCell(
                        createLabelValueCell(
                            "Release Date:",
                            detail.releaseDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )

                    document.add(detailTable)
                }
            } else {
                val paragraph = Paragraph("No microbiology details recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addMicroCommentsSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.microComments?.let { microComments ->
            document.add(createSectionTitle("Microbiology Comments"))
            if (microComments.isNotEmpty()) {
                microComments.forEach { comment ->
                    val commentTable = PdfPTable(2)
                    commentTable.widthPercentage = 100f
                    setupDataItemTable(commentTable)

                    val headerText = "${comment.commentType ?: "Comment"} (Seq: ${comment.sequenceNumber ?: "N/A"})"
                    commentTable.addCell(createDataItemHeaderCell(headerText, 2))

                    // Create a cell for the comment description that spans both columns
                    val descriptionCell = PdfPCell(Paragraph(comment.commentDescription ?: "N/A", FONT_BODY))
                    descriptionCell.colspan = 2
                    descriptionCell.border = Rectangle.NO_BORDER
                    descriptionCell.setPadding(5f)
                    commentTable.addCell(descriptionCell)

                    commentTable.addCell(createLabelValueCell("Accession #:", comment.accessionNumber, inTable = true))
                    commentTable.addCell(createLabelValueCell("Isolate:", comment.accessionIsolate, inTable = true))
                    commentTable.addCell(createLabelValueCell("Entered By:", comment.enteredBy, inTable = true))
                    commentTable.addCell(
                        createLabelValueCell(
                            "Entered Date:",
                            comment.enteredDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )

                    document.add(commentTable)
                }
            } else {
                val paragraph = Paragraph("No microbiology comments recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    private fun addAlertsSection(
        sections: VisitSectionsViewModel?,
        document: Document,
    ) {
        sections?.clinical?.alerts?.let { alerts ->
            document.add(createSectionTitle("Alerts"))
            if (alerts.isNotEmpty()) {
                alerts.forEach { alert ->
                    val alertTable = PdfPTable(2)
                    alertTable.widthPercentage = 100f
                    setupDataItemTable(alertTable)
                    val headerText = "${alert.alertDescription ?: "Alert"} (${alert.alertCode ?: "N/A"})"
                    alertTable.addCell(createDataItemHeaderCell(headerText, 2))
                    alertTable.addCell(
                        createLabelValueCell(
                            "Alert Date:",
                            alert.alertDate?.let { formatDateTime(it) },
                            inTable = true
                        )
                    )
                    document.add(alertTable)
                }
            } else {
                val paragraph = Paragraph("No alerts recorded for this visit.", FONT_BODY)
                paragraph.spacingAfter = 25f
                document.add(paragraph)
            }
        }
    }

    // --- Helper Methods for Creating PDF Elements ---

    private fun createSectionTitle(title: String): Paragraph {
        val p = Paragraph(title, FONT_H3)
        p.spacingAfter = 5f
        // Create a line under the title
        val line = PdfPTable(1)
        line.widthPercentage = 100f
        val cell = PdfPCell()
        cell.border = Rectangle.NO_BORDER
        cell.borderWidthBottom = 2f
        cell.borderColorBottom = COLOR_PRIMARY
        line.addCell(cell)
        p.add(line)
        p.spacingAfter = 10f
        return p
    }

    private fun createLabelValueCell(label: String, value: String?, inTable: Boolean = false): PdfPCell {
        return createLabelValueCell(label, Chunk(value ?: "N/A", FONT_BODY), inTable)
    }

    private fun createLabelValueCell(label: String, valueChunk: Chunk, inTable: Boolean = false): PdfPCell {
        val p = Paragraph()
        p.add(Chunk(label, FONT_LABEL))
        p.add(Chunk(" "))
        p.add(valueChunk)

        val cell = PdfPCell(p)
        if (inTable) {
            cell.border = Rectangle.NO_BORDER
        } else {
            cell.border = Rectangle.NO_BORDER
            cell.paddingBottom = 5f
        }
        return cell
    }

    private fun setupDataItemTable(table: PdfPTable) {
        table.defaultCell.border = Rectangle.BOX
        table.defaultCell.borderColor = COLOR_BORDER
        table.defaultCell.backgroundColor = COLOR_BACKGROUND_MUTED
        table.defaultCell.setPadding(5f)
        table.setSpacingAfter(10f)
    }

    private fun createDataItemHeaderCell(text: String, colspan: Int): PdfPCell {
        val headerCell = PdfPCell(Paragraph(text, FONT_BODY_BOLD))
        headerCell.colspan = colspan
        headerCell.border = Rectangle.BOTTOM
        headerCell.borderColorBottom = COLOR_PRIMARY
        headerCell.paddingBottom = 5f
        headerCell.backgroundColor = COLOR_BACKGROUND_MUTED
        return headerCell
    }

    private fun createTableHeaderCell(text: String): PdfPCell {
        val cell = PdfPCell(Paragraph(text, FONT_BODY_BOLD))
        cell.backgroundColor = COLOR_BACKGROUND_MUTED
        cell.border = Rectangle.BOX
        cell.borderColor = COLOR_BORDER
        cell.setPadding(5f)
        cell.horizontalAlignment = Element.ALIGN_CENTER
        return cell
    }

    private fun createTableDataCell(text: String): PdfPCell {
        val cell = PdfPCell(Paragraph(text, FONT_BODY))
        cell.border = Rectangle.BOX
        cell.borderColor = COLOR_BORDER
        cell.setPadding(5f)
        return cell
    }

    // --- Helper Methods for Formatting ---
    private fun formatDate(date: LocalDate?): String = date?.format(DATE_FORMATTER) ?: "N/A"
    private fun formatDateTime(dateTime: LocalDateTime?): String = dateTime?.format(DATETIME_FORMATTER) ?: "N/A"
}
