# IDE files
.idea/
.vscode/
*.iml

# Build artifacts
build/
.gradle/
target/
.kotlin

# Local environment files
.env
env.json
/local.properties
local.properties

# Logs
*.log

# OS specific
.DS_Store

# IDEA project files, because the project can be imported from settings.gradle.kts
*.iml
.idea/*
!.idea/copyright
# Keep the code styles.
!/.idea/codeStyles
/.idea/codeStyles/*
!/.idea/codeStyles/Project.xml
!/.idea/codeStyles/codeStyleConfig.xml

# Gradle cache
.gradle

Thumbs.db
.micronaut/
*.ipr
*.iws
.project
.settings
.classpath
.factorypath

# CDK asset staging directory
.cdk.staging
cdk.out/

# Tooling
*aider*
typescript
