package com.cha.security

import io.micronaut.context.annotation.Replaces
import io.micronaut.http.HttpRequest
import io.micronaut.inject.ExecutableMethod
import io.micronaut.management.endpoint.EndpointSensitivityProcessor
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.rules.SecurityRuleResult
import io.micronaut.security.rules.SensitiveEndpointRule
import io.micronaut.security.token.RolesFinder
import jakarta.inject.Singleton
import org.reactivestreams.Publisher
import reactor.core.publisher.Mono

/**
 * to allow authenticated users access to sensitive endpoints we replace
 * the default implementation [SensitiveEndpointRule].
 *
 * @see
 *    https://micronaut-projects.github.io/micronaut-security/latest/guide/#builtInEndpointsAccess
 * @see https://docs.micronaut.io/latest/guide/index.html#providedEndpoints
 */
@Replaces(SensitiveEndpointRule::class)
@Singleton
class SensitiveEndpointRuleReplacement(
    endpointSensitivityProcessor: EndpointSensitivityProcessor,
    private val rolesFinder: RolesFinder
) : SensitiveEndpointRule(endpointSensitivityProcessor) {

    override fun checkSensitiveAuthenticated(
        request: HttpRequest<*>,
        authentication: Authentication,
        method: ExecutableMethod<*, *>
    ): Publisher<SecurityRuleResult> {
        // Allow Authorized users access to sensitive endpoints
        return Mono.just(
            if (rolesFinder.hasAnyRequiredRoles(listOf("ROLE_ADMIN"), authentication.roles)) SecurityRuleResult.ALLOWED
            else SecurityRuleResult.REJECTED
        )
    }
}
