package com.cha.model.dto

import com.cha.model.entity.InvoiceEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime
import java.util.Currency

@Serdeable
@Schema(description = "Patient invoice information")
data class Invoice(
    @Schema(description = "Patient's Enterprise Master Patient Index", example = "12345")
    val empi: Int?,
    @Schema(description = "System identifier", example = "1")
    val systemId: Int?,
    @Schema(description = "Medical Record Number", example = "MRN123456")
    val mrn: String?,
    @Schema(description = "Account number", example = "ACC789012")
    val accountNumber: String?,
    @Schema(description = "Invoice number", example = "INV2024001")
    val invoiceNumber: String?,
    @Schema(description = "Accounts receivable status", example = "Open")
    val arStatus: String?,
    @Schema(description = "Billing date", example = "2024-01-15T10:30:00")
    val billingDate: LocalDateTime?,
    @Schema(description = "Service from date", example = "2024-01-10T08:00:00")
    val serviceFromDate: LocalDateTime?,
    @Schema(description = "Service through date", example = "2024-01-12T17:00:00")
    val serviceThruDate: LocalDateTime?,
    @Schema(description = "Total billed amount")
    val totalBilled: Money?,
    @Schema(description = "Insurance payments amount")
    val insurancePayments: Money?,
    @Schema(description = "Coinsurance payments amount")
    val coinsurancePayments: Money?,
    @Schema(description = "Patient payments amount")
    val patientPayments: Money?,
    @Schema(description = "Patient payment amount")
    val patientPaymentAmount: Money?,
    @Schema(description = "Patient adjustment amount")
    val patientAdjustmentAmount: Money?,
    @Schema(description = "Insurance payment amount")
    val insurancePaymentAmount: Money?,
    @Schema(description = "Insurance adjustment amount")
    val insuranceAdjustmentAmount: Money?,
    @Schema(description = "Insurance plan name", example = "Blue Cross Blue Shield")
    val insurancePlan: String?,
    @Schema(description = "Outstanding balance")
    val balance: Money?,
    @Schema(description = "System name where invoice originates", example = "HIS")
    val systemName: String?,
    @Schema(description = "Security level flag", example = "false")
    val securityLevel: Boolean?,
    @Schema(description = "Admission date", example = "2024-01-10T08:00:00")
    val admitDate: LocalDateTime?,
)

fun InvoiceEntity.toDto(): Invoice {
    val usd = Currency.getInstance("USD")
    return Invoice(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        invoiceNumber = this.invoiceNumber,
        arStatus = this.arStatus,
        billingDate = this.billingDate,
        serviceFromDate = this.serviceFromDate,
        serviceThruDate = this.serviceThruDate,
        totalBilled = this.totalBilled?.let { Money(it, usd) },
        insurancePayments = this.insurancePayments?.let { Money(it, usd) },
        coinsurancePayments = this.coinsurancePayments?.let { Money(it, usd) },
        patientPayments = this.patientPayments?.let { Money(it, usd) },
        patientPaymentAmount = this.patientPaymentAmount?.let { Money(it, usd) },
        patientAdjustmentAmount = this.patientAdjustmentAmount?.let { Money(it, usd) },
        insurancePaymentAmount = this.insurancePaymentAmount?.let { Money(it, usd) },
        insuranceAdjustmentAmount = this.insuranceAdjustmentAmount?.let { Money(it, usd) },
        insurancePlan = this.insurancePlan,
        balance = this.balance?.let { Money(it, usd) },
        systemName = this.systemName,
        securityLevel = this.securityLevel,
        admitDate = this.admitDate
    )
}