package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable

private fun addBillsSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.financial?.bills?.let { bills ->
        document.add(createSectionTitle("Bills"))
        if (bills.isNotEmpty()) {
            bills.forEach { bill ->
                val billTable = PdfPTable(2)
                billTable.widthPercentage = 100f
                setupDataItemTable(billTable)

                val headerText = "Transaction Code: ${bill.chargeCode} (${bill.chargeCodeDescription ?: "N/A"})"
                billTable.addCell(createDataItemHeaderCell(headerText, 2))

                billTable.addCell(createLabelValueCell("Amount:", bill.amount.toString(), inTable = true))
                billTable.addCell(
                    createLabelValueCell(
                        "Total Amount:",
                        bill.totalAmount.toString(),
                        inTable = true
                    )
                )
                billTable.addCell(createLabelValueCell("Invoice #:", bill.invoiceNumber ?: "N/A", inTable = true))
                billTable.addCell(createLabelValueCell("Insurance:", bill.insurance ?: "N/A", inTable = true))
                billTable.addCell(
                    createLabelValueCell(
                        "Financial Class:",
                        bill.financialClass ?: "N/A",
                        inTable = true
                    )
                )
                billTable.addCell(createLabelValueCell("Department:", bill.department ?: "N/A", inTable = true))
                billTable.addCell(createLabelValueCell("NRV:", bill.nrv ?: "N/A", inTable = true))
                billTable.addCell(
                    createLabelValueCell(
                        "Transaction Type:",
                        bill.transactionType ?: "N/A",
                        inTable = true
                    )
                )
                billTable.addCell(createLabelValueCell("Visit Status:", bill.visitStatus ?: "N/A", inTable = true))
                billTable.addCell(createLabelValueCell("Units:", bill.units.toString(), inTable = true))
                billTable.addCell(createLabelValueCell("Account:", bill.account ?: "N/A", inTable = true))
                billTable.addCell(
                    createLabelValueCell(
                        "Service Date:",
                        bill.serviceDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                billTable.addCell(
                    createLabelValueCell(
                        "Posting Date:",
                        bill.postingDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                billTable.addCell(
                    createLabelValueCell(
                        "Transaction Date:",
                        bill.transactionDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )

                document.add(billTable)
            }
        } else {
            val paragraph = Paragraph("No billing information recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}