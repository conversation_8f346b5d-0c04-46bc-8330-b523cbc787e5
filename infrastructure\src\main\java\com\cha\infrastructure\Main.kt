package com.cha.infrastructure

import io.github.cdklabs.cdknag.AwsSolutionsChecks
import io.github.cdklabs.cdknag.HIPAASecurityChecks
import software.amazon.awscdk.App
import software.amazon.awscdk.AppProps
import software.amazon.awscdk.Environment
import software.amazon.awscdk.StackProps
import software.amazon.awscdk.services.ec2.Peer
import software.amazon.awscdk.services.ec2.Port
import java.nio.file.Files
import java.nio.file.Paths
import java.util.regex.Pattern

/** Reads the viewer version from libs.versions.toml */
fun readAppVersion(): String {
    val tomlPath = Paths.get("../gradle/libs.versions.toml")
    val pattern = Pattern.compile("viewer\\s*=\\s*\"([^\"]+)\"")

    Files.lines(tomlPath).use { lines ->
        for (line in lines) {
            val matcher = pattern.matcher(line)
            if (matcher.find()) {
                return matcher.group(1)
            }
        }
    }

    // If the version is not found.
    return "0.0.1-SNAPSHOT"
}

fun main() {
    println("Initializing the CDK application...")

    val app = App(
        AppProps.Builder()
            .analyticsReporting(false)
            .build()
    )

    val appEnv = System.getenv("APP_ENV") ?: System.getenv("MICRONAUT_ENVIRONMENTS") ?: "staging"
    println("Current app environment (APP_ENV or MICRONAUT_ENVIRONMENTS): $appEnv")

    // Read the app version from libs.versions.toml
    val appVersion = readAppVersion()
    println("App version: $appVersion")

    // Define environment variables - fetch from system environment or context
    val allowedCorsOrigins = System.getenv("ALLOWED_CORS_ORIGINS")
    val bucketName = System.getenv("APP_BUCKET_NAME")
        ?: throw IllegalArgumentException("Missing environment variable: APP_BUCKET_NAME")
    val oauthClientId = System.getenv("OAUTH_CLIENT_ID") ?: "defaultClient" // Replace with secure retrieval
    val cognitoPoolId = System.getenv("COGNITO_POOL_ID") ?: "defaultPool"

    // Get AWS account and region for environment
    val awsAccountId = System.getenv("AWS_ACCOUNT_ID")
        ?: throw IllegalArgumentException("Missing environment variable: AWS_ACCOUNT_ID")
    val awsRegion = System.getenv("AWS_REGION") ?: "us-east-1"


    println("Environment Variables:")
    println("AWS_ACCOUNT_ID: $awsAccountId")
    println("ALLOWED_CORS_ORIGINS: $allowedCorsOrigins")
    println("OAUTH_CLIENT_ID: $oauthClientId")
    println("COGNITO_POOL_ID: $cognitoPoolId")
    println("APP_BUCKET_NAME: $bucketName")
    println("")

    // Create the AWS environment for the stack
    val env = Environment.builder()
        .account(awsAccountId)
        .region(awsRegion)
        .build()

    // Create environment-specific variables map
    val environmentVariables = mapOf(
        "OAUTH_CLIENT_ID" to oauthClientId,
        "COGNITO_POOL_ID" to cognitoPoolId,
        "MICRONAUT_ENVIRONMENTS" to appEnv, // Inform Micronaut of the current environment
        "APP_BUCKET_NAME" to bucketName,
        "ALLOWED_CORS_ORIGINS" to allowedCorsOrigins
    )

    // Create infrastructure stack
    val infrastructureStack = InfrastructureStack(
        app,
        "viewer-infrastructure-$appEnv",
        StackProps.builder()
            .env(env)
            .build(),
        appEnv,
        environmentVariables
    )

    // Create application stack that depends on infrastructure
    val applicationStack = ApplicationStack(
        app,
        "viewer-application-$appEnv",
        StackProps.builder()
            .env(env)
            .build(),
        infrastructureStack.vpc,
        infrastructureStack.documentsBucket,
        infrastructureStack.loggingBucket,
        infrastructureStack.certificate,
        infrastructureStack.hostedZone,
        appEnv,
        environmentVariables,
        appVersion
    )

    // Set up the security group rules between Fargate and RDS
    // This enables the Fargate tasks to connect to the RDS instance

    // Add ingress rule to the actual RDS security group in the RDS VPC
    // This allows traffic from the Fargate service to the RDS instance
    infrastructureStack.actualRdsSecurityGroup.addIngressRule(
        Peer.ipv4(infrastructureStack.vpc.vpcCidrBlock), // Allow traffic from the entire Fargate VPC CIDR block
        Port.tcp(1443),  // RDS port
        "Allow inbound from Fargate VPC to RDS on port 1443"
    )

    // Allow the networking to be properly set up for Fargate->RDS communication
    println("Security group rules configured for Fargate->RDS communication")
    println("RDS Security Group ID: ${infrastructureStack.actualRdsSecurityGroup.securityGroupId}")
    println("Fargate Security Group ID: ${applicationStack.fargateSecurityGroup.securityGroupId}")
    println("Created ingress rule to allow traffic from ${infrastructureStack.vpc.vpcCidrBlock} to RDS on port 1443")

    // https://github.com/cdklabs/cdk-nag/blob/main/RULES.md#awssolutions
    val awsSolutionsChecks = AwsSolutionsChecks.Builder
        .create()
        .verbose(true)
        .build()

    // https://github.com/cdklabs/cdk-nag/blob/main/RULES.md#hipaa-security
    val hIPPASecurityChecks = HIPAASecurityChecks.Builder
        .create()
        .verbose(true)
        .build()

    // TODO: Resolve the issues with the AWS Solutions and HIPAA security checks
    //  and reenable these aspects
//    Aspects.of(app).add(awsSolutionsChecks)
//    Aspects.of(app).add(hIPPASecurityChecks)

    app.synth()
}
