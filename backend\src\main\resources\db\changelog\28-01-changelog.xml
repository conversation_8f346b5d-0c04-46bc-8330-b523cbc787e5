<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.31.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="*************-1" author="cameron">
        <createTable tableName="patient_addresses">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_addresses1"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="empi" type="INT"/>
            <column name="address" type="VARCHAR(80)"/>
            <column name="city" type="VARCHAR(80)"/>
            <column name="state" type="VARCHAR(10)"/>
            <column name="zip_code" type="VARCHAR(12)"/>
            <column name="telephone" type="VARCHAR(30)"/>
            <column name="active" type="INT"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-2" author="cameron">
        <createTable tableName="patient_alerts">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="empi" type="INT"/>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="alert_code" type="VARCHAR(20)"/>
            <column name="alert_description" type="VARCHAR(255)"/>
            <column name="alert_date" type="datetime"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column name="admit_date" type="datetime"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-3" author="cameron">
        <createTable tableName="patient_allergies">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_par_allergies"/>
            </column>
            <column name="empi" type="INT"/>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="severity" type="VARCHAR(10)"/>
            <column name="allergy_start_date" type="datetime"/>
            <column name="allergy_end_date" type="datetime"/>
            <column name="reaction" type="VARCHAR(255)"/>
            <column name="allergy_code" type="VARCHAR(20)"/>
            <column name="allergy_type" type="VARCHAR(50)"/>
            <column name="allergy_description" type="VARCHAR(80)"/>
            <column name="admit_date" type="datetime"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-4" author="cameron">
        <createTable tableName="patient_bills">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="service_date" type="datetime"/>
            <column name="posting_date" type="datetime"/>
            <column name="charge_code" type="VARCHAR(50)"/>
            <column name="charge_code_description" type="VARCHAR(80)"/>
            <column name="invoice_number" type="VARCHAR(50)"/>
            <column name="nrv" type="VARCHAR(5)"/>
            <column name="department" type="VARCHAR(50)"/>
            <column name="account" type="VARCHAR(50)"/>
            <column name="amount" type="DECIMAL"/>
            <column name="insurance" type="VARCHAR(50)"/>
            <column name="total_amount" type="DECIMAL"/>
            <column name="units" type="INT"/>
            <column name="financial_class" type="VARCHAR(50)"/>
            <column name="visit_status" type="VARCHAR(50)"/>
            <column name="transaction_type" type="VARCHAR(50)"/>
            <column name="transaction_date" type="datetime"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-5" author="cameron">
        <createTable tableName="patient_charges">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="service_date" type="datetime"/>
            <column name="posting_date" type="datetime"/>
            <column name="charge_code_external_id" type="VARCHAR(50)"/>
            <column name="charge_description" type="VARCHAR(80)"/>
            <column name="amount" type="DECIMAL"/>
            <column name="units" type="SMALLINT"/>
            <column name="nrv_code" type="VARCHAR(50)"/>
            <column name="total_amount" type="DECIMAL"/>
            <column name="caregiver" type="VARCHAR(80)"/>
            <column name="financial_class" type="VARCHAR(50)"/>
            <column name="patient_type" type="VARCHAR(50)"/>
            <column name="department" type="VARCHAR(50)"/>
            <column name="visit_status" type="VARCHAR(50)"/>
            <column name="empi" type="INT"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-6" author="cameron">
        <createTable tableName="patient_diagnosis">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="icd_code" type="VARCHAR(10)"/>
            <column name="icd_description" type="VARCHAR(255)"/>
            <column name="rank" type="INT"/>
            <column name="type" type="VARCHAR(5)"/>
            <column name="sequence" type="INT"/>
            <column name="caregiver" type="VARCHAR(50)"/>
            <column name="procedure_date" type="datetime"/>
            <column defaultValueNumeric="1" name="active" type="INT"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column name="admit_date" type="datetime"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-7" author="cameron">
        <createTable tableName="patient_documents">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="created" type="date"/>
            <column name="category" type="VARCHAR(255)"/>
            <column name="title" type="VARCHAR(255)"/>
            <column name="filename" type="VARCHAR(256)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column defaultValueComputed="0" name="progress_note" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-8" author="cameron">
        <createTable tableName="patient_home_meds">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="brand_name" type="VARCHAR(30)"/>
            <column name="generic_name" type="VARCHAR(60)"/>
            <column name="dosage" type="VARCHAR(45)"/>
            <column name="dosage_form" type="VARCHAR(40)"/>
            <column name="frequence" type="VARCHAR(20)"/>
            <column name="start_datetime" type="datetime"/>
            <column name="stop_datetime" type="datetime"/>
            <column name="caregiver" type="VARCHAR(41)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-9" author="cameron">
        <createTable tableName="patient_immunizations">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK__paragon___3213E83F77D716AA"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="immunization" type="VARCHAR(255)"/>
            <column name="lot_number" type="VARCHAR(255)"/>
            <column name="manufacturer" type="VARCHAR(255)"/>
            <column name="service_datetime" type="datetime"/>
            <column name="immunization_datetime" type="datetime"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-10" author="cameron">
        <createTable tableName="patient_insurance">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK__patient___3213E83F1D77001A1"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="insurance_plan" type="VARCHAR(50)"/>
            <column name="ins_seq" type="VARCHAR(24)"/>
            <column name="policy_holder" type="VARCHAR(80)"/>
            <column name="policy_number" type="VARCHAR(50)"/>
            <column name="certificate_no" type="VARCHAR(50)"/>
            <column name="organization_name" type="VARCHAR(255)"/>
            <column name="policy_holder_ssn" type="VARCHAR(16)"/>
            <column name="group_number" type="VARCHAR(50)"/>
            <column name="group_name" type="VARCHAR(255)"/>
            <column name="subscriber_number" type="VARCHAR(50)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-11" author="cameron">
        <createTable tableName="patient_invoice_notes">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="note_date" type="datetime"/>
            <column name="patient_note" type="VARCHAR(**********)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-12" author="cameron">
        <createTable tableName="patient_invoices">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="invoice_number" type="VARCHAR(50)"/>
            <column name="ar_status" type="VARCHAR(50)"/>
            <column name="billing_date" type="datetime"/>
            <column name="service_from_date" type="datetime"/>
            <column name="service_thru_date" type="datetime"/>
            <column name="total_billed" type="DECIMAL"/>
            <column name="insurance_payments" type="DECIMAL"/>
            <column name="coinsurance_payments" type="DECIMAL"/>
            <column name="patient_payments" type="DECIMAL"/>
            <column name="patient_payment_amount" type="DECIMAL"/>
            <column name="patient_adjustment_amount" type="DECIMAL"/>
            <column name="insurance_payment_amount" type="DECIMAL"/>
            <column name="insurance_adjustment_amount" type="DECIMAL"/>
            <column name="insurance_plan" type="VARCHAR(50)"/>
            <column name="balance" type="DECIMAL"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-13" author="cameron">
        <createTable tableName="patient_lab_orders">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="accession" type="VARCHAR(50)"/>
            <column name="order_code" type="VARCHAR(50)"/>
            <column name="order_description" type="VARCHAR(80)"/>
            <column name="empi" type="INT"/>
            <column defaultValueNumeric="1" name="active" type="INT"/>
            <column name="service_datetime" type="datetime"/>
            <column name="collected_datetime" type="datetime"/>
            <column name="received_datetime" type="datetime"/>
            <column name="released_datetime" type="datetime"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-14" author="cameron">
        <createTable tableName="patient_lab_results">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="accession" type="VARCHAR(50)"/>
            <column name="order_code" type="VARCHAR(50)"/>
            <column name="order_description" type="VARCHAR(80)"/>
            <column name="sequence" type="SMALLINT"/>
            <column name="test_abbr" type="VARCHAR(50)"/>
            <column name="test_description" type="VARCHAR(50)"/>
            <column name="numeric_lab_result" type="FLOAT"/>
            <column name="alpha_lab_result" type="CHAR(80)"/>
            <column name="units" type="VARCHAR(50)"/>
            <column name="status" type="VARCHAR(50)"/>
            <column name="result_flag" type="VARCHAR(50)"/>
            <column name="abn_flag" type="VARCHAR(50)"/>
            <column name="released_by" type="VARCHAR(50)"/>
            <column name="empi" type="INT"/>
            <column name="service_datetime" type="datetime"/>
            <column name="collected_datetime" type="datetime"/>
            <column name="received_datetime" type="datetime"/>
            <column name="released_datetime" type="datetime"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-15" author="cameron">
        <createTable tableName="patient_medications">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="brand_name" type="VARCHAR(30)"/>
            <column name="generic_name" type="VARCHAR(60)"/>
            <column name="dosage" type="VARCHAR(45)"/>
            <column name="dosage_form" type="VARCHAR(40)"/>
            <column name="frequence" type="VARCHAR(20)"/>
            <column name="start_datetime" type="datetime"/>
            <column name="stop_datetime" type="datetime"/>
            <column name="caregiver" type="VARCHAR(41)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-16" author="cameron">
        <createTable tableName="patient_micro_comments">
            <column name="empi" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="accession_number" type="VARCHAR(20)"/>
            <column name="accession_isolate" type="VARCHAR(22)"/>
            <column name="isolate_number" type="INT"/>
            <column name="comment_type" type="VARCHAR(1)"/>
            <column name="sequence_number" type="INT"/>
            <column name="comment_description" type="VARCHAR(80)"/>
            <column name="entered_date" type="datetime"/>
            <column name="entered_by" type="VARCHAR(45)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-17" author="cameron">
        <createTable tableName="patient_micro_detail">
            <column name="empi" type="INT"/>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="order_code" type="VARCHAR(20)"/>
            <column name="order_code_description" type="VARCHAR(80)"/>
            <column name="accession_number" type="VARCHAR(20)"/>
            <column name="accession_isolate" type="VARCHAR(22)"/>
            <column name="result_type_code" type="VARCHAR(2)"/>
            <column name="isolate_number" type="INT"/>
            <column name="result_sequence_number" type="INT"/>
            <column name="result_text" type="VARCHAR(80)"/>
            <column name="released_by" type="VARCHAR(45)"/>
            <column name="release_date" type="datetime"/>
            <column name="result_type" type="VARCHAR(2)"/>
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-18" author="cameron">
        <createTable tableName="patient_micro_orders">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="empi" type="INT"/>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="order_code" type="VARCHAR(20)"/>
            <column name="order_code_description" type="VARCHAR(80)"/>
            <column name="accession_number" type="VARCHAR(20)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-19" author="cameron">
        <createTable tableName="patient_micro_suscept">
            <column name="empi" type="INT"/>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="accession_number" type="VARCHAR(20)"/>
            <column name="isolate_number" type="INT"/>
            <column name="accession_isolate" type="VARCHAR(22)"/>
            <column name="antibody_description" type="VARCHAR(40)"/>
            <column name="result_qualifier_code" type="CHAR(2)"/>
            <column name="result_value_number" type="CHAR(20)"/>
            <column name="category_code" type="VARCHAR(20)"/>
            <column name="category_description" type="VARCHAR(40)"/>
            <column name="entered_by" type="VARCHAR(45)"/>
            <column name="entered_date" type="datetime"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-20" author="cameron">
        <createTable tableName="patient_mpi">
            <column autoIncrement="true" name="index_id" type="INT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK__cha_pati__9D4F3187DC29957D1"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="last_name" type="VARCHAR(80)"/>
            <column name="first_name" type="VARCHAR(80)"/>
            <column name="middle_name" type="VARCHAR(80)"/>
            <column name="bth_ts" type="date"/>
            <column name="sex" type="VARCHAR(1)"/>
            <column name="mpi_index" type="VARCHAR(255)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-21" author="cameron">
        <createTable tableName="patient_notes">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="empi" type="INT"/>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="note_datetime" type="datetime"/>
            <column name="private_note_flag" type="VARCHAR(1)"/>
            <column name="note_text" type="VARCHAR(**********)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-22" author="cameron">
        <createTable tableName="patient_orders">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="empi" type="INT"/>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(20)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="order_number" type="INT"/>
            <column name="ordered_by" type="VARCHAR(45)"/>
            <column name="verified_by" type="VARCHAR(45)"/>
            <column name="discontinued_by" type="VARCHAR(45)"/>
            <column name="caregiver" type="VARCHAR(45)"/>
            <column name="frequency" type="VARCHAR(80)"/>
            <column name="entered_datetime" type="VARCHAR(20)"/>
            <column name="priority" type="VARCHAR(50)"/>
            <column name="special_instructions_1" type="VARCHAR(120)"/>
            <column name="special_instructions_2" type="VARCHAR(120)"/>
            <column name="special_instructions_3" type="VARCHAR(120)"/>
            <column name="order_status_code" type="CHAR(1)"/>
            <column name="order_code" type="CHAR(20)"/>
            <column name="order_code_description" type="VARCHAR(80)"/>
            <column name="collected_datetime" type="datetime"/>
            <column name="collected_by" type="VARCHAR(45)"/>
            <column name="service_datetime" type="datetime"/>
            <column name="received_datetime" type="datetime"/>
            <column name="released_datetime" type="datetime"/>
            <column name="released_by" type="VARCHAR(45)"/>
            <column name="accession_number" type="VARCHAR(20)"/>
            <column name="order_type_id" type="INT"/>
            <column name="order_type_description" type="VARCHAR(40)"/>
            <column name="order_area" type="VARCHAR(50)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-23" author="cameron">
        <createTable tableName="patient_payments">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="invoice_number" type="VARCHAR(50)"/>
            <column name="nrv" type="VARCHAR(5)"/>
            <column name="charge_code" type="VARCHAR(50)"/>
            <column name="charge_description" type="VARCHAR(50)"/>
            <column name="insurance" type="VARCHAR(50)"/>
            <column name="payment_description" type="VARCHAR(50)"/>
            <column name="department" type="VARCHAR(50)"/>
            <column name="account" type="VARCHAR(50)"/>
            <column name="amount" type="DECIMAL"/>
            <column name="posting_date" type="datetime"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-24" author="cameron">
        <createTable tableName="patient_procedures">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="cpt_code" type="VARCHAR(10)"/>
            <column name="modifier1" type="VARCHAR(10)"/>
            <column name="modifier2" type="VARCHAR(10)"/>
            <column name="modifier3" type="VARCHAR(10)"/>
            <column name="modifier4" type="VARCHAR(10)"/>
            <column name="cpt_description" type="VARCHAR(50)"/>
            <column name="caregiver" type="VARCHAR(50)"/>
            <column name="timestamp" type="datetime"/>
            <column defaultValueNumeric="1" name="active" type="INT"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column name="admit_date" type="datetime"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-25" author="cameron">
        <createTable tableName="patient_reference_lab_orders">
            <column name="empi" type="INT"/>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="accession" type="VARCHAR(20)"/>
            <column name="result_type" type="CHAR(1)"/>
            <column name="order_code" type="VARCHAR(20)"/>
            <column name="order_code_description" type="VARCHAR(80)"/>
            <column name="event_date" type="datetime"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-26" author="cameron">
        <createTable tableName="patient_reference_lab_results">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="empi" type="INT"/>
            <column name="system_id" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="sequence" type="VARCHAR(20)"/>
            <column name="result_type" type="CHAR(1)"/>
            <column name="order_code" type="VARCHAR(20)"/>
            <column name="order_code_description" type="VARCHAR(80)"/>
            <column name="test_code" type="VARCHAR(20)"/>
            <column name="test_description" type="VARCHAR(40)"/>
            <column name="test_abbreviation" type="VARCHAR(20)"/>
            <column name="result_line" type="VARCHAR(80)"/>
            <column name="reference_range" type="VARCHAR(40)"/>
            <column name="abnormal_flag" type="VARCHAR(1)"/>
            <column name="test_unit" type="VARCHAR(20)"/>
            <column name="event_date" type="datetime"/>
            <column name="release_date" type="datetime"/>
            <column name="performed_by" type="VARCHAR(45)"/>
            <column name="entered_date" type="datetime"/>
            <column name="entered_by" type="VARCHAR(45)"/>
            <column name="test_status" type="VARCHAR(1)"/>
            <column name="corrected_result" type="VARCHAR(80)"/>
            <column name="abnormal_code" type="CHAR(2)"/>
            <column name="test_result_flag" type="CHAR(2)"/>
            <column name="normal_lookup_flag" type="VARCHAR(20)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-27" author="cameron">
        <createTable tableName="patient_telephone">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="CHAR(20)"/>
            <column name="account_number" type="VARCHAR(20)"/>
            <column name="type" type="VARCHAR(20)"/>
            <column name="Telephone" type="VARCHAR(17)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-28" author="cameron">
        <createTable tableName="patient_transcription_orders">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(24)"/>
            <column name="account_number" type="VARCHAR(24)"/>
            <column name="order_code" type="VARCHAR(24)"/>
            <column name="order_description" type="CHAR(80)"/>
            <column name="status" type="VARCHAR(24)"/>
            <column name="enter_datetime" type="datetime"/>
            <column name="system_name" type="VARCHAR(24)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-29" author="cameron">
        <createTable tableName="patient_transcription_results">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(24)"/>
            <column name="account_number" type="VARCHAR(24)"/>
            <column name="order_code" type="VARCHAR(24)"/>
            <column name="order_description" type="CHAR(80)"/>
            <column name="reading_caregiver" type="VARCHAR(41)"/>
            <column name="signing_caregiver" type="VARCHAR(41)"/>
            <column name="status" type="VARCHAR(24)"/>
            <column name="enter_datetime" type="datetime"/>
            <column name="read_datetime" type="datetime"/>
            <column name="sign_datetime" type="datetime"/>
            <column name="cancel_reason" type="VARCHAR(255)"/>
            <column name="transcription_text" type="VARCHAR(**********)"/>
            <column name="system_name" type="VARCHAR(24)"/>
            <column name="security_level" type="BOOLEAN"/>
            <column name="admit_date" type="datetime"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-30" author="cameron">
        <createTable tableName="patient_visits">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="system_id" type="INT"/>
            <column name="empi" type="INT"/>
            <column name="mrn" type="VARCHAR(50)"/>
            <column name="account_number" type="VARCHAR(50)"/>
            <column name="admit_date" type="datetime"/>
            <column name="discharge_date" type="datetime"/>
            <column name="visit_status" type="VARCHAR(50)"/>
            <column name="patient_category" type="VARCHAR(50)"/>
            <column name="admit_source" type="VARCHAR(50)"/>
            <column name="admit_type" type="VARCHAR(50)"/>
            <column name="admit_service" type="VARCHAR(50)"/>
            <column name="adm_diag" type="VARCHAR(80)"/>
            <column name="discharge_service" type="VARCHAR(50)"/>
            <column name="discharge_status" type="VARCHAR(50)"/>
            <column name="financial_class" type="VARCHAR(50)"/>
            <column name="patient_type" type="VARCHAR(50)"/>
            <column name="home_telephone" type="VARCHAR(12)"/>
            <column name="mobile_telephone" type="VARCHAR(12)"/>
            <column name="visit_type" type="VARCHAR(50)"/>
            <column name="reason" type="VARCHAR(255)"/>
            <column name="notes" type="VARCHAR(255)"/>
            <column name="visit_number" type="INT"/>
            <column name="provider" type="VARCHAR(50)"/>
            <column name="system_name" type="VARCHAR(50)"/>
            <column defaultValueComputed="0" name="security_level" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet id="*************-31" author="cameron">
        <createIndex indexName="IDX_patient_diagnosis_join" tableName="patient_diagnosis">
            <column defaultValueNumeric="0" name="system_id"/>
            <column name="mrn"/>
            <column name="account_number"/>
        </createIndex>
    </changeSet>
    <changeSet id="*************-32" author="cameron">
        <createIndex indexName="NonClusteredIndex-********-100020" tableName="patient_procedures">
            <column defaultValueNumeric="0" name="system_id"/>
            <column name="mrn"/>
            <column name="account_number"/>
        </createIndex>
    </changeSet>
    <changeSet id="*************-33" author="cameron">
        <createIndex indexName="NonClusteredIndex-********-115506" tableName="patient_visits">
            <column name="empi"/>
        </createIndex>
    </changeSet>
    <changeSet id="*************-34" author="cameron">
        <createIndex indexName="NonClusteredIndex-********-115601" tableName="patient_procedures">
            <column name="empi"/>
        </createIndex>
    </changeSet>
    <changeSet id="*************-35" author="cameron">
        <createIndex indexName="NonClusteredIndex-********-081010" tableName="patient_lab_results">
            <column name="accession"/>
        </createIndex>
    </changeSet>
    <changeSet id="*************-36" author="cameron">
        <createIndex indexName="NonClusteredIndex-********-081142" tableName="patient_lab_results">
            <column name="empi"/>
        </createIndex>
    </changeSet>
    <changeSet id="*************-37" author="cameron">
        <createIndex indexName="empi" tableName="patient_allergies">
            <column name="empi"/>
        </createIndex>
    </changeSet>
    <changeSet id="*************-38" author="cameron">
        <createIndex indexName="system_id_account" tableName="patient_allergies">
            <column name="system_id"/>
            <column name="account_number"/>
        </createIndex>
    </changeSet>
    <changeSet id="*************-39" author="cameron">
        <createIndex indexName="system_id_mrn" tableName="patient_allergies">
            <column name="system_id"/>
            <column name="mrn"/>
        </createIndex>
    </changeSet>
    <changeSet id="*************-40" author="cameron">
        <createView fullDefinition="false" viewName="vpatient_documents">select *
                                                                         from patient_documents
                                                                         where progress_note = 0
                                                                             go</createView>
    </changeSet>
    <changeSet id="*************-41" author="cameron">
        <createView fullDefinition="false" viewName="vpatient_progress_notes">select *
                                                                              from patient_documents
                                                                              where progress_note = 1
                                                                                  go</createView>
    </changeSet>

</databaseChangeLog>