package com.cha.model.auth

import io.micronaut.core.annotation.Introspected
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank

@Introspected
@Serdeable
@Schema(name = "RespondToChallengeRequest", description = "Response to authentication challenge")
data class RespondToChallengeRequest(
    @field:NotBlank
    @Schema(description = "Username of the user", example = "johndoe")
    val username: String,
    @field:NotBlank
    @Schema(description = "Challenge name", example = "NEW_PASSWORD_REQUIRED")
    val challengeName: String,
    @field:NotBlank
    @Schema(description = "Session token from challenge", example = "session-token")
    val session: String,
    @Schema(description = "Challenge responses", example = "{\"NEW_PASSWORD\": \"NewPassword123!\"}")
    val challengeResponses: Map<String, String>
)