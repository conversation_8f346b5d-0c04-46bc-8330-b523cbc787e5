package com.cha.model.dto

import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

/** Request model for generating a report. */
@Serdeable
@Schema(
    description = """
       Request model for generating a patient report. Contains patient identifiers, report type, date range, sections to include, output format, and optional title. 
    """,
    requiredProperties = ["empi", "mrn", "reportType"]
)
data class ReportRequest(
    @field:Schema(
        description = "The Enterprise Master Patient Index (EMPI) of the patient.",
        example = "12345",
        required = true
    )
    val empi: Int,

    @field:Schema(
        description = "The Medical Record Number (MRN) of the patient.",
        example = "MRN789012",
        required = true
    )
    val mrn: String,

    @field:Schema(
        description = "The Visit ID, required when reportType is VISIT_SUMMARY. Null otherwise.",
        example = "VISIT5678",
        nullable = true,
        accessMode = Schema.AccessMode.READ_WRITE,
        defaultValue = "null"
    )
    val visitId: String? = null,

    val patientName: String? = null,

    @field:Schema(
        description = "The type of report to generate. Determines the scope and content structure.",
        required = true,
        implementation = ReportType::class
    )
    val reportType: ReportType,

    @field:Schema(
        description = "Optional date range for filtering report content.",
        nullable = true,
        implementation = DateRangeFilter::class
    )
    val dateRange: DateRangeFilter? = null,

    @field:Schema(
        description = "Set of clinical sections to include in the report. If empty, defaults apply by report type.",
        example = "[\"MEDICATIONS\", \"ALLERGIES\"]",
//        implementation = ReportSections::class
    )
    val includeSections: Set<ReportSections> = emptySet(),

    @field:Schema(
        description = "The desired output format for the report.",
        defaultValue = "PDF",
        nullable = true,
        required = false,
        implementation = ReportOutputFormat::class
    )
    val outputFormat: ReportOutputFormat = ReportOutputFormat.PDF,

    @field:Schema(
        description = "Optional custom title for the generated report file. Used as the document name.",
        nullable = true,
        example = "John Doe - Visit Summary 2023-10-26"
    )
    val title: String? = null,
)

/** Enum specifying the type of report to generate. */
@Serdeable
@Schema(
    name = "ReportType",
    description = "Types of reports available for generation."
)
enum class ReportType {
    @Schema(description = "A summary of a specific patient visit, typically includes encounter details.")
    VISIT_SUMMARY,

    @Schema(
        description = """
       A general report based on MRN, not tied to a specific visit. May contain cumulative information. 
    """
    )
    MRN,

    @Schema(description = "Release of Information report, for legal, compliance, or third-party requests.")
    ROI
}

/** Enum specifying clinical sections that may be included in a report. */
@Serdeable
@Schema(
    description = "Enumerates all possible clinical and administrative sections available for inclusion in a report."
)
enum class ReportSections {
    // CLINICAL
    @Schema(description = "Patient's allergies and adverse reactions.")
    ALLERGIES,

    @Schema(description = "Patient's immunization records.")
    IMMUNIZATIONS,

    @Schema(
        description = """
        Laboratory results, Reference laboratory results, Microbiology details, comments, and susceptibilities.
    """
    )
    LABORATORY,

    @Schema(description = "Patient's active and historical medications.")
    MEDICATIONS,

    @Schema(description = "Medications the patient reports taking at home.")
    HOME_MEDICATIONS,

    @Schema(description = "Procedures performed on the patient.")
    PROCEDURES,

    @Schema(description = "Orders placed during the patient's care.")
    ORDERS,

    @Schema(description = "Clinical notes and summaries from medical encounters. Includes alerts.")
    NOTES,

    @Schema(description = "Diagnostic codes and descriptions for the patient.")
    DIAGNOSES,

    @Schema(description = "Transcription results.")
    TRANSCRIPTIONS,

    // FINANCIAL
    @Schema(description = "Insurance coverage and payer information for the patient.")
    INSURANCE,

    @Schema(description = "Billing statements related to the patient's care.")
    BILLS,

    @Schema(description = "Provider notes or comments on invoices.")
    INVOICE_NOTES,

    @Schema(description = "Invoices generated for patient care services.")
    INVOICES,

    // DOCUMENTS
    @Schema(description = "Documents that are associated with a visit.")
    DOCUMENTS,

    @Schema(description = "Unassigned documents; only available for MRN and ROI reports.")
    UNASSIGNED_DOCUMENTS
}

/** A date range filter for report queries. */
@Serdeable
@Schema(
    description = "Date range used to filter report contents. Either or both startDate and endDate may be null."
)
data class DateRangeFilter(
    @field:Schema(
        description = "Start date (inclusive) for report content. Null means no lower bound.",
        format = "date",
        example = "2023-01-01",
        nullable = true
    )
    val startDate: LocalDate?,

    @field:Schema(
        description = "End date (inclusive) for report content. Null means no upper bound.",
        format = "date",
        example = "2023-12-31",
        nullable = true
    )
    val endDate: LocalDate?,
)

/** Enum specifying available report output formats. */
@Serdeable
@Schema(
    description = "Output format for generated report documents."
)
enum class ReportOutputFormat {
    @Schema(description = "Portable Document Format (PDF), the standard output format for most reports.")
    PDF,

    @Schema(
        description = """
       Comma-Separated Values (CSV), suitable for data analysis and spreadsheets. CSV support may be limited. 
    """
    )
    CSV // TODO: support CSV export in future
    // Extend with XML or FHIR if supported in the future.
}
