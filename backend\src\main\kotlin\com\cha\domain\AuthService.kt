package com.cha.domain

import aws.sdk.kotlin.services.cognitoidentityprovider.CognitoIdentityProviderClient
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AdminAddUserToGroupRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AdminCreateUserRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AdminDeleteUserRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AdminGetUserRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AdminInitiateAuthRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AdminInitiateAuthResponse
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AdminResetUserPasswordRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AdminRespondToAuthChallengeRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AdminSetUserPasswordRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AdminUpdateUserAttributesRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AttributeType
import aws.sdk.kotlin.services.cognitoidentityprovider.model.AuthFlowType
import aws.sdk.kotlin.services.cognitoidentityprovider.model.ChallengeNameType
import aws.sdk.kotlin.services.cognitoidentityprovider.model.ChangePasswordRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.CognitoIdentityProviderException
import aws.sdk.kotlin.services.cognitoidentityprovider.model.GetTokensFromRefreshTokenRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.GlobalSignOutRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.ListUsersRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.ListUsersResponse
import aws.sdk.kotlin.services.cognitoidentityprovider.model.NotAuthorizedException
import aws.sdk.kotlin.services.cognitoidentityprovider.model.ResendConfirmationCodeRequest
import aws.sdk.kotlin.services.cognitoidentityprovider.model.UserNotFoundException
import aws.sdk.kotlin.services.cognitoidentityprovider.model.UsernameExistsException
import com.cha.domain.exception.AuthenticationException
import com.cha.domain.exception.AuthorizationException
import com.cha.domain.exception.InternalServerException
import com.cha.domain.exception.InvalidRequestException
import com.cha.domain.exception.ResourceNotFoundException
import com.cha.model.auth.CreateUserRequest
import com.cha.model.auth.CreateUserResponse
import com.cha.model.auth.ForcePasswordResetRequest
import com.cha.model.auth.LoginRequest
import com.cha.model.auth.LoginResponse
import com.cha.model.auth.NewPasswordRequest
import com.cha.model.auth.ResendVerificationRequest
import com.cha.model.auth.RespondToChallengeRequest
import com.cha.model.auth.UpdateUserRequest
import com.cha.model.auth.UpdateUserResponse
import com.cha.model.auth.User
import com.cha.security.CognitoClaimsValidator
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.context.annotation.Value
import io.micronaut.data.model.CursoredPage
import io.micronaut.data.model.CursoredPageable
import io.micronaut.data.model.Pageable
import jakarta.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.Instant
import kotlin.jvm.optionals.getOrNull
import com.cha.model.auth.ChangePasswordRequest as UserChangePasswordRequest

private val logger = KotlinLogging.logger { }

@Singleton
class AuthService(
    private val cognitoClient: CognitoIdentityProviderClient,
    private val cognitoClaimsValidator: CognitoClaimsValidator,
) {

    @Value("\${aws.cognito.userPoolId}")
    lateinit var userPoolId: String

    @Value("\${aws.cognito.clientId}")
    lateinit var clientId: String

    /** Authenticates a user with Cognito using the provided credentials. */
    suspend fun login(loginRequest: LoginRequest): LoginResponse = withContext(Dispatchers.IO) {
        val authParams = mapOf(
            "USERNAME" to loginRequest.username,
            "PASSWORD" to loginRequest.password,
        )

        val authRequest = AdminInitiateAuthRequest {
            clientId = <EMAIL>
            userPoolId = <EMAIL>
            authParameters = authParams
            authFlow = AuthFlowType.AdminUserPasswordAuth
        }

        try {
            logger.info { "Attempting AdminInitiateAuth for user: ${loginRequest.username}" }
            val response = cognitoClient.adminInitiateAuth(authRequest)

            // Process the response directly instead of delegating to handleCognitoResponse
            handleCognitoResponse(response)
        } catch (e: NotAuthorizedException) {
            logger.warn { "Authentication failed for user ${loginRequest.username}: ${e.message}" }
            throw AuthenticationException(
                message = "Invalid username or password",
                cause = e
            )
        } catch (e: UserNotFoundException) {
            logger.warn { "Authentication failed, user not found: ${loginRequest.username}" }
            // Return generic unauthorized to avoid user enumeration
            throw AuthenticationException(
                message = "Invalid username or password",
                cause = e
            )
        } catch (e: CognitoIdentityProviderException) {
            logger.error { "Cognito Error during authentication for user ${loginRequest.username}: ${e.message}" }
            throw InternalServerException(
                message = "An error occurred during authentication",
                cause = e
            )
        } catch (e: Exception) {
            logger.error { "Unexpected error during authentication for user ${loginRequest.username}: ${e.message}" }
            throw InternalServerException(
                message = "An unexpected error occurred",
                cause = e
            )
        }
    }

    /** Handles the response from a Cognito authentication attempt. */
    private fun handleCognitoResponse(response: AdminInitiateAuthResponse): LoginResponse {
        val authResult = response.authenticationResult
        if (authResult != null) {
            logger.info { "Successfully authenticated user, received tokens." }

            // Check for required tokens in authResult
            val accessToken = authResult.accessToken
            if (accessToken.isNullOrBlank()) {
                logger.error { "Authentication successful but access token is missing" }
                throw InternalServerException(
                    message = "Access token is missing from authentication result"
                )
            }

            val idToken = authResult.idToken
            if (idToken.isNullOrBlank()) {
                logger.error { "Authentication successful but ID token is missing" }
                throw InternalServerException(
                    message = "ID token is missing from authentication result"
                )
            }

            // Extract user info from the ID token using the validator
            val userInfo = cognitoClaimsValidator.extractUserInfoFromIdToken(idToken)

            return LoginResponse(
                accessToken = accessToken,
                refreshToken = authResult.refreshToken, // May be null, which is acceptable
                expiresIn = authResult.expiresIn,
                user = userInfo
            )
        } else if (response.challengeName != null) {
            // Authentication requires a challenge response
            logger.warn { "Authentication requires challenge: ${response.challengeName}" }
            throw AuthenticationException(
                message = "Authentication requires further action: ${response.challengeName}",
                challengeName = response.challengeName.toString(),
                session = response.session
            )
        } else {
            // Unexpected response state
            logger.error { "Unexpected Cognito response state: No auth result or challenge." }
            throw InternalServerException(
                message = "Unexpected authentication response"
            )
        }
    }

    /** Logs out a user by invalidating their token with AWS Cognito. */
    suspend fun logout(username: String, accessToken: String): Boolean =
        withContext(Dispatchers.IO) {
            try {
                logger.info { "Attempting global sign out for user: $username" }

                // Create the GlobalSignOut request with the user's access token
                val signOutRequest = GlobalSignOutRequest {
                    this.accessToken = accessToken
                }

                // Execute the sign out
                cognitoClient.globalSignOut(signOutRequest)

                logger.info { "Successfully signed out user: $username" }
                return@withContext true
            } catch (e: NotAuthorizedException) {
                logger.warn { "Logout failed: Invalid or expired token for user $username: ${e.message}" }
                throw AuthenticationException(
                    message = "Invalid or expired token",
                    cause = e
                )
            } catch (e: CognitoIdentityProviderException) {
                logger.error { "Cognito error during logout for user $username: ${e.message}" }
                throw InternalServerException(
                    message = "An error occurred during logout",
                    cause = e
                )
            } catch (e: Exception) {
                logger.error { "Unexpected error during logout for user $username: ${e.message}" }
                throw InternalServerException(
                    message = "An unexpected error occurred",
                    cause = e
                )
            }
        }

    /** Refreshes the access token and ID token using a refresh token. */
    suspend fun refreshToken(refreshToken: String): LoginResponse = withContext(Dispatchers.IO) {
        if (refreshToken.isBlank()) {
            logger.warn { "Refresh token attempt with empty refresh token" }
            throw InvalidRequestException(
                message = "Refresh token is required"
            )
        }

        val authRequest = GetTokensFromRefreshTokenRequest {
            this.clientId = <EMAIL>
            this.refreshToken = refreshToken
        }

        try {
            logger.info { "Attempting to refresh tokens" }
            val response = cognitoClient.getTokensFromRefreshToken(authRequest)

            val authResult = response.authenticationResult
            if (authResult != null) {
                logger.info { "Successfully refreshed tokens" }

                // Check for required tokens in authResult
                val accessToken = authResult.accessToken
                if (accessToken.isNullOrBlank()) {
                    logger.error { "Token refresh successful but access token is missing" }
                    throw InternalServerException(
                        message = "Access token is missing from refresh result"
                    )
                }

                val idToken = authResult.idToken
                if (idToken.isNullOrBlank()) {
                    logger.error { "Token refresh successful but ID token is missing" }
                    throw InternalServerException(
                        message = "ID token is missing from refresh result"
                    )
                }

                // Extract user info from the ID token
                val userInfo = cognitoClaimsValidator.extractUserInfoFromIdToken(idToken)

                return@withContext LoginResponse(
                    accessToken = accessToken,
                    refreshToken = refreshToken, // Return the same refresh token as it doesn't change
                    expiresIn = authResult.expiresIn,
                    user = userInfo
                )
            } else {
                logger.error { "Unexpected response from Cognito during token refresh: No auth result" }
                throw InternalServerException(
                    message = "Failed to refresh tokens"
                )
            }
        } catch (e: NotAuthorizedException) {
            logger.warn { "Token refresh failed: Invalid or expired refresh token: ${e.message}" }
            throw AuthenticationException(
                message = "Invalid or expired refresh token",
                cause = e
            )
        } catch (e: CognitoIdentityProviderException) {
            logger.error { "Cognito error during token refresh: ${e.message}" }
            throw InternalServerException(
                message = "An error occurred during token refresh",
                cause = e
            )
        } catch (e: Exception) {
            logger.error { "Unexpected error during token refresh: ${e.message}" }
            throw InternalServerException(
                message = "An unexpected error occurred",
                cause = e
            )
        }
    }

    /**
     * Creates a new user in the Cognito User Pool and adds them to the
     * specified groups.
     *
     * @param createUserRequest The request containing user details
     * @return HttpResponse containing the created user information or error
     *    details
     */
    suspend fun createUser(createUserRequest: CreateUserRequest): CreateUserResponse =
        withContext(Dispatchers.IO) {
            logger.info { "Attempting to create new user with username: ${createUserRequest.username}" }

            // Prepare user attributes
            val userAttributes = mutableListOf(
                AttributeType {
                    name = "email"
                    value = createUserRequest.email
                },
                AttributeType {
                    name = "email_verified"
                    value =
                        "true" // Auto-verify email for simplicity, could be changed to false if verification flow is needed
                },
                AttributeType {
                    name = "given_name"
                    value = createUserRequest.firstName
                },
                AttributeType {
                    name = "family_name"
                    value = createUserRequest.lastName
                }
            )

            if (createUserRequest.pictureUrl.isNotBlank()) {
                userAttributes.add(
                    AttributeType {
                        name = "picture"
                        value = createUserRequest.pictureUrl
                    }
                )
            }
            userAttributes.add(
                AttributeType {
                    name = "updated_at"
                    value = Instant.now().epochSecond.toString()
                }
            )

            val createRequest = AdminCreateUserRequest {
                userPoolId = <EMAIL>
                username = createUserRequest.username
                temporaryPassword = createUserRequest.temporaryPassword
                this.userAttributes = userAttributes
                // desiredDeliveryMediums = listOf(DeliveryMediumType.Email) // Send welcome email
                // messageAction = MessageActionType.Suppress // Don't send temporary password via email for security
            }

            var userId: String? = null

            try {
                // Step 1: Create the user
                logger.info { "Creating user: ${createUserRequest.username}" }
                val response = cognitoClient.adminCreateUser(createRequest)

                val user = response.user
                if (user == null || user.username.isNullOrBlank()) {
                    logger.error { "User creation succeeded but Cognito returned null user or username" }
                    throw InternalServerException(message = "User creation succeeded but with unexpected response")
                }

                userId = user.username
                logger.info { "Successfully created user: ${createUserRequest.username} with ID: $userId" }

                // Step 2: Add user to groups
                if (createUserRequest.roles.isNotEmpty()) {
                    try {
                        for (groupName in createUserRequest.roles) {
                            logger.info { "Adding user $userId to group: $groupName" }
                            val addToGroupRequest = AdminAddUserToGroupRequest {
                                userPoolId = <EMAIL>
                                username = userId
                                this.groupName = groupName
                            }
                            cognitoClient.adminAddUserToGroup(addToGroupRequest)
                            logger.info { "Successfully added user $userId to group: $groupName" }
                        }
                    } catch (e: Exception) {
                        // Failed to add user to group - rollback by deleting the user
                        logger.error {
                            "Failed to add user $userId to groups: ${e.message}. " +
                                    "Rolling back user creation."
                        }

                        try {
                            val deleteRequest = AdminDeleteUserRequest {
                                userPoolId = <EMAIL>
                                username = userId
                            }
                            cognitoClient.adminDeleteUser(deleteRequest)
                            logger.info { "Rolled back user creation for $userId due to group assignment failure" }
                        } catch (deleteEx: Exception) {
                            logger.error { "Failed to roll back user creation for $userId: ${deleteEx.message}" }
                        }

                        InternalServerException(
                            message = "Failed to assign user to groups: ${e.message}. User creation was rolled back."
                        )
                    }
                }

                // If we got here, both user creation and group assignment succeeded
                val userResponse = CreateUserResponse(
                    userId = userId!!,
                    username = createUserRequest.username,
                    email = createUserRequest.email,
                    roles = createUserRequest.roles
                )

                return@withContext userResponse
            } catch (e: UsernameExistsException) {
                logger.warn { "Failed to create user: Username ${createUserRequest.username} already exists" }
                throw InvalidRequestException(
                    message = "Username already exists",
                    cause = e
                )
            } catch (e: CognitoIdentityProviderException) {
                logger.error { "Cognito error during user creation for ${createUserRequest.username}: ${e.message}" }
                throw InternalServerException(
                    message = "An error occurred during user creation",
                    cause = e
                )
            } catch (e: Exception) {
                logger.error { "Unexpected error during user creation for ${createUserRequest.username}: ${e.message}" }

                // If we already created the user but hit another exception, try to clean up
                if (userId != null) {
                    try {
                        val deleteRequest = AdminDeleteUserRequest {
                            userPoolId = <EMAIL>
                            username = userId
                        }
                        cognitoClient.adminDeleteUser(deleteRequest)
                        logger.info { "Rolled back user creation for $userId due to unexpected error" }
                    } catch (deleteEx: Exception) {
                        logger.error { "Failed to roll back user creation for $userId: ${deleteEx.message}" }
                    }
                }

                throw InternalServerException(
                    message = "An unexpected error occurred during user creation",
                )
            }
        }

    /**
     * Deletes a user from the Cognito User Pool.
     *
     * @param username The username of the user to delete
     */
    suspend fun deleteUser(username: String): Boolean = withContext(Dispatchers.IO) {
        logger.info { "Attempting to delete user with username: $username" }

        try {
            val deleteRequest = AdminDeleteUserRequest {
                userPoolId = <EMAIL>
                this.username = username
            }

            cognitoClient.adminDeleteUser(deleteRequest)
            logger.info { "Successfully deleted user: $username" }

            return@withContext true
        } catch (e: UserNotFoundException) {
            logger.warn { "Delete user failed: User not found: ${username}" }
            throw ResourceNotFoundException(
                resourceType = "User",
                identifier = username,
                cause = e
            )
        } catch (e: CognitoIdentityProviderException) {
            logger.error { "Cognito error during user deletion for ${username}: ${e.message}" }
            throw InternalServerException(
                message = "An error occurred during user deletion",
                cause = e
            )
        } catch (e: Exception) {
            logger.error { "Unexpected error during user deletion for ${username}: ${e.message}" }
            throw InternalServerException(
                message = "An unexpected error occurred during user deletion",
                cause = e
            )
        }
    }

    /**
     * Retrieves a paginated list of users from the Cognito User Pool.
     *
     * @param pageable Optional pagination parameters with cursor support. If
     *    null, default pagination will be applied.
     */
    suspend fun getUsers(pageable: CursoredPageable): CursoredPage<User> = withContext(Dispatchers.IO) {

        logger.info { "Attempting to retrieve users from user pool: $userPoolId with cursored pagination (size: ${pageable.size})" }

        try {
            val listUsersRequest = ListUsersRequest {
                userPoolId = <EMAIL>
                pageable.size.let { this.limit = it }
                pageable.cursor()?.getOrNull()?.elements()?.last()
                    ?.let { this.paginationToken = it.toString() } // Set pagination token if present
            }

            val response: ListUsersResponse = cognitoClient.listUsers(listUsersRequest)
            val users = (response.users?.map { user ->
                // Extract attributes
                val attributes = user.attributes ?: emptyList()
                val id = attributes.find { it.name == "sub" }?.value ?: ""
                val email = attributes.find { it.name == "email" }?.value ?: ""
                val emailVerified = attributes.find { it.name == "email_verified" }?.value?.toBoolean() ?: false
                val firstName = attributes.find { it.name == "given_name" }?.value ?: ""
                val lastName = attributes.find { it.name == "family_name" }?.value ?: ""
                val pictureUrl = attributes.find { it.name == "picture" }?.value ?: ""
                val updatedAtStr = attributes.find { it.name == "updated_at" }?.value
                val createdAtStr = attributes.find { it.name == "created_at" }?.value

                // Convert updated_at to Instant
                val updatedAt = if (updatedAtStr != null) {
                    try {
                        Instant.ofEpochSecond(updatedAtStr.toLong())
                    } catch (e: NumberFormatException) {
                        Instant.now()
                    }
                } else {
                    Instant.now()
                }

                // Convert created_at to Instant, fall back to current time for now
                // TODO: Properly handle AWS Instant conversion when available
                val createdAt = if (createdAtStr != null) {
                    try {
                        Instant.ofEpochSecond(createdAtStr.toLong())
                    } catch (e: NumberFormatException) {
                        Instant.now()
                    }
                } else {
                    Instant.now()
                }

                // TODO: Fetch user groups/roles from Cognito for complete user information
                val roles = listOf<String>()

                User(
                    id = id,
                    username = user.username.toString(),
                    email = email,
                    emailVerified = emailVerified,
                    firstName = firstName,
                    lastName = lastName,
                    pictureUrl = pictureUrl,
                    updatedAt = updatedAt,
                    createdAt = createdAt,
                    roles = roles
                )
            } ?: emptyList()).ifEmpty { emptyList() }

            // Create cursors for each user
            // For CursoredPage, we need to create a cursor for each content item
            val cursors = users.map {
                // Use a placeholder cursor for each user, since they don't have individual cursors
                Pageable.Cursor.of("user-${it.id}")
            }

            // For actual pagination, we'll use the pagination token from the response
            // Store the pagination token as a Long value for the next cursor
            val nextCursor: Long? = if (response.paginationToken != null) {
                // Store the pagination token in the last cursor's offset
                // This will be used in subsequent requests
                response.paginationToken.hashCode().toLong()
            } else {
                null
            }

            // Create a CursoredPage with explicit total size to avoid serialization issues
            // The total size is set to users.size because we don't have a way to get the total count from Cognito
            // If response.paginationToken is not null, we know there are more users, so we set a larger number
            val totalSize = if (response.paginationToken != null) {
                // When we have a pagination token, there are more users than just this page
                // Setting a value larger than the current page size to indicate more pages
                users.size.toLong() + 1
            } else {
                // When there's no pagination token, we're on the last page
                // The total size is exactly the number of users we have
                users.size.toLong()
            }
            val userPage = CursoredPage.of(users, pageable, cursors, totalSize)

            logger.info { "Successfully retrieved ${users.size} users. Has next page: ${response.paginationToken != null}" }
            return@withContext userPage
        } catch (e: CognitoIdentityProviderException) {
            logger.error { "Cognito error during getUsers: ${e.message}" }
            throw InternalServerException(
                message = "An error occurred while retrieving users",
                cause = e
            )
        } catch (e: Exception) {
            logger.error { "Unexpected error during getUsers: ${e.message}" }
            throw InternalServerException(
                message = "An unexpected error occurred",
                cause = e
            )
        }
    }

    /**
     * Retrieves a single user from the Cognito User Pool.
     *
     * @param username The username of the user to retrieve
     * @return User details
     * @throws ResourceNotFoundException if the user is not found
     * @throws InternalServerException if an unexpected error occurs
     */
    suspend fun getUser(username: String): User = withContext(Dispatchers.IO) {
        logger.info { "Attempting to retrieve user: $username from user pool: $userPoolId" }

        try {
            val getUserRequest = AdminGetUserRequest {
                userPoolId = <EMAIL>
                this.username = username
            }

            val response = cognitoClient.adminGetUser(getUserRequest)

            // Extract attributes
            val attributes = response.userAttributes ?: emptyList()
            val email = attributes.find { it.name == "email" }?.value ?: ""
            val emailVerified = attributes.find { it.name == "email_verified" }?.value?.toBoolean() ?: false
            val firstName = attributes.find { it.name == "given_name" }?.value ?: ""
            val lastName = attributes.find { it.name == "family_name" }?.value ?: ""
            val pictureUrl = attributes.find { it.name == "picture" }?.value ?: ""
            val updatedAtStr = attributes.find { it.name == "updated_at" }?.value

            // Convert updated_at to Instant
            val updatedAt = if (updatedAtStr != null) {
                try {
                    Instant.ofEpochSecond(updatedAtStr.toLong())
                } catch (e: NumberFormatException) {
                    Instant.now()
                }
            } else {
                Instant.now()
            }

            // Use user creation date from Cognito response - for now use current time
            // TODO: Properly convert AWS Instant to Java Instant
            val createdAt = Instant.now()

            // TODO: get user groups/roles from Cognito
            val roles = listOf<String>()

            val user = User(
                id = response.username,
                username = response.username,
                email = email,
                emailVerified = emailVerified,
                firstName = firstName,
                lastName = lastName,
                pictureUrl = pictureUrl,
                updatedAt = updatedAt,
                createdAt = createdAt,
                roles = roles
            )

            logger.info { "Successfully retrieved user: $username" }
            return@withContext user
        } catch (e: UserNotFoundException) {
            logger.warn { "Get user failed: User not found: $username" }
            throw ResourceNotFoundException(
                resourceType = "User",
                identifier = username,
                message = "User with username '$username' not found",
                cause = e
            )
        } catch (e: CognitoIdentityProviderException) {
            logger.error { "Cognito error during getUser for $username: ${e.message}" }
            throw InternalServerException(
                message = "An error occurred while retrieving user information",
                cause = e
            )
        } catch (e: Exception) {
            logger.error { "Unexpected error during getUser for $username: ${e.message}" }
            throw InternalServerException(
                message = "An unexpected error occurred",
                cause = e
            )
        }
    }

    /**
     * Updates a user's attributes in the Cognito User Pool.
     *
     * @param username The username of the user to update
     * @param updateUserRequest The request containing updated user details
     */
    suspend fun updateUser(username: String, updateUserRequest: UpdateUserRequest): UpdateUserResponse =
        withContext(Dispatchers.IO) {
            logger.info { "Attempting to update user: $username" }

            try {
                val userAttributes = mutableListOf<AttributeType>()

                // Add attributes that are being updated
                updateUserRequest.email?.let {
                    userAttributes.add(AttributeType {
                        name = "email"
                        value = it
                    })
                }

                updateUserRequest.firstName?.let {
                    userAttributes.add(AttributeType {
                        name = "given_name"
                        value = it
                    })
                }

                updateUserRequest.lastName?.let {
                    userAttributes.add(AttributeType {
                        name = "family_name"
                        value = it
                    })
                }

                updateUserRequest.pictureUrl?.let {
                    userAttributes.add(AttributeType {
                        name = "picture"
                        value = it
                    })
                }

                // Always update the updated_at timestamp
                userAttributes.add(AttributeType {
                    name = "updated_at"
                    value = Instant.now().epochSecond.toString()
                })

                if (userAttributes.isNotEmpty()) {
                    val updateRequest = AdminUpdateUserAttributesRequest {
                        userPoolId = <EMAIL>
                        this.username = username
                        this.userAttributes = userAttributes
                    }

                    cognitoClient.adminUpdateUserAttributes(updateRequest)
                    logger.info { "Successfully updated user attributes for: $username" }
                }

                // TODO: Handle role updates by adding/removing user from groups

                val response = UpdateUserResponse(
                    username = username,
                    message = "User updated successfully"
                )

                return@withContext response
            } catch (e: UserNotFoundException) {
                logger.warn { "Update user failed: User not found: $username" }
                throw ResourceNotFoundException(
                    resourceType = "User",
                    identifier = username,
                    message = "User with username '$username' not found",
                    cause = e
                )
            } catch (e: CognitoIdentityProviderException) {
                logger.error { "Cognito error during updateUser for $username: ${e.message}" }
                throw InternalServerException(
                    message = "An error occurred while updating user information",
                    cause = e
                )
            } catch (e: Exception) {
                logger.error { "Unexpected error during updateUser for $username: ${e.message}" }
                throw InternalServerException(
                    message = "An unexpected error occurred",
                    cause = e
                )
            }
        }

    /**
     * Handles new password requests for AWS Cognito admin workflow.
     *
     * @param newPasswordRequest The request containing new password details
     */
    suspend fun handleNewPassword(newPasswordRequest: NewPasswordRequest): Boolean = withContext(Dispatchers.IO) {
        logger.info { "Attempting to set new password for user: ${newPasswordRequest.username}" }

        try {
            val setPasswordRequest = AdminSetUserPasswordRequest {
                userPoolId = <EMAIL>
                username = newPasswordRequest.username
                password = newPasswordRequest.password
                permanent = true // Set as permanent password
            }

            cognitoClient.adminSetUserPassword(setPasswordRequest)
            logger.info { "Successfully set new password for user: ${newPasswordRequest.username}" }

            return@withContext true
        } catch (e: UserNotFoundException) {
            logger.warn { "New password failed: User not found: ${newPasswordRequest.username}" }
            throw ResourceNotFoundException(
                resourceType = "User",
                identifier = newPasswordRequest.username,
                cause = e
            )
        } catch (e: CognitoIdentityProviderException) {
            logger.error { "Cognito error during handleNewPassword for ${newPasswordRequest.username}: ${e.message}" }
            throw InternalServerException(
                message = "An error occurred while setting the new password",
                cause = e
            )
        } catch (e: Exception) {
            logger.error {
                "Unexpected error during handleNewPassword " +
                        "for ${newPasswordRequest.username}: ${e.message}"
            }
            throw InternalServerException(
                message = "An unexpected error occurred",
                cause = e
            )
        }
    }

    /**
     * Forces a password reset for a user.
     *
     * This method uses AWS Cognito's AdminResetUserPassword which is secure
     * because:
     * - It doesn't set a temporary password
     * - It sends a password-reset code to the user's verified email/phone
     * - The user must complete the forgot-password flow with the code they
     *   receive
     * - No predictable passwords are involved
     *
     * @param forcePasswordResetRequest The request containing user details
     */
    suspend fun forcePasswordReset(forcePasswordResetRequest: ForcePasswordResetRequest): Boolean =
        withContext(Dispatchers.IO) {
            logger.info { "Attempting to force password reset for user: ${forcePasswordResetRequest.username}" }

            try {
                // Use AdminResetUserPassword which sends a secure password-reset code to the user
                val resetPasswordRequest = AdminResetUserPasswordRequest {
                    userPoolId = <EMAIL>
                    username = forcePasswordResetRequest.username
                    // Optional: Add client metadata for custom workflows
                    clientMetadata = mapOf(
                        "initiatedBy" to "admin",
                        "reason" to "password_reset_forced"
                    )
                }

                cognitoClient.adminResetUserPassword(resetPasswordRequest)
                logger.info { "Successfully initiated password reset for user: ${forcePasswordResetRequest.username}" }

                return@withContext true
            } catch (e: UserNotFoundException) {
                logger.warn { "Force password reset failed: User not found: ${forcePasswordResetRequest.username}" }
                throw ResourceNotFoundException(
                    resourceType = "User",
                    identifier = forcePasswordResetRequest.username,
                    cause = e
                )
            } catch (e: CognitoIdentityProviderException) {
                logger.error {
                    "Cognito error during forcePasswordReset " +
                            "for ${forcePasswordResetRequest.username}: ${e.message}"
                }
                throw InternalServerException(
                    message = "An error occurred while initiating the password reset",
                    cause = e
                )
            } catch (e: Exception) {
                logger.error {
                    "Unexpected error during forcePasswordReset " +
                            "for ${forcePasswordResetRequest.username}: ${e.message}"
                }
                throw InternalServerException(
                    message = "An unexpected error occurred",
                    cause = e
                )
            }
        }

    /**
     * Responds to authentication challenges.
     *
     * @param challengeRequest The request containing challenge response
     *    details
     * @return HttpResponse containing authentication result or error details
     */
    suspend fun respondToChallenge(challengeRequest: RespondToChallengeRequest): LoginResponse =
        withContext(Dispatchers.IO) {
            logger.info {
                "Attempting to respond to challenge ${challengeRequest.challengeName} " +
                        "for user: ${challengeRequest.username}"
            }

            try {
                val challengeResponseRequest = AdminRespondToAuthChallengeRequest {
                    userPoolId = <EMAIL>
                    clientId = <EMAIL>
                    challengeName = ChallengeNameType.fromValue(challengeRequest.challengeName)
                    session = challengeRequest.session
                    challengeResponses = challengeRequest.challengeResponses + ("USERNAME" to challengeRequest.username)
                }

                val response = cognitoClient.adminRespondToAuthChallenge(challengeResponseRequest)

                // Handle the response similar to login
                return@withContext handleCognitoResponse(AdminInitiateAuthResponse {
                    authenticationResult = response.authenticationResult
                    challengeName = response.challengeName
                    challengeParameters = response.challengeParameters
                    session = response.session
                })
            } catch (e: UserNotFoundException) {
                logger.warn { "Challenge response failed: User not found: ${challengeRequest.username}" }
                throw ResourceNotFoundException(
                    resourceType = "User",
                    identifier = challengeRequest.username,
                    cause = e
                )
            } catch (e: NotAuthorizedException) {
                logger.warn {
                    "Challenge response failed: Not authorized " +
                            "for user ${challengeRequest.username}: ${e.message}"
                }
                throw AuthorizationException(
                    message = "Not authorized for user ${challengeRequest.username}",
                    cause = e
                )
            } catch (e: CognitoIdentityProviderException) {
                logger.error {
                    "Cognito error during respondToChallenge " +
                            "for ${challengeRequest.username}: ${e.message}"
                }
                throw InternalServerException(
                    message = "An error occurred while responding to the challenge",
                    cause = e
                )
            } catch (e: Exception) {
                logger.error {
                    "Unexpected error during respondToChallenge " +
                            "for ${challengeRequest.username}: ${e.message}"
                }
                throw InternalServerException(
                    message = "An unexpected error occurred",
                    cause = e
                )
            }
        }

    /**
     * Changes the password for an authenticated user.
     *
     * @param username The username of the authenticated user
     * @param accessToken The user's access token
     * @param changePasswordRequest The request containing old and new
     *    passwords
     */
    suspend fun changePassword(
        username: String,
        accessToken: String,
        changePasswordRequest: UserChangePasswordRequest,
    ): Boolean = withContext(Dispatchers.IO) {
        logger.info { "Attempting to change password for user: $username" }

        try {
            val changePasswordRequestCognito = ChangePasswordRequest {
                this.accessToken = accessToken
                previousPassword = changePasswordRequest.currentPassword
                proposedPassword = changePasswordRequest.newPassword
            }

            cognitoClient.changePassword(changePasswordRequestCognito)
            logger.info { "Successfully changed password for user: $username" }
            return@withContext true
        } catch (e: NotAuthorizedException) {
            logger.warn { "Change password failed: Invalid current password for user $username: ${e.message}" }
            throw AuthorizationException(
                message = "Invalid current password",
                cause = e
            )
        } catch (e: CognitoIdentityProviderException) {
            logger.error { "Cognito error during changePassword for $username: ${e.message}" }
            throw InternalServerException(
                message = "An error occurred while changing password",
                cause = e
            )
        } catch (e: Exception) {
            logger.error { "Unexpected error during changePassword for $username: ${e.message}" }
            throw InternalServerException(
                message = "An unexpected error occurred",
                cause = e
            )
        }
    }

    /**
     * Resends email verification for a user.
     *
     * @param resendVerificationRequest The request containing user details
     */
    suspend fun resendEmailVerification(resendVerificationRequest: ResendVerificationRequest): Boolean =
        withContext(Dispatchers.IO) {
            logger.info { "Attempting to resend email verification for: ${resendVerificationRequest.email}" }

            try {
                // Find the user by email to get their username
                val listUsersRequest = ListUsersRequest {
                    userPoolId = <EMAIL>
                    filter = "email = \"${resendVerificationRequest.email}\""
                    limit = 1
                }

                val listResponse = cognitoClient.listUsers(listUsersRequest)
                val user = listResponse.users?.firstOrNull()

                if (user?.username == null) {
                    logger.warn {
                        "Resend verification failed: User not found with " +
                                "email: ${resendVerificationRequest.email}"
                    }
                    throw ResourceNotFoundException(
                        resourceType = "User",
                        identifier = "User"
                    )
                }

                val resendRequest = ResendConfirmationCodeRequest {
                    clientId = <EMAIL>
                    username = user.username!!
                }

                cognitoClient.resendConfirmationCode(resendRequest)
                logger.info { "Successfully resent email verification for: ${resendVerificationRequest.email}" }

                return@withContext true
            } catch (e: UserNotFoundException) {
                logger.warn { "Resend verification failed: User not found: ${resendVerificationRequest.email}" }
                throw ResourceNotFoundException(
                    resourceType = "Email",
                    identifier = resendVerificationRequest.email,
                    cause = e
                )
            } catch (e: CognitoIdentityProviderException) {
                logger.error {
                    "Cognito error during resendEmailVerification " +
                            "for ${resendVerificationRequest.email}: ${e.message}"
                }
                throw InternalServerException(
                    message = "An error occurred while sending verification email",
                    cause = e
                )
            } catch (e: Exception) {
                logger.error {
                    "Unexpected error during resendEmailVerification " +
                            "for ${resendVerificationRequest.email}: ${e.message}"
                }
                throw InternalServerException(
                    message = "An unexpected error occurred",
                    cause = e
                )
            }
        }
}
