package com.cha.model.auth

import io.micronaut.core.annotation.Introspected
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size

@Introspected
@Serdeable
@Schema(name = "CreateUserRequest", description = "User creation request data")
data class CreateUserRequest(
    @field:NotBlank
    @field:Size(min = 3, max = 50)
    @Schema(description = "Username for the new user", example = "johndoe")
    val username: String,
    @field:NotBlank
    @field:Email
    @Schema(description = "Email address for the new user", example = "<EMAIL>")
    val email: String,
    @field:NotBlank
    @field:Size(min = 8, max = 100)
    @Schema(description = "Temporary password for the new user", example = "Temp123!")
    val temporaryPassword: String,
    @field:NotBlank
    @Schema(description = "First name of the user", example = "<PERSON>")
    val firstName: String,
    @field:NotBlank
    @Schema(description = "Last name of the user", example = "Doe")
    val lastName: String,
    @Schema(description = "Optional URL to profile picture", example = "https://example.com/profile.jpg")
    val pictureUrl: String = "",
    @Schema(
        description = "Optional user roles, defaults to `RESTRICTED_ACCESS` if not provided",
        example = "[\"FULL_ACCESS\", \"ADMIN\"]"
    )
    val roles: List<String> = listOf("RESTRICTED_ACCESS")
)
