package com.cha.filter

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.http.HttpRequest
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Filter
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import org.reactivestreams.Publisher
import org.slf4j.MDC
import reactor.core.publisher.Flux
import java.util.UUID

private val logger = KotlinLogging.logger {}

/**
 * Filter that adds a traceId to each request for correlation purposes.
 * The traceId is added to the MDC for logging and to the response headers.
 */
@Filter("/**")
class RequestTraceFilter : HttpServerFilter {

    companion object {
        const val TRACE_ID_HEADER = "X-Trace-ID"
        const val TRACE_ID_MDC_KEY = "traceId"
    }

    override fun doFilter(request: HttpRequest<*>, chain: ServerFilterChain): Publisher<MutableHttpResponse<*>> {
        val traceId = request.headers.get(TRACE_ID_HEADER) ?: UUID.randomUUID().toString()

        // Add traceId to MDC for logging
        MDC.put(TRACE_ID_MDC_KEY, traceId)

        logger.debug { "Processing request ${request.method} ${request.uri} with traceId: $traceId" }

        return Flux.from(chain.proceed(request))
            .doOnNext { response ->
                // Add traceId to response headers
                response.headers.add(TRACE_ID_HEADER, traceId)
            }
            .doOnError { error ->
                logger.error(error) { "Error processing request with traceId: $traceId" }
            }
            .doFinally {
                // Clean up MDC
                MDC.remove(TRACE_ID_MDC_KEY)
            }
    }
}
