package com.cha.model.dto

import com.cha.model.entity.DocumentEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(description = "Patient document information")
data class Document(
    @field:Schema(description = "Patient's Enterprise Master Patient Index", example = "12345")
    val empi: Int?,
    @field:Schema(description = "System identifier", example = "1")
    val systemId: Int?,
    @field:Schema(description = "Medical Record Number", example = "MRN123456")
    val mrn: String?,
    @field:Schema(description = "Account number", example = "ACC789012")
    val accountNumber: String?,
    @field:Schema(description = "Document title", example = "Discharge Summary")
    val title: String?,
    @field:Schema(description = "Document file type", example = "pdf")
    val type: String?,
    @field:Schema(description = "Document creation timestamp", example = "2024-01-15T10:30:00")
    val createdAt: LocalDateTime?,
    @field:Schema(description = "Document category", example = "Clinical Reports")
    val category: String?,
    @field:Schema(description = "System name where document originates", example = "HIS")
    val systemName: String?,
    @field:Schema(description = "Document filename", example = "discharge_summary_20240115.pdf")
    val filename: String?,
    @field:Schema(description = "Whether this is a progress note", example = "true")
    val progressNote: Boolean?,
    @field:Schema(description = "Admission date", example = "2024-01-15T08:00:00")
    val admitDate: LocalDateTime?,
    @field:Schema(description = "Security level flag", example = "false")
    val securityLevel: Boolean?,
)

fun DocumentEntity.toDto(): Document {
    return Document(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        createdAt = this.created,
        title = this.title,
        type = filename?.substringAfterLast(".", "")?.takeIf { it.isNotEmpty() }?.lowercase(),
        category = this.category,
        systemName = this.systemName,
        filename = this.filename,
        progressNote = this.progressNote,
        admitDate = this.admitDate,
        securityLevel = this.securityLevel
    )
}
