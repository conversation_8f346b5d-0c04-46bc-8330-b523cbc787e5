# Bootstrap configuration for test environment
# This disables AWS services during build/test
micronaut:
  application:
    name: viewer-backend
  config-client:
    enabled: false  # Disable distributed configuration for tests

aws:
  region: us-east-1
  secretsmanager:
    enabled: false  # Disable secrets manager for test builds
  client:
    system-manager:
      parameterstore:
        enabled: false  # Disable parameter store for test builds
  distributed-configuration:
    enabled: false  # Disable distributed configuration