#!/bin/bash

set -e  # Exit immediately if a command exits with a non-zero status

# Change to project root directory
cd "$(dirname "$0")/.." || { echo "Failed to change to project root"; exit 1; }

echo "Loading environment variables from .env file..."
if [ -f .env ]; then
  source .env
  # Export all variables from .env to child processes
  export $(grep -v '^#' .env | xargs)
else
  echo "Warning: .env file not found. Make sure all required environment variables are set."
fi

# Check for required environment variables
if [ -z "$AWS_ACCOUNT_ID" ]; then
  echo "Error: AWS_ACCOUNT_ID environment variable is required."
  exit 1
fi

echo "Building the backend JAR file..."
# Use the standard shadowJar or fatJar task instead of optimizedJitJarAll
./gradlew clean :backend:shadowJar || ./gradlew clean :backend:fatJar || ./gradlew clean :backend:build

# Try multiple possible JAR patterns to find the built JAR file
JAR_PATH=""
POSSIBLE_PATHS=(
  "./backend/build/libs/backend-*-all.jar"
  "./backend/build/libs/backend-*.jar"
  "./backend/build/libs/*-all.jar"
  "./backend/build/libs/*-fat.jar"
  "./backend/build/libs/*.jar"
)

for PATTERN in "${POSSIBLE_PATHS[@]}"; do
  FOUND_JAR=$(find ./backend/build/libs -name "$(basename $PATTERN)" 2>/dev/null | head -n 1)
  if [ -n "$FOUND_JAR" ]; then
    JAR_PATH="$FOUND_JAR"
    break
  fi
done

if [ -z "$JAR_PATH" ]; then
  echo "Error: Failed to build or locate the backend JAR file."
  echo "Expected path: ./backend/build/libs/backend-*-all.jar"
  exit 1
else
  echo "Found JAR file: $JAR_PATH"
fi

echo "Deploying infrastructure with CDK..."
cd infrastructure || { echo "Failed to change to infrastructure directory"; exit 1; }
cdk synth --quiet true

# Deploy the CDK stack
echo "Deploying CDK stack..."
cdk deploy --require-approval never

echo "Deployment completed successfully!"
