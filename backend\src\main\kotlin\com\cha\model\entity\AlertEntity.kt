package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("alerts")
data class AlertEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val alertCode: String?,
    val alertDescription: String?,
    val alertDate: LocalDateTime?,
    val systemName: String?,
    val admitDate: LocalDateTime?,
    val securityLevel: Boolean? = false
)
