package com.cha.domain

import com.cha.model.dto.Visit
import com.cha.model.dto.toDto
import com.cha.repository.VisitsRepository
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import jakarta.inject.Singleton
import java.time.LocalDate

@Singleton
class PatientVisitService(
    private val repository: VisitsRepository,
) {

    suspend fun getVisits(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<Visit> {
        return repository.findByEmpiAndMrnAndAccountNumber(empi, mrn, accountNumber, pageable)
            .map { it.toDto() }
    }

    suspend fun getVisits(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        startDate: LocalDate?,
        endDate: LocalDate?,
        pageable: Pageable,
    ): Page<Visit> {
        return repository.searchWithDateFilter(empi, mrn, accountNumber, startDate, endDate, pageable)
            .map { it.toDto() }
    }
}
