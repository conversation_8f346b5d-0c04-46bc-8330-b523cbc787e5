package com.cha.infrastructure

import software.amazon.awscdk.CfnOutput
import software.amazon.awscdk.Fn
import software.amazon.awscdk.RemovalPolicy
import software.amazon.awscdk.Stack
import software.amazon.awscdk.StackProps
import software.amazon.awscdk.services.certificatemanager.Certificate
import software.amazon.awscdk.services.certificatemanager.CertificateValidation
import software.amazon.awscdk.services.ec2.CfnRoute
import software.amazon.awscdk.services.ec2.CfnVPCPeeringConnection
import software.amazon.awscdk.services.ec2.IVpc
import software.amazon.awscdk.services.ec2.IpAddresses
import software.amazon.awscdk.services.ec2.SecurityGroup
import software.amazon.awscdk.services.ec2.SubnetConfiguration
import software.amazon.awscdk.services.ec2.SubnetType
import software.amazon.awscdk.services.ec2.Vpc
import software.amazon.awscdk.services.ec2.VpcLookupOptions
import software.amazon.awscdk.services.iam.Effect
import software.amazon.awscdk.services.iam.PolicyStatement
import software.amazon.awscdk.services.iam.ServicePrincipal
import software.amazon.awscdk.services.logs.CfnResourcePolicy
import software.amazon.awscdk.services.logs.LogGroup
import software.amazon.awscdk.services.logs.RetentionDays
import software.amazon.awscdk.services.rds.DatabaseInstance
import software.amazon.awscdk.services.rds.DatabaseInstanceAttributes
import software.amazon.awscdk.services.rds.IDatabaseInstance
import software.amazon.awscdk.services.route53.HostedZone
import software.amazon.awscdk.services.s3.BlockPublicAccess
import software.amazon.awscdk.services.s3.Bucket
import software.amazon.awscdk.services.s3.BucketEncryption
import software.amazon.awscdk.services.s3.IBucket
import software.constructs.Construct

class InfrastructureStack(
    scope: Construct,
    id: String,
    props: StackProps? = null,
    envName: String, // Renamed from environment to envName to avoid JVM signature conflict
    environmentVariables: Map<String, String>,
) : Stack(scope, id, props) {
    // --- VPC Configuration ---
    // Using a consistent name "viewer-app-vpc" for the VPC across all environments
    val vpc: Vpc = Vpc.Builder.create(this, "ViewerVpc")
        .vpcName("viewer-app-vpc") // Remove environment suffix for long-living resource
        .maxAzs(2)
        .ipAddresses(IpAddresses.cidr("10.0.0.0/16"))
        .subnetConfiguration(
            listOf(
                SubnetConfiguration.builder()
                    .name("public-subnet")
                    .subnetType(SubnetType.PUBLIC)
                    .cidrMask(24)
                    .build(),
                SubnetConfiguration.builder()
                    .name("private-subnet")
                    .subnetType(SubnetType.PRIVATE_WITH_EGRESS)
                    .cidrMask(24)
                    .build()
            )
        )
        .natGateways(if (envName == "prod") 2 else 1) // Use 2 NAT gateways for production for high availability
        .build()
    val documentsBucket: IBucket
    val loggingBucket: IBucket
    val rdsInstance: IDatabaseInstance // Changed from DatabaseInstance to IDatabaseInstance
    val rdsSecurityGroup: SecurityGroup
    val actualRdsSecurityGroup: SecurityGroup // The actual SG in the RDS VPC
    val rdsVpc: IVpc // Reference to the existing VPC where RDS resides
    val certificate: Certificate // Exposed ACM Certificate
    val hostedZone: HostedZone // New Route 53 Hosted Zone

    init {

        // --- S3 Bucket Configuration ---
        val loggingBucketName = "documents-access-logs-$envName"
        loggingBucket = Bucket.Builder.create(this, "DocumentsLoggingBucket")
            .bucketName(loggingBucketName)
            .encryption(BucketEncryption.S3_MANAGED)
            .blockPublicAccess(BlockPublicAccess.BLOCK_ALL)
            .removalPolicy(RemovalPolicy.RETAIN) // Always retain for all environments
            // Consider adding autoDeleteObjects for non-production environments if easier cleanup is desired
            // .autoDeleteObjects(envName != "prod")
            // .removalPolicy(if (envName == "prod") RemovalPolicy.RETAIN else RemovalPolicy.DESTROY)
            .build()

        // Add bucket policy for ALB access logs
        loggingBucket.addToResourcePolicy(
            PolicyStatement.Builder.create()
                .effect(Effect.ALLOW)
                .principals(listOf(ServicePrincipal("elasticloadbalancing.amazonaws.com")))
                .actions(listOf("s3:PutObject"))
                .resources(listOf(loggingBucket.bucketArn + "/alb-access-logs/*")) // Ensure this prefix matches the one used in ApplicationStack
                .conditions(
                    mapOf(
                        "StringEquals" to mapOf(
                            "s3:x-amz-acl" to "bucket-owner-full-control"
                        )
                    )
                )
                .build()
        )
        loggingBucket.addToResourcePolicy(
            PolicyStatement.Builder.create()
                .effect(Effect.ALLOW)
                .principals(listOf(ServicePrincipal("delivery.logs.amazonaws.com")))
                .actions(listOf("s3:PutObject"))
                .resources(listOf(loggingBucket.bucketArn + "/*"))
                .conditions(
                    mapOf(
                        "StringEquals" to mapOf(
                            "s3:x-amz-acl" to "bucket-owner-full-control"
                        )
                    )
                )
                .build()
        )
        loggingBucket.addToResourcePolicy(
            PolicyStatement.Builder.create()
                .effect(Effect.ALLOW)
                .principals(listOf(ServicePrincipal("delivery.logs.amazonaws.com")))
                .actions(listOf("s3:GetBucketAcl"))
                .resources(listOf(loggingBucket.bucketArn))
                .build()
        )

        // Check if the documents bucket already exists, otherwise create it
        val documentsBucketName = "viewer-documents"
        documentsBucket = try {
            Bucket.fromBucketName(this, "ExistingDocumentsBucket", documentsBucketName)
        } catch (_: Exception) {
            Bucket.Builder.create(this, "DocumentsBucket")
                .bucketName(documentsBucketName)
                .versioned(true)
                .encryption(BucketEncryption.S3_MANAGED)
                .blockPublicAccess(BlockPublicAccess.BLOCK_ALL)
                .serverAccessLogsBucket(loggingBucket)
                .serverAccessLogsPrefix("access-logs/${documentsBucketName}/")
                .removalPolicy(RemovalPolicy.RETAIN) // Always retain
                .build()
        }

        // --- RDS Security Group ---
        // Look up the existing VPC where RDS resides
        rdsVpc = Vpc.fromLookup(
            this, "RdsVpc", VpcLookupOptions.builder()
                .isDefault(true)
                .build()
        )

        // Create a security group in our new VPC
        rdsSecurityGroup = SecurityGroup.Builder.create(this, "RdsSecurityGroup")
            .vpc(vpc)  // Important: This security group is in our new VPC
            .description("Security group for RDS connection from app VPC")
            .allowAllOutbound(true)
            .build()

        // Create/import the actual security group in the RDS VPC
        // This is the security group that's actually attached to the RDS instance
        actualRdsSecurityGroup = SecurityGroup.Builder.create(this, "ActualRdsSecurityGroup")
            .vpc(rdsVpc)  // This security group is in the RDS VPC
            .description("Security group for RDS instance in the default VPC")
            .allowAllOutbound(true)
            .securityGroupName("viewer-app-rds-sg-$envName") // Give it a descriptive name
            .build()

        // Import the existing RDS instance
        // Note: This creates a reference to the existing RDS instance but doesn't actually manage it
        rdsInstance = DatabaseInstance.fromDatabaseInstanceAttributes(
            this,
            "ImportedRdsInstance",
            DatabaseInstanceAttributes.builder()
                .instanceIdentifier("chards03")
                .instanceEndpointAddress("chards03.cavet07fhltc.us-east-1.rds.amazonaws.com")
                .port(1443)
                .securityGroups(listOf(actualRdsSecurityGroup)) // Use the actual RDS security group
                .build()
        )

        // --- VPC Peering Connection ---
        // Create VPC peering connection between the new VPC and RDS VPC
        val vpcPeeringConnection = CfnVPCPeeringConnection.Builder.create(this, "VpcPeeringConnection")
            .vpcId(vpc.vpcId)                // Requester VPC (our new VPC)
            .peerVpcId(rdsVpc.vpcId)         // Accepter VPC (existing RDS VPC)
            .peerOwnerId(account)            // Same AWS account
            .peerRegion(region)              // Same region
            .build()

        // Mark the peering connection as essential metadata to be displayed in the output
        CfnOutput.Builder.create(this, "VpcPeeringConnectionId")
            .exportName("VpcPeeringConnectionId-${envName}")
            .value(vpcPeeringConnection.ref)
            .description("ID of the VPC peering connection")
            .build()

        // --- Route Configuration for VPC Peering ---
        // Add routes from our new VPC to RDS VPC
        // For each public subnet in our VPC
        vpc.publicSubnets.forEach { subnet ->
            CfnRoute.Builder.create(this, "PublicRouteToRdsVpc${subnet.node.id}")
                .routeTableId(subnet.routeTable.routeTableId)
                .destinationCidrBlock(rdsVpc.vpcCidrBlock)  // Route to the RDS VPC CIDR
                .vpcPeeringConnectionId(vpcPeeringConnection.ref)  // Via the peering connection
                .build()
        }

        // For each private subnet in our VPC
        vpc.privateSubnets.forEach { subnet ->
            CfnRoute.Builder.create(this, "PrivateRouteToRdsVpc${subnet.node.id}")
                .routeTableId(subnet.routeTable.routeTableId)
                .destinationCidrBlock(rdsVpc.vpcCidrBlock)  // Route to the RDS VPC CIDR
                .vpcPeeringConnectionId(vpcPeeringConnection.ref)  // Via the peering connection
                .build()
        }

        // Add routes from RDS VPC to our new VPC for each subnet in the RDS VPC
        // This enables bi-directional communication necessary for VPC peering
        // Note: Since we don't know the route table IDs directly, we create an output that can help in manual configuration
        CfnOutput.Builder.create(this, "RdsVpcCidrBlock")
            .exportName("RdsVpcCidrBlock-${envName}")
            .value(rdsVpc.vpcCidrBlock)
            .description("CIDR block of the RDS VPC")
            .build()

        CfnOutput.Builder.create(this, "ViewerVpcCidrBlock")
            .exportName("ViewerVpcCidrBlock-${envName}")
            .value(vpc.vpcCidrBlock)
            .description("CIDR block of the Viewer app VPC")
            .build()

        CfnOutput.Builder.create(this, "ActualRdsSecurityGroupId")
            .exportName("ActualRdsSecurityGroupId-${envName}")
            .value(actualRdsSecurityGroup.securityGroupId)
            .description("Security group ID for the RDS instance")
            .build()

        // --- NEW Route 53 Hosted Zone ---
        val queryLogGroup = LogGroup.Builder.create(this, "ViewerHostedZoneLogGroup")
            .logGroupName("/aws/route53/chaviewer.com")
            .retention(RetentionDays.ONE_MONTH)
            .removalPolicy(RemovalPolicy.DESTROY)
            .build()

        // Add resource policy to allow Route 53 to write query logs
        CfnResourcePolicy.Builder.create(this, "Route53QueryLogsResourcePolicy")
            .policyName("Route53QueryLogsPolicy")
            .policyDocument(
                """
                {
                  "Version": "2012-10-17",
                  "Statement": [
                    {
                      "Effect": "Allow",
                      "Principal": { "Service": "route53.amazonaws.com" },
                      "Action": [
                        "logs:CreateLogStream",
                        "logs:PutLogEvents",
                        "logs:DescribeLogStreams"
                      ],
                      "Resource": "${queryLogGroup.logGroupArn}:*"
                    }
                  ]
                }
            """.trimIndent()
            )
            .build()

        // This will create a new public hosted zone for your domain.
        hostedZone = HostedZone.Builder.create(this, "ViewerHostedZone")
            .zoneName("chaviewer.com")
            .comment("Hosted zone for the ChaViewer project")
            .queryLogsLogGroupArn(queryLogGroup.logGroupArn)
            .build()

        // --- Output the new Name Servers ---
        // You will need these values for your domain registrar.
        // Output each NS record individually to avoid token list issues
        hostedZone.hostedZoneNameServers?.let { nsList ->
            for (i in nsList.indices) {
                CfnOutput.Builder.create(this, "NameServer${i}Output")
                    .exportName("ViewerNameServer${i + 1}-$envName")
                    .value(Fn.select(i, nsList))
                    .description("NS record ${i + 1} for the domain registrar.")
                    .build()
            }
        }

        // --- ACM Certificate ---
        certificate = Certificate.Builder.create(this, "ViewerCertificate")
            .domainName("chaviewer.com")
            .subjectAlternativeNames(listOf("*.chaviewer.com"))
            .validation(CertificateValidation.fromDns(hostedZone))
            .certificateName("Viewer-Site-Certificate")
            .build()

        CfnOutput.Builder.create(this, "CertificateArnOutput")
            .exportName("ViewerCertificateArn-$envName")
            .value(certificate.certificateArn)
            .description("ARN of the ACM certificate for chaviewer.com")
            .build()
    }
}
