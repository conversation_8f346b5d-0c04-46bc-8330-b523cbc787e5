package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable

private fun addAlertsSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.alerts?.let { alerts ->
        document.add(createSectionTitle("Alerts"))
        if (alerts.isNotEmpty()) {
            alerts.forEach { alert ->
                val alertTable = PdfPTable(2)
                alertTable.widthPercentage = 100f
                setupDataItemTable(alertTable)
                val headerText = "${alert.alertDescription ?: "Alert"} (${alert.alertCode ?: "N/A"})"
                alertTable.addCell(createDataItemHeaderCell(headerText, 2))
                alertTable.addCell(
                    createLabelValueCell(
                        "Alert Date:",
                        alert.alertDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                document.add(alertTable)
            }
        } else {
            val paragraph = Paragraph("No alerts recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}