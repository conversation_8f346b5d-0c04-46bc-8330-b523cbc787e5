package com.cha.model.entity

import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity

@MappedEntity("addresses")
data class AddressEntity(
    @field:Id
    val id: Int,
    val systemId: Int?,
    val mrn: String?,
    val empi: Int?,
    val address: String?,
    val city: String?,
    val state: String?,
    val zipCode: String?,
    val telephone: String?,
    val systemName: String?
)
