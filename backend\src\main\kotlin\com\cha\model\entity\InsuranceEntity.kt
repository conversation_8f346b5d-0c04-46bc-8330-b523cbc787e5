package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("insurance")
data class InsuranceEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val insurancePlan: String?,
    val insSeq: String?,
    val policyHolder: String?,
    val policyNumber: String?,
    val certificateNo: String?,
    val organizationName: String?,
    val policyHolderSsn: String?,
    val groupNumber: String?,
    val groupName: String?,
    val subscriberNumber: String?,
    val systemName: String?,
    val securityLevel: Boolean? = false,
    val admitDate: LocalDateTime?
)
