package com.cha.util

import io.micronaut.data.model.Pageable
import io.micronaut.data.model.Sort
import io.micronaut.http.HttpRequest
import java.net.URLDecoder
import java.nio.charset.StandardCharsets

/**
 * Parses sorting information from the HTTP request's "sort" query
 * parameter and applies it to the given Pageable object.
 *
 * **Note:** This is a workaround for what appears to be a bug in Micronaut
 * Data's support for sorting.
 *
 * @param pageable The base Pageable object (typically injected by
 *    Micronaut, used for page number and size).
 * @param defaultSortProperty The property to sort by if no "sort"
 *    parameter is provided or if it's malformed.
 * @param defaultSortDirection The direction for the default sort.
 * @return A new Pageable object with the resolved sorting, or the original
 *    if it was already sorted and no "sort" param was found.
 */
fun HttpRequest<*>.parsePageableWithSorting(
    pageable: Pageable,
    defaultSortProperty: String,
    defaultSortDirection: Sort.Order.Direction = Sort.Order.Direction.ASC
): Pageable {
    val sortQueryParam: String? = uri.rawQuery?.let { rawQuery ->
        rawQuery.split('&').associate { param ->
            val parts = param.split('=', limit = 2)
            val key = URLDecoder.decode(parts[0], StandardCharsets.UTF_8.name())
            val value = if (parts.size > 1) URLDecoder.decode(parts[1], StandardCharsets.UTF_8.name()) else ""
            key to value
        }["sort"]
    }

    if (!sortQueryParam.isNullOrBlank()) {
        // TODO size doesn't seem to be properly setting to previous value (size=20 but will always return size=100)
        val parts = sortQueryParam.split(',')
        // Ensure property is not an empty string after trimming
        val property = parts.getOrNull(0)?.trim()?.takeIf { it.isNotEmpty() }

        if (property != null) {
            val directionStr = parts.getOrNull(1)?.trim()
            val direction = when (directionStr?.lowercase()) {
                "desc" -> Sort.Order.Direction.DESC
                else -> Sort.Order.Direction.ASC // Default to ASC if not "desc" or if direction part is missing/empty
            }
            return Pageable.from(pageable.number, pageable.size, Sort.of(Sort.Order(property, direction, false)))
        }
        // Malformed sort parameter (e.g., "?sort=,desc" or "?sort=" where property is blank),
        // fall through to use default sort or existing pageable sort.
    }

    // No valid sortQueryParam was found or it was malformed (empty property part).
    // If the original pageable (from Micronaut) already has sorting, respect it.
    // Otherwise, apply the controller-defined default sort.
    return if (pageable.sort.isSorted) {
        pageable
    } else {
        Pageable.from(
            pageable.number,
            pageable.size,
            Sort.of(Sort.Order(defaultSortProperty, defaultSortDirection, false))
        )
    }
}

