package com.cha.config

import aws.sdk.kotlin.services.cognitoidentityprovider.CognitoIdentityProviderClient
import aws.sdk.kotlin.services.s3.S3Client
import aws.smithy.kotlin.runtime.client.LogMode
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton

private val log = KotlinLogging.logger { }

@Factory
class AwsConfig {

    @Value("\${aws.region}")
    lateinit var awsRegion: String

    @Value("\${micronaut.environments:}")
    lateinit var environment: String

    @Singleton
    fun cognitoClient(): CognitoIdentityProviderClient {
        return CognitoIdentityProviderClient {
            this.region = awsRegion
            this.logMode = if (environment != "prod") {
                LogMode.LogRequestWithBody + LogMode.LogResponseWithBody
            } else {
                LogMode.Default
            }

            // When running on Fargate, the SDK automatically uses the
            // Fargate execution role's credentials via the default provider chain.
            // No explicit credential provider configuration is typically needed here.
        }
    }

    @Singleton
    fun s3Client(): S3Client {
        log.debug { "New instance of S3Client being created." }
        return S3Client {
            region = awsRegion
            logMode = if (environment != "prod") {
                LogMode.LogRequestWithBody + LogMode.LogResponseWithBody
            } else {
                LogMode.Default
            }

            // When running on Fargate, the SDK automatically uses the
            // Fargate execution role's credentials via the default provider chain.
        }
    }
}
