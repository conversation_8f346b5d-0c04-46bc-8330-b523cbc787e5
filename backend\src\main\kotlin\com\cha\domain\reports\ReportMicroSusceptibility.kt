package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable

private fun addMicroSusceptibilitySection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.microSuscept?.let { microSuscepts ->
        document.add(createSectionTitle("Microbiology Susceptibility"))
        if (microSuscepts.isNotEmpty()) {
            microSuscepts.forEach { suscept ->
                val susceptTable = PdfPTable(2)
                susceptTable.widthPercentage = 100f
                setupDataItemTable(susceptTable)

                val headerText = suscept.antibodyDescription ?: "Susceptibility Test"
                susceptTable.addCell(createDataItemHeaderCell(headerText, 2))

                susceptTable.addCell(createLabelValueCell("Accession #:", suscept.accessionNumber, inTable = true))
                susceptTable.addCell(createLabelValueCell("Isolate:", suscept.accessionIsolate, inTable = true))
                susceptTable.addCell(
                    createLabelValueCell(
                        "Result:",
                        "${suscept.resultQualifierCode ?: ""} ${suscept.resultValueNumber ?: ""}".trim(),
                        inTable = true
                    )
                )
                susceptTable.addCell(createLabelValueCell("Category:", suscept.categoryDescription, inTable = true))
                susceptTable.addCell(createLabelValueCell("Entered By:", suscept.enteredBy, inTable = true))
                susceptTable.addCell(
                    createLabelValueCell(
                        "Entered Date:",
                        suscept.enteredDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )

                document.add(susceptTable)
            }
        } else {
            val paragraph = Paragraph("No microbiology susceptibility results recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}