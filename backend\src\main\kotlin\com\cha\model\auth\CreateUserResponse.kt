package com.cha.model.auth

import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant

@Serdeable
@Schema(name = "CreateUserResponse", description = "Response for user creation")
data class CreateUserResponse(
    @Schema(description = "Unique identifier for the created user", example = "abc123-def456")
    val userId: String,
    @Schema(description = "Username of the created user", example = "johndoe")
    val username: String,
    @Schema(description = "Email address of the created user", example = "<EMAIL>")
    val email: String,
    @Schema(description = "Time when the user was created", example = "2023-01-15T10:30:00Z")
    val createdAt: Instant = Instant.now(),
    @Schema(description = "Assigned roles for the user", example = "[\"ROLE_USER\", \"ROLE_ADMIN\"]")
    val roles: List<String>
)
