package com.cha.domain

import com.cha.domain.exception.InvalidRequestException
import com.cha.fake.FakeAddressRepository
import com.cha.fake.FakePatientRepository
import com.cha.fake.FakeTelephoneRepository
import com.cha.model.dto.SearchType
import com.cha.model.entity.PatientEntity
import com.cha.repository.AddressRepository
import com.cha.repository.TelephoneRepository
import io.micronaut.data.model.Pageable
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDate

@MicronautTest
class PatientServiceTest {
    private lateinit var patientRepository: FakePatientRepository
    private lateinit var addressRepository: AddressRepository
    private lateinit var telephoneRepository: TelephoneRepository
    private lateinit var patientService: PatientService

    @BeforeEach
    fun setup() {
        patientRepository = FakePatientRepository()
        addressRepository = FakeAddressRepository()
        telephoneRepository = FakeTelephoneRepository()
        patientService = PatientService(patientRepository, addressRepository, telephoneRepository)
        patientRepository.seed(
            PatientEntity(
                empi = 1,
                name = "John Smith",
                mrn = "MRN123",
                bthTs = LocalDate.of(1990, 1, 1),
                lastName = "Smith",
                firstName = "John",
                systemId = 1,
                systemName = "TestSystem",
                sex = "M"
            ),
            PatientEntity(
                empi = 2,
                name = "Jane Doe",
                mrn = "MRN456",
                bthTs = LocalDate.of(1985, 5, 5),
                lastName = "Doe",
                firstName = "Jane",
                systemId = 2,
                systemName = "TestSystem",
                sex = "F"
            ),
            PatientEntity(
                empi = 3,
                name = "Alice Smith",
                mrn = "MRN789",
                bthTs = LocalDate.of(1990, 1, 1),
                lastName = "Smith",
                firstName = "Alice",
                systemId = 3,
                systemName = "TestSystem",
                sex = "F"
            ),
            PatientEntity(
                empi = 4,
                name = "Bob Johnson",
                mrn = "MRN321",
                bthTs = LocalDate.of(1978, 12, 12),
                lastName = "Johnson",
                firstName = "Bob",
                systemId = 4,
                systemName = "TestSystem",
                sex = "M"
            ),
            PatientEntity(
                empi = 5,
                name = "Carol White",
                mrn = "MRN654",
                bthTs = LocalDate.of(2000, 7, 7),
                lastName = "White",
                firstName = "Carol",
                systemId = 5,
                systemName = "TestSystem",
                sex = "F"
            ),
            PatientEntity(
                empi = 6,
                name = "David Brown",
                mrn = "MRN987",
                bthTs = LocalDate.of(1995, 3, 15),
                lastName = "Brown",
                firstName = "David",
                systemId = 6,
                systemName = "TestSystem",
                sex = "M"
            )
        )
    }

    @Test
    fun `should return patients matching name search`() = runTest {
        val pageable = Pageable.from(0, 10)
        val results = patientService.searchPatients("Smith", SearchType.NAME, pageable)
        assertEquals(2, results.totalSize)
        assertTrue(results.content.all { it.lastName == "Smith" })
    }

    @Test
    fun `should return patients matching MRN search`() = runTest {
        val pageable = Pageable.from(0, 10)
        val results = patientService.searchPatients("MRN456", SearchType.MRN, pageable)
        assertEquals(1, results.totalSize)
        val patient = results.content.first()
        assertEquals("Jane", patient.firstName)
        assertEquals("Doe", patient.lastName)
    }

    @Test
    fun `should return patients matching birthdate search`() = runTest {
        val pageable = Pageable.from(0, 10)
        val results = patientService.searchPatients("1990-01-01", SearchType.DOB, pageable)
        assertEquals(2, results.totalSize)
        val lastNames = results.content.map { it.lastName }
        assertTrue(lastNames.contains("Smith"))
    }

    @Test
    fun `should throw Problem for invalid birthdate format`() = runTest {
        val pageable = Pageable.from(0, 10)
        val exception = assertThrows<InvalidRequestException> {
            runBlocking {
                patientService.searchPatients("not-a-date", SearchType.DOB, pageable)
            }
        }
        assertTrue(exception.message?.contains("not in a valid format") == true)
    }
}
