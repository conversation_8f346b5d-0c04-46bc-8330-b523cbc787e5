package com.cha.repository

import com.cha.model.entity.MicroSusceptEntity
import io.micronaut.data.annotation.Query
import io.micronaut.data.jdbc.annotation.JdbcRepository
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.data.model.query.builder.sql.Dialect
import io.micronaut.data.repository.kotlin.CoroutinePageableCrudRepository
import jakarta.validation.Valid

@JdbcRepository(dialect = Dialect.SQL_SERVER)
interface MicroSusceptRepository : CoroutinePageableCrudRepository<@Valid MicroSusceptEntity, Long> {

    @Query(
        value = """
            SELECT * FROM micro_suscept p
            WHERE p.empi = :empi
            AND (:mrn IS NULL OR p.mrn = :mrn)
            AND (:accountNumber IS NULL OR p.account_number = :accountNumber)
        """,
        countQuery = """
            SELECT COUNT(*) FROM micro_suscept p
            WHERE p.empi = :empi
            AND (:mrn IS NULL OR p.mrn = :mrn)
            AND (:accountNumber IS NULL OR p.account_number = :accountNumber)
        """,
        nativeQuery = true
    )
    suspend fun findByEmpiAndMrnAndAccountNumber(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<MicroSusceptEntity>
}
