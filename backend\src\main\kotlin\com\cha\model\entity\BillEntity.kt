package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("bills")
data class BillEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val serviceDate: LocalDateTime?,
    val postingDate: LocalDateTime?,
    val chargeCode: String?,
    val chargeCodeDescription: String?,
    val invoiceNumber: String?,
    val nrv: String?,
    val department: String?,
    val account: String?,
    val amount: Long?,
    val insurance: String?,
    val totalAmount: Long?,
    val units: Int?,
    val financialClass: String?,
    val visitStatus: String?,
    val transactionType: String?,
    val transactionDate: LocalDateTime?,
    val systemName: String?,
    val securityLevel: Boolean? = false,
    val admitDate: LocalDateTime?,
)