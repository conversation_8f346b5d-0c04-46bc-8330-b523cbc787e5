package com.cha.controller

import com.cha.domain.PatientService
import com.cha.domain.PatientVisitService
import com.cha.domain.UserService
import com.cha.model.dto.Patient
import com.cha.model.dto.PatientInfo
import com.cha.model.dto.SearchType
import com.cha.model.dto.Visit
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.rules.SecurityRule
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.zalando.problem.Problem

@Secured(SecurityRule.IS_AUTHENTICATED)
@Controller("/v1/patients")
@Tag(name = "Patient", description = "Operations related to patient management and retrieval")
class PatientsController(
    private val patientService: PatientService,
    private val patientVisitService: PatientVisitService,
    private val userService: UserService,
) {

    @Get("/search")
    @Operation(summary = "Search patients", description = "Searches for patients by name, MRN, or birthdate")
    @ApiResponse(responseCode = "200", description = "Search results returned successfully")
    @ApiResponse(responseCode = "400", description = "Invalid search parameters")
    @ApiResponse(
        responseCode = "401", description = "Unauthorized - Authentication required", content = [Content(
            mediaType = "application/problem+json",
            schema = Schema(implementation = Problem::class)
        )]
    )
    suspend fun searchPatients(
        @Parameter(description = "Search term") @QueryValue q: String,
        @Parameter(description = "Search type") @QueryValue(defaultValue = "name") type: SearchType,
        @Parameter(
            description = "Page number (0-based)",
            required = false,
            example = "0"
        ) @QueryValue("page") page: Int? = null,
        @Parameter(description = "Page size", required = false, example = "20") @QueryValue("size") size: Int? = null,
        @Parameter(
            description = "Sort criteria in the format: property,(asc|desc). Can be specified multiple times.",
            required = false,
            example = "lastName,asc"
        ) @QueryValue("sort") sort: List<String>? = null,
        @Parameter(required = false, hidden = true) pageable: Pageable,
    ): Page<Patient> {
        return patientService.searchPatients(q, type, pageable)
    }

    @Get("/{empi}")
    @Operation(
        summary = "Get patient by EMPI",
        description = "Retrieves a patient by their EMPI with detailed information including addresses and telephone numbers"
    )
    @ApiResponse(
        responseCode = "200", description = "Successfully retrieved patient", content = [Content(
            mediaType = MediaType.APPLICATION_JSON,
            schema = Schema(implementation = PatientInfo::class)
        )]
    )
    @ApiResponse(
        responseCode = "401", description = "Unauthorized - Authentication required", content = [Content(
            mediaType = "application/problem+json",
            schema = Schema(implementation = Problem::class)
        )]
    )
    @ApiResponse(
        responseCode = "404", description = "Patient not found", content = [Content(
            mediaType = "application/problem+json",
            schema = Schema(implementation = Problem::class)
        )]
    )
    suspend fun getPatient(
        @Parameter(description = "Patient's EMPI") @PathVariable empi: Int,
        @Parameter(required = false, hidden = true) authentication: Authentication,
    ): PatientInfo {
        val userId = authentication.attributes["sub"] as String
        val patientInfo = patientService.getPatientInfo(empi)
        userService.addPatientHistoryEntry(userId, empi.toString())
        return patientInfo
    }

    @Get("/{empi}/visits")
    @Operation(
        summary = "Get patient visits by EMPI",
        description = "Retrieves all visits for a patient identified by their EMPI"
    )
    @ApiResponse(responseCode = "200", description = "Successfully retrieved patient visits")
    @ApiResponse(
        responseCode = "401", description = "Unauthorized - Authentication required", content = [Content(
            mediaType = "application/problem+json",
            schema = Schema(implementation = Problem::class)
        )]
    )
    @ApiResponse(responseCode = "404", description = "Patient not found")
    suspend fun getPatientVisits(
        @Parameter(description = "Patient's EMPI") @PathVariable empi: Int,
        @Parameter(
            description = "Page number (0-based)",
            required = false,
            example = "0"
        ) @QueryValue("page") page: Int? = null,
        @Parameter(description = "Page size", required = false, example = "20") @QueryValue("size") size: Int? = null,
        @Parameter(
            description = "Sort criteria in the format: property,(asc|desc). Can be specified multiple times.",
            required = false,
            example = "lastName,asc"
        ) @QueryValue("sort") sort: List<String>? = null,
        @Parameter(required = false, hidden = true) pageable: Pageable,
    ): Page<Visit> {
        return patientVisitService.getVisits(empi, null, null, pageable)
    }
}
