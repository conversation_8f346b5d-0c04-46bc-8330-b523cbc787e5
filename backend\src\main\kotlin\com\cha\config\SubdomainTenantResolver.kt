package com.cha.config

//@Singleton
//@Requires(property = "micronaut.multitenancy.tenantResolver.subdomain.enabled", value = "true")
//@Requires(property = "micronaut.multitenancy.fixed.enabled", value = "false", defaultValue = "false")
//class SubdomainTenantResolver(
//    private val httpHostResolver: HttpHostResolver,
//) : TenantResolver {
//
//    override fun resolveTenantIdentifier(): Serializable? {
//        // TODO just throw an exception if tenant id is missing.
//
//        // Get the current request from the server context
//        val request = ServerRequestContext.currentRequest<Any>().orElse(null) as? HttpRequest<*>
//            ?: return null
//
//        val host = httpHostResolver.resolve(request)
//
//        // Extract tenant ID from subdomain (e.g., "lrh.example.com" -> "lrh")
//        val subdomain = host.split('.').firstOrNull()
//        return subdomain
//    }
//}
