package com.cha.model.dto

import com.cha.model.entity.MedicationEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a medication record for a patient, including drug details, dosage, route, and administration information."
)
data class Medication(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int?,
    @Schema(description = "System identifier for the medication record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the medication record.")
    val accountNumber: String?,
    @Schema(description = "Name of the medication.")
    val brandName: String?,
    val genericName: String?,
    val dosage: String?,
    @Schema(description = "Route of administration (e.g., oral, IV).")
    val dosageForm: String?,
    @Schema(description = "Frequency of administration.")
    val frequency: String?,
    @Schema(description = "Start date of the medication.")
    val startDatetime: LocalDateTime?,
    @Schema(description = "End date of the medication, if applicable.")
    val stopDatetime: LocalDateTime?,
    val caregiver: String?,
    val systemName: String?,
    @Schema(description = "Indicates if the medication record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean?,
    @Schema(description = "Admission date associated with the medication record, if relevant.")
    val admitDate: LocalDateTime?
)

fun MedicationEntity.toDto(): Medication {
    return Medication(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        brandName = this.brandName,
        genericName = this.genericName,
        dosage = this.dosage,
        dosageForm = this.dosageForm,
        frequency = this.frequency,
        startDatetime = this.startDatetime,
        stopDatetime = this.stopDatetime,
        caregiver = this.caregiver,
        systemName = this.systemName,
        securityLevel = this.securityLevel,
        admitDate = this.admitDate
    )
}