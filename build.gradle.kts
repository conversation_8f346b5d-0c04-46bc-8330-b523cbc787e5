plugins {
    id("org.jetbrains.kotlin.plugin.allopen") version "2.2.0" apply false
    id("com.gradleup.shadow") version "8.3.7" apply false
    id("io.micronaut.application") version "4.5.4" apply false
    alias(libs.plugins.detekt)
}

// Apply detekt to all subprojects
subprojects {
    apply(plugin = "io.gitlab.arturbosch.detekt")
}

// Configure detekt for all subprojects
configure(subprojects) {
    tasks.withType<io.gitlab.arturbosch.detekt.Detekt>().configureEach {
        buildUponDefaultConfig = true
        allRules = false
        config.setFrom("${rootProject.projectDir}/config/detekt/config.yml")
        ignoreFailures = true

        // Set baseline files based on project name
        when (project.name) {
            "backend" -> baseline.set(file("${rootProject.projectDir}/config/detekt/backend-baseline.xml"))
            "infrastructure" -> baseline.set(file("${rootProject.projectDir}/config/detekt/infrastructure-baseline.xml"))
        }
    }
}
