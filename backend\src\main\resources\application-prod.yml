# Production-specific configuration
# This file automatically overrides settings in application.yml when MICRONAUT_ENVIRONMENTS=prod

micronaut:
  application:
    tracing:
      sampler:
        probability: 0.25   # 25% sampling for production environment
  
  # Production-specific security settings
  security:
    endpoints:
      loggers:
        sensitive: true
  
  # Production-specific metrics settings
  metrics:
    export:
      cloudwatch:
        enabled: true
        namespace: ViewerBackend-Production
        step: PT1M  # Export metrics every 1 minute

  # More restrictive CORS settings for production
  server:
    cors:
      configurations:
        all:
          # TODO: limiting allowed origins in production instead of using .*
          # allowed-origins:
          #   - https://your-production-domain.com
          max-age: 3600  # Increased cache time for CORS preflight in production


# Production-specific datasource optimizations
datasources:
  default:
    hikari:
      maximumPoolSize: 12 # Higher for production with 2 instances handling more load
      minimumIdle: 4 # More idle connections for production stability
      leakDetectionThreshold: 120000 # 2 minutes - enable leak detection in production for monitoring

endpoints:
  health:
    enabled: true
    details-visible: AUTHENTICATED
  info:
    enabled: true
  loggers:
    enabled: true
