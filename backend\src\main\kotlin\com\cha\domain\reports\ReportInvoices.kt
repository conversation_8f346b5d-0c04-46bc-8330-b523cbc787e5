package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable

private fun addInvoicesSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.financial?.invoices?.let { invoices ->
        document.add(createSectionTitle("Invoices"))
        if (invoices.isNotEmpty()) {
            invoices.forEach { invoice ->
                val invoiceTable = PdfPTable(2)
                invoiceTable.widthPercentage = 100f
                setupDataItemTable(invoiceTable)

                val headerText = "Invoice #${invoice.invoiceNumber}"
                invoiceTable.addCell(createDataItemHeaderCell(headerText, 2))

                invoiceTable.addCell(
                    createLabelValueCell(
                        "Total Amount:",
                        invoice.totalBilled.toString(),
                        inTable = true
                    )
                )
                invoiceTable.addCell(
                    createLabelValueCell(
                        "Accounts Receivable Status:",
                        invoice.arStatus,
                        inTable = true
                    )
                )
                invoiceTable.addCell(createLabelValueCell("Insurance Plan:", invoice.insurancePlan, inTable = true))
                invoiceTable.addCell(
                    createLabelValueCell(
                        "Insurance Payment:",
                        invoice.insurancePayments.toString(),
                        inTable = true
                    )
                )
                invoiceTable.addCell(
                    createLabelValueCell(
                        "Insurance Payment Amount:",
                        invoice.insurancePaymentAmount.toString(),
                        inTable = true
                    )
                )
                invoiceTable.addCell(
                    createLabelValueCell(
                        "Insurance Adjustment Amount:",
                        invoice.insuranceAdjustmentAmount.toString(),
                        inTable = true
                    )
                )
                invoiceTable.addCell(
                    createLabelValueCell(
                        "Coinsurance Payment:",
                        invoice.coinsurancePayments.toString(),
                        inTable = true
                    )
                )
                invoiceTable.addCell(
                    createLabelValueCell(
                        "Patient Payment:",
                        invoice.patientPayments.toString(),
                        inTable = true
                    )
                )
                invoiceTable.addCell(
                    createLabelValueCell(
                        "Patient Payment Amount:",
                        invoice.patientPaymentAmount.toString(),
                        inTable = true
                    )
                )
                invoiceTable.addCell(
                    createLabelValueCell(
                        "Patient Adjustment Amount:",
                        invoice.patientAdjustmentAmount.toString(),
                        inTable = true
                    )
                )
                invoiceTable.addCell(createLabelValueCell("Balance:", invoice.balance.toString(), inTable = true))
                invoiceTable.addCell(
                    createLabelValueCell(
                        "Billing Date:",
                        invoice.billingDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                invoiceTable.addCell(
                    createLabelValueCell(
                        "Service Date:",
                        "${invoice.serviceFromDate?.let { formatDateTime(it) }} - ${
                            invoice.serviceThruDate?.let {
                                formatDateTime(
                                    it
                                )
                            }
                        }",
                        inTable = true
                    )
                )

                document.add(invoiceTable)
            }
        } else {
            val paragraph = Paragraph("No invoice information recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}