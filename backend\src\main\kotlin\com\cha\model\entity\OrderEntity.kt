package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import jakarta.persistence.Column
import java.time.LocalDateTime

@MappedEntity("orders")
data class OrderEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val orderNumber: Int?,
    val orderedBy: String?,
    val verifiedBy: String?,
    val discontinuedBy: String?,
    val caregiver: String?,
    val frequency: String?,
    val enteredDatetime: String?,
    val priority: String?,
    @field:Column(name = "special_instructions_1")
    val specialInstructions1: String?,
    @field:Column(name = "special_instructions_2")
    val specialInstructions2: String?,
    @field:Column(name = "special_instructions_3")
    val specialInstructions3: String?,
    val orderStatusCode: String?,
    val orderCode: String?,
    val orderCodeDescription: String?,
    val collectedDatetime: LocalDateTime? = null,
    val collectedBy: String?,
    val serviceDatetime: LocalDateTime? = null,
    val receivedDatetime: LocalDateTime? = null,
    val releasedDatetime: LocalDateTime? = null,
    val releasedBy: String?,
    val accessionNumber: String?,
    val orderTypeId: Int?,
    val orderTypeDescription: String?,
    val orderArea: String?,
    val systemName: String?,
    val securityLevel: Boolean? = false,
    val admitDate: LocalDateTime? = null
)
