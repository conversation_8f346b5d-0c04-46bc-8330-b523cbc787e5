package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable

private fun addImmunizationsSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.immunizations?.let { immunizations ->
        document.add(createSectionTitle("Immunizations"))
        if (immunizations.isNotEmpty()) {
            immunizations.forEach { immunization ->
                val immunizationTable = PdfPTable(2)
                immunizationTable.widthPercentage = 100f
                setupDataItemTable(immunizationTable)

                val headerText = immunization.immunization ?: "Immunization"
                immunizationTable.addCell(createDataItemHeaderCell(headerText, 2))

                immunizationTable.addCell(
                    createLabelValueCell(
                        "Lot Number:",
                        immunization.lotNumber,
                        inTable = true
                    )
                )
                immunizationTable.addCell(
                    createLabelValueCell(
                        "Manufacturer:",
                        immunization.manufacturer,
                        inTable = true
                    )
                )
                immunizationTable.addCell(
                    createLabelValueCell(
                        "Service Date:",
                        immunization.serviceDatetime?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                immunizationTable.addCell(
                    createLabelValueCell(
                        "Immunization Date:",
                        immunization.immunizationDatetime?.let { formatDateTime(it) },
                        inTable = true
                    )
                )

                document.add(immunizationTable)
            }
        } else {
            val paragraph = Paragraph("No immunizations recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}
