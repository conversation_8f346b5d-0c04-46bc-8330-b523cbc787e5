package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity

@MappedEntity("telephone")
data class TelephoneEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val systemId: Int?,
    val empi: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val type: String?,
    val telephone: String?,
    val systemName: String?,
    val securityLevel: Boolean? = false
)
