import java.util.Properties

plugins {
    id("application")
    id("org.jetbrains.kotlin.jvm")

}

dependencies {
    implementation(kotlin("stdlib-jdk8"))
    implementation("io.micronaut.starter:micronaut-starter-aws-cdk:4.8.3") {
        exclude(group = "software.amazon.awscdk", module = "aws-cdk-lib")
    }
    implementation("software.amazon.awscdk:aws-cdk-lib:2.202.0")
    implementation("software.constructs:constructs:10.4.2")

    // https://github.com/cdklabs/cdk-nag
    implementation("io.github.cdklabs:cdknag:2.36.26")

    // Testing dependencies
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testImplementation("org.junit.jupiter:junit-jupiter-engine")
}

application {
    mainClass = "com.cha.infrastructure.MainKt"
}

tasks.withType<Test> {
    useJUnitPlatform()
}

kotlin {
    jvmToolchain(21)
}

// Function to load .env file
fun loadEnvFile(file: File): Properties {
    val props = Properties()
    if (file.exists()) {
        file.forEachLine { line ->
            val trimmedLine = line.trim()
            // Skip empty lines and comments
            if (trimmedLine.isNotEmpty() && !trimmedLine.startsWith("#")) {
                val parts = trimmedLine.split("=", limit = 2)
                if (parts.size == 2) {
                    val key = parts[0].trim()
                    // Remove potential surrounding quotes from value
                    val value = parts[1].trim().removeSurrounding("\"").removeSurrounding("'")
                    props[key] = value
                }
            }
        }
    } else {
        // Print a warning if the .env file is not found
        println("Warning: .env file not found at ${file.absolutePath}. Environment variables might be missing.")
    }
    return props
}

// Load environment variables from the root project's .env file
val envProps = loadEnvFile(rootProject.projectDir.resolve(".env"))

// Configure the run task to use these properties
tasks.named<JavaExec>("run") {
    // Convert Properties to Map<String, Any> for systemProperties
    val systemPropsMap = envProps.map { (key, value) -> key.toString() to value }.toMap()
    if (systemPropsMap.isNotEmpty()) {
        println("Applying system properties from .env file to :infrastructure:run task")
        systemProperties(systemPropsMap)

        // Also set as environment variables
        environment(systemPropsMap)
    }
}


