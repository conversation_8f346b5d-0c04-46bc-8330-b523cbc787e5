package com.cha.infrastructure

//class ViewerAppStackTest {
//    @Test
//    fun testAppStack() {
//        if (File(functionPath()).exists()) {
//            val stack = ViewerAppStack(App(), "TestMicronautAppStack", StackProps.builder().build(), "test")
//            val template = Template.fromStack(stack)
//            template.hasResourceProperties(
//                "AWS::Lambda::Function",
//                Collections.singletonMap(
//                    "Handler",
//                    "io.micronaut.function.aws.proxy.payload1.ApiGatewayProxyRequestEventFunction"
//                )
//            )
//        }
//    }
//}