package com.cha.model.dto

import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.util.Currency
import java.util.Locale

@Serdeable
@Schema(description = "Monetary amount with currency information")
data class Money(
    @Schema(description = "Amount in cents to avoid floating point precision issues", example = "15099")
    private val amountInCents: Long, 
    @Schema(description = "Currency information")
    val currency: Currency
) : Comparable<Money> {

    // Overload constructors for convenience
    constructor(amount: Double, currency: Currency) : this((amount * 100).toLong(), currency)
    constructor(amount: Int, currency: Currency) : this(amount * 100L, currency)

    val amount: Double
        get() = amountInCents / 100.0

    // Operator overloads for intuitive arithmetic
    operator fun plus(other: Money): Money {
        require(this.currency == other.currency) { "Cannot add money with different currencies." }
        return Money(this.amountInCents + other.amountInCents, this.currency)
    }

    operator fun minus(other: Money): Money {
        require(this.currency == other.currency) { "Cannot subtract money with different currencies." }
        return Money(this.amountInCents - other.amountInCents, this.currency)
    }

    operator fun times(factor: Double): Money {
        return Money((this.amountInCents * factor).toLong(), this.currency)
    }

    operator fun div(divisor: Double): Money {
        require(divisor != 0.0) { "Cannot divide by zero." }
        return Money((this.amountInCents / divisor).toLong(), this.currency)
    }

    override fun compareTo(other: Money): Int {
        require(this.currency == other.currency) { "Cannot compare money with different currencies." }
        return this.amountInCents.compareTo(other.amountInCents)
    }

    override fun toString(): String {
        return "${currency.getSymbol(Locale.getDefault())}%.2f".format(amount)
    }
}
