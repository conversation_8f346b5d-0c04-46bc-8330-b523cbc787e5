package com.cha.model.entity

import com.cha.model.dto.JobStatus
import com.cha.model.dto.ReportType
import io.micronaut.data.annotation.DateCreated
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import io.micronaut.data.annotation.TypeDef
import io.micronaut.data.model.DataType
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.time.LocalDateTime
import java.util.UUID

/**
 * Represents a background job for generating a report. This entity tracks
 * the status and progress of the generation task.
 */
@MappedEntity("jobs") // More accurate table name
data class JobEntity(
    @field:Id
    val id: String = UUID.randomUUID().toString(),

    val userId: String,

    @Enumerated(value = EnumType.STRING)
    var status: JobStatus,

    @Enumerated(value = EnumType.STRING)
    val reportType: ReportType,

    var progress: Int,

    @DateCreated
    val createdAt: LocalDateTime = LocalDateTime.now(),

    var completedAt: LocalDateTime?,
    var errorMessage: String?,

    // A nullable foreign key to the 'reports' table.
    // This will be populated when the job successfully completes.
    var reportId: String?,

    @field:TypeDef(type = DataType.JSON)
    val parameters: Map<String, Any>,

    var lastMessage: String?
)
