package com.cha.model.dto

import com.cha.model.entity.ImmunizationEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a clinical immunization record for a patient, including vaccine, manufacturer, lot, and administration dates."
)
data class Immunization(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int?,
    @Schema(description = "System identifier for the immunization record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the immunization record.")
    val accountNumber: String?,
    @Schema(description = "Name of the immunization or vaccine administered.")
    val immunization: String?,
    @Schema(description = "Lot number of the vaccine.")
    val lotNumber: String?,
    @Schema(description = "Manufacturer of the vaccine.")
    val manufacturer: String?,
    @Schema(description = "Date and time when the service was provided.")
    val serviceDatetime: LocalDateTime?,
    @Schema(description = "Date and time when the immunization was administered.")
    val immunizationDatetime: LocalDateTime?,
    @Schema(description = "Name of the system/source where the immunization was recorded.")
    val systemName: String?,
    @Schema(description = "Indicates if the immunization record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean?,
    @Schema(description = "Admission date associated with the immunization record, if relevant.")
    val admitDate: LocalDateTime?
)

fun ImmunizationEntity.toDto(): Immunization {
    return Immunization(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        immunization = this.immunization,
        lotNumber = this.lotNumber,
        manufacturer = this.manufacturer,
        serviceDatetime = this.serviceDatetime,
        immunizationDatetime = this.immunizationDatetime,
        systemName = this.systemName,
        securityLevel = this.securityLevel,
        admitDate = this.admitDate
    )
}
