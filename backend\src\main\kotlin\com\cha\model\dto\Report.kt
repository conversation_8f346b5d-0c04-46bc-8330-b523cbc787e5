package com.cha.model.dto

import com.cha.model.entity.ReportEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

/** Represents a generated report artifact. */
@Serdeable
@Schema(name = "Report", description = "Represents a generated report artifact.")
data class Report(
    @Schema(description = "Unique identifier for the report.", example = "report-xyz-789", readOnly = true)
    val id: String,

    @Schema(
        description = "Optional, user-defined title for the report.",
        example = "Q3 Sales Performance",
        nullable = true
    )
    val title: String?,

    @Schema(
        description = "Storage key for the report file (e.g., path in an S3 bucket).",
        example = "reports/q3-sales-performance.pdf",
        readOnly = true
    )
    val storageKey: String,

    @Schema(description = "Format of the report file.", example = "PDF", readOnly = true)
    val fileFormat: String,

    @Schema(description = "Size of the generated report file in bytes.", example = "1048576", readOnly = true)
    val fileSize: Long,

    @Schema(
        description = "Timestamp when the report was successfully generated.",
        type = "string",
        format = "date-time",
        example = "2023-10-27T10:15:30Z",
        readOnly = true
    )
    val createdAt: LocalDateTime,

    @Schema(description = "EMPI (Enterprise Master Patient Index) associated with the report.", example = "12345")
    val empi: Int,

    @Schema(description = "Visit ID associated with the report, if applicable.", example = "visit-678", nullable = true)
    val visitId: String?,

    @Schema(
        description = "MRN (Medical Record Number) associated with the report, if applicable.",
        example = "MRN54321",
        nullable = true
    )
    val mrn: String?
)

/** Converts this entity to a DTO. */
fun ReportEntity.toDto(): Report {
    return Report(
        id = this.id,
        title = this.title,
        storageKey = this.storageKey,
        fileFormat = this.fileFormat,
        fileSize = this.fileSize,
        createdAt = this.createdAt,
        empi = this.empi,
        visitId = this.visitId,
        mrn = this.mrn
    )
}