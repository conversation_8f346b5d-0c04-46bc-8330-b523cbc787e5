package com.cha.model.dto

import com.cha.model.entity.VisitEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a patient visit, including admission, discharge, and related details."
)
data class Visit(
    @Schema(description = "Information about the EHR system this visit is associated with.")
    val system: System,
    @Schema(description = "Enterprise Master Patient Index", example = "1001")
    val empi: Int,
    @Schema(description = "Account number associated with the visit", example = "ACC123456")
    val accountNumber: String?,
    @Schema(
        description = "Date and time of admission",
        type = "string",
        format = "date-time",
        example = "2023-01-01T08:00:00"
    )
    val admitDate: LocalDateTime?,
    @Schema(
        description = "Date and time of discharge",
        type = "string",
        format = "date-time",
        example = "2023-01-05T14:00:00"
    )
    val dischargeDate: LocalDateTime?,
    @Schema(description = "Status of the visit", example = "Completed")
    val visitStatus: String?,
    @Schema(description = "Category of the patient", example = "Inpatient")
    val patientCategory: String?,
    @Schema(description = "Source of admission", example = "Emergency")
    val admitSource: String?,
    @Schema(description = "Type of admission", example = "Elective")
    val admitType: String?,
    @Schema(description = "Service under which the patient was admitted", example = "Cardiology")
    val admitService: String?,
    @Schema(description = "Admission diagnosis", example = "Chest Pain")
    val admDiag: String?,
    @Schema(description = "Service under which the patient was discharged", example = "General Medicine")
    val dischargeService: String?,
    @Schema(description = "Status at discharge", example = "Recovered")
    val dischargeStatus: String?,
    @Schema(description = "Financial class of the patient", example = "Self-Pay")
    val financialClass: String?,
    @Schema(description = "Type of patient", example = "Adult")
    val patientType: String?,
    @Schema(description = "Home telephone number", example = "************")
    val homeTelephone: String?,
    @Schema(description = "Mobile telephone number", example = "************")
    val mobileTelephone: String?,
    @Schema(description = "Type of visit", example = "Consultation")
    val visitType: String?,
    @Schema(description = "Reason for the visit", example = "Routine checkup")
    val reason: String?,
    @Schema(description = "Additional notes about the visit", example = "Patient requested early discharge")
    val notes: String?,
    @Schema(description = "Visit number", example = "2")
    val visitNumber: Int?,
    @Schema(description = "Provider responsible for the visit", example = "Dr. Smith")
    val provider: String?,
    @Schema(description = "Security level for the visit", example = "1")
    val securityLevel: Boolean?,
)

fun VisitEntity.toDto(): Visit {

    val system = System(
        id = systemId,
        name = systemName ?: throw IllegalArgumentException("System name cannot be null"),
        mrn = mrn,
    )

    return Visit(
        system = system,
        empi = empi,
        accountNumber = accountNumber,
        admitDate = admitDate,
        dischargeDate = dischargeDate,
        visitStatus = visitStatus,
        patientCategory = patientCategory,
        admitSource = admitSource,
        admitType = admitType,
        admitService = admitService,
        admDiag = admDiag,
        dischargeService = dischargeService,
        dischargeStatus = dischargeStatus,
        financialClass = financialClass,
        patientType = patientType,
        homeTelephone = homeTelephone,
        mobileTelephone = mobileTelephone,
        visitType = visitType,
        reason = reason,
        notes = notes,
        visitNumber = visitNumber,
        provider = provider,
        securityLevel = securityLevel
    )
}
