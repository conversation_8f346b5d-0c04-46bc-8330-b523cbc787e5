package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("micro_detail")
data class MicroDetailEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val orderCode: String?,
    val orderCodeDescription: String?,
    val accessionNumber: String?,
    val accessionIsolate: String?,
    val resultTypeCode: String?,
    val isolateNumber: Int?,
    val resultSequenceNumber: Int?,
    val resultText: String?,
    val releasedBy: String?,
    val releaseDate: LocalDateTime?,
    val resultType: String?,
    val systemName: String?,
    val securityLevel: Boolean? = false,
    val admitDate: LocalDateTime?,
)
