package com.cha.model.dto

import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema

/** Represents a progress update from a background job. */
@Serdeable
@Schema(description = "Progress update from a background job")
data class JobProgress(
    @Schema(description = "Progress as a percentage", example = "75.5", minimum = "0.0", maximum = "100.0")
    val progress: Float, // Progress as a percentage (0.0 to 100.0)
    @Schema(description = "Current step message", example = "Processing patient data...")
    val message: String, // Current step message
    @Schema(description = "Overall job status")
    val status: JobStatus, // Overall job status
    @Schema(description = "Report ID if completed", example = "RPT123456", nullable = true)
    val reportId: String? = null, // reportId if completed
    @Schema(description = "Error message if failed", example = "Database connection timeout", nullable = true)
    val errorMessage: String? = null, // Error message if failed
)
