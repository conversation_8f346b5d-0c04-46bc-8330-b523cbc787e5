package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.Rectangle
import com.lowagie.text.pdf.PdfPCell
import com.lowagie.text.pdf.PdfPTable

private fun addMicroCommentsSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.microComments?.let { microComments ->
        document.add(createSectionTitle("Microbiology Comments"))
        if (microComments.isNotEmpty()) {
            microComments.forEach { comment ->
                val commentTable = PdfPTable(2)
                commentTable.widthPercentage = 100f
                setupDataItemTable(commentTable)

                val headerText = "${comment.commentType ?: "Comment"} (Seq: ${comment.sequenceNumber ?: "N/A"})"
                commentTable.addCell(createDataItemHeaderCell(headerText, 2))

                // Create a cell for the comment description that spans both columns
                val descriptionCell = PdfPCell(Paragraph(comment.commentDescription ?: "N/A", FONT_BODY))
                descriptionCell.colspan = 2
                descriptionCell.border = Rectangle.NO_BORDER
                descriptionCell.setPadding(5f)
                commentTable.addCell(descriptionCell)

                commentTable.addCell(createLabelValueCell("Accession #:", comment.accessionNumber, inTable = true))
                commentTable.addCell(createLabelValueCell("Isolate:", comment.accessionIsolate, inTable = true))
                commentTable.addCell(createLabelValueCell("Entered By:", comment.enteredBy, inTable = true))
                commentTable.addCell(
                    createLabelValueCell(
                        "Entered Date:",
                        comment.enteredDate?.let { formatDateTime(it) },
                        inTable = true
                    )
                )

                document.add(commentTable)
            }
        } else {
            val paragraph = Paragraph("No microbiology comments recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}