package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("lab_results")
data class LabResultEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val accession: String?,
    val orderCode: String?,
    val orderDescription: String?,
    val sequence: Short?,
    val testAbbr: String?,
    val testDescription: String?,
    val numericLabResult: String?,
    val alphaLabResult: String?,
    val units: String?,
    val status: String?,
    val resultFlag: String?,
    val abnFlag: String?,
    val releasedBy: String?,
    val empi: Int?,
    val serviceDatetime: LocalDateTime?,
    val collectedDatetime: LocalDateTime?,
    val receivedDatetime: LocalDateTime?,
    val releasedDatetime: LocalDateTime?,
    val systemName: String?,
    val securityLevel: Boolean? = false,
)
