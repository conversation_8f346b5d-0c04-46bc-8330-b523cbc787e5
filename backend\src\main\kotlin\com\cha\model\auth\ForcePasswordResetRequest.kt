package com.cha.model.auth

import io.micronaut.core.annotation.Introspected
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank

@Introspected
@Serdeable
@Schema(name = "ForcePasswordResetRequest", description = "Force password reset request")
data class ForcePasswordResetRequest(
    @field:NotBlank
    @Schema(description = "Username of the user to reset password for", example = "johndoe")
    val username: String,
    @Schema(description = "Send reset notification to user", example = "true")
    val sendNotification: Boolean = true
)