package com.cha.domain.exception

/**
 * Exception thrown when an unexpected error occurs in the server. This
 * will typically be mapped to a 500 Internal Server Error HTTP response.
 */
class InternalServerException(
    message: String = "An unexpected error occurred",
    val errorCode: String? = null,
    val traceId: String? = null,
    cause: Throwable? = null,
) : RuntimeException(message, cause)
