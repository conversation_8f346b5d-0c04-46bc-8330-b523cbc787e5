package com.cha.domain

import aws.sdk.kotlin.services.s3.S3Client
import aws.sdk.kotlin.services.s3.model.PutObjectRequest
import aws.smithy.kotlin.runtime.content.ByteStream
import aws.smithy.kotlin.runtime.content.fromFile
import com.cha.model.dto.Alert
import com.cha.model.dto.Allergy
import com.cha.model.dto.Bill
import com.cha.model.dto.DateRangeFilter
import com.cha.model.dto.Diagnosis
import com.cha.model.dto.Document
import com.cha.model.dto.HomeMedication
import com.cha.model.dto.Immunization
import com.cha.model.dto.Insurance
import com.cha.model.dto.Invoice
import com.cha.model.dto.InvoiceNote
import com.cha.model.dto.JobProgress
import com.cha.model.dto.JobStatus
import com.cha.model.dto.LabResult
import com.cha.model.dto.LabResultReference
import com.cha.model.dto.Medication
import com.cha.model.dto.MicroComment
import com.cha.model.dto.MicroDetail
import com.cha.model.dto.MicroSuscept
import com.cha.model.dto.Notes
import com.cha.model.dto.Orders
import com.cha.model.dto.Procedure
import com.cha.model.dto.ReportSections
import com.cha.model.dto.ReportType
import com.cha.model.dto.TranscriptionResult
import com.cha.model.dto.Visit
import com.cha.model.entity.ReportEntity
import com.cha.model.report.ClinicalViewModel
import com.cha.model.report.DocumentsMetadataViewModel
import com.cha.model.report.FinancialViewModel
import com.cha.model.report.HospitalInfoViewModel
import com.cha.model.report.PatientInfoViewModel
import com.cha.model.report.ReportViewModel
import com.cha.model.report.RequestUserViewModel
import com.cha.model.report.VisitClinicalData
import com.cha.model.report.VisitFinancialData
import com.cha.repository.JobRepository
import com.cha.repository.ReportRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.context.annotation.Value
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.http.MediaType
import io.micronaut.multitenancy.tenantresolver.TenantResolver
import jakarta.inject.Singleton
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.io.File
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID

private val log = KotlinLogging.logger {}

// TODO scope this class to a single report generation lifecycle.
@Singleton
class ReportsService(
    private val authService: AuthService,
    private val visitService: PatientVisitService,
    private val patientService: PatientService,
    private val clinicalService: ClinicalService,
    private val financialService: FinancialService,
    private val documentsService: DocumentsService,
    private val hospitalService: HospitalService,
    private val s3Client: S3Client,
    @Value("\${aws.s3.bucket}") private val bucketName: String,
    @Value("\${aws.s3.prefixes.viewer-generated-reports}") private val viewerGeneratedDocsPrefix: String,
    private val tenantResolver: TenantResolver,
    private val reportPdfService: ReportPdfService,
    private val jobRepository: JobRepository,
    private val reportRepository: ReportRepository,
) {

    fun generateReport(
        jobId: String,
        empi: Int,
        mrn: String,
        visitId: String?,
        reportType: ReportType = ReportType.VISIT_SUMMARY,
        includeSections: Set<ReportSections> = ReportSections.entries.toSet(),
        dateRange: DateRangeFilter?,
        fileTitle: String,
    ): Flow<JobProgress> = flow {
        // Get the job entity
        val jobEntity = jobRepository.findById(jobId)
            ?: throw IllegalArgumentException("Job not found with ID: $jobId")

        // Initial progress update
        emit(
            JobProgress(
                progress = 0f,
                message = "Starting report generation",
                status = JobStatus.PROCESSING
            )
        )

        // Validate inputs based on report type
        require(includeSections.isNotEmpty()) {
            "At least one report section must be included"
        }

        if (reportType == ReportType.VISIT_SUMMARY) {
            if (visitId.isNullOrBlank()) {
                throw IllegalArgumentException("Visit ID must be provided for a visit summary report")
            }
            if (dateRange != null) {
                throw IllegalArgumentException("Date range should not be provided for a visit summary report")
            }
        }

        emit(
            JobProgress(
                progress = 3f,
                message = "Fetching patient information",
                status = JobStatus.PROCESSING
            )
        )

        // Fetch the full user profile using the ID from the token
        val currentUser = authService.getUser(jobEntity.parameters["requesterUsername"] as? String ?: "")
        // TODO: Add auth role final check to ensure they have permission to generate document here.

        // Fetch patient info
        val patientInfo = fetchPatientInfo(currentUser.id, empi)

        emit(
            JobProgress(
                progress = 7f,
                message = "Fetching hospital information",
                status = JobStatus.PROCESSING
            )
        )

        // Fetch hospital info
        val hospitalInfo = fetchHospitalInfo()

        emit(
            JobProgress(
                progress = 10f,
                message = "Searching for visits",
                status = JobStatus.PROCESSING
            )
        )

        val visits = getVisits(empi, mrn, visitId, reportType, dateRange)

        emit(
            JobProgress(
                progress = 15f,
                message = "Collecting clinical data",
                status = JobStatus.PROCESSING
            )
        )

        // Process multiple visits for MRN reports or just one for visit summary
        val visitsToProcess = when (reportType) {
            ReportType.VISIT_SUMMARY -> listOf(visitId!!)
            else -> visits.map { it.accountNumber }
        }

        val progressIncrement = 76f / (visitsToProcess.size * 2)

        val allClinicalData = mutableListOf<VisitClinicalData>()
        val allFinancialData = mutableListOf<VisitFinancialData>()
        visitsToProcess.forEachIndexed { index, currentVisitId ->
            emit(
                JobProgress(
                    progress = 38f + (index * progressIncrement),
                    message = "Collecting clinical data for visit ${index + 1}/${visitsToProcess.size}",
                    status = JobStatus.PROCESSING
                )
            )

            val clinicalViewModel = collectClinicalData(empi, mrn, currentVisitId, includeSections)
            allClinicalData.add(VisitClinicalData(currentVisitId ?: "", clinicalViewModel))

            emit(
                JobProgress(
                    progress = 38f + (index * progressIncrement) + (progressIncrement / 2),
                    message = "Collecting financial data for visit ${index + 1}/${visitsToProcess.size}",
                    status = JobStatus.PROCESSING
                )
            )

            val financialViewModel = collectFinancialData(empi, mrn, currentVisitId, includeSections)
            allFinancialData.add(VisitFinancialData(currentVisitId ?: "", financialViewModel))
        }

        val documentsMetadata = getDocumentMetadata(empi, mrn, visitId, includeSections)

        emit(
            JobProgress(
                progress = 91f,
                message = "Building report model",
                status = JobStatus.PROCESSING
            )
        )

        val model = ReportViewModel(
            hospital = hospitalInfo,
            requestUser = RequestUserViewModel(
                id = currentUser.id,
                username = currentUser.username,
                firstName = currentUser.firstName,
                lastName = currentUser.lastName,
                email = currentUser.email,
                role = currentUser.roles.joinToString(", ")
            ),
            patient = patientInfo,
            generatedDateTime = ZonedDateTime.now(ZoneOffset.UTC),
            system = emptyList(), // TODO provide the system information.
            visits = visits,
            allClinicalData = allClinicalData,
            allFinancialData = allFinancialData,
            documentsMetadata = documentsMetadata,
            reportTitle = fileTitle,
            reportType = reportType
        )

        emit(
            JobProgress(
                progress = 93f,
                message = "Generating PDF document",
                status = JobStatus.PROCESSING
            )
        )

        // Generate the PDF as a Flow of ByteBuffer chunks
        val pdfFlow = reportPdfService.generateReportPdf(model)

        emit(
            JobProgress(
                progress = 95f,
                message = "Saving report file",
                status = JobStatus.PROCESSING
            )
        )

        // Create a temporary file to save the PDF
        val timestamp = ZonedDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))
        val pdfFile = File.createTempFile("$empi-${reportType.name.lowercase()}-$timestamp", ".pdf")
        pdfFile.deleteOnExit() // Ensure cleanup even if JVM terminates unexpectedly

        pdfFile.outputStream().use { fileOutputStream ->
            // Collect the flow and write chunks to the file
            pdfFlow.collect { buffer ->
                val bytes = ByteArray(buffer.remaining())
                buffer.get(bytes)
                fileOutputStream.write(bytes)
            }
        }

        emit(
            JobProgress(
                progress = 95f,
                message = "Uploading report",
                status = JobStatus.PROCESSING
            )
        )

        // Upload to S3 bucket and get document key for retrieval
        val tenant = tenantResolver.resolveTenantId()
        val documentKey = generateUniqueReportFilename(reportType, empi)
        val s3ObjectKey = "$viewerGeneratedDocsPrefix/$tenant/$documentKey"

        try {
            log.debug { "Uploading PDF to S3 bucket: $bucketName, key: $s3ObjectKey" }

            s3Client.putObject(PutObjectRequest {
                bucket = bucketName
                key = s3ObjectKey
                contentType = MediaType.APPLICATION_PDF
                contentLength = pdfFile.length()
                // Add additional metadata
                metadata = mapOf(
                    "empi" to empi.toString(),
                    "report-type" to reportType.name,
                    "generated-timestamp" to timestamp
                )
                body = ByteStream.fromFile(pdfFile)
            })
            log.info { "Successfully uploaded report PDF to S3: $s3ObjectKey" }

            val reportEntity = ReportEntity(
                title = fileTitle,
                storageKey = documentKey,
                fileFormat = "PDF",
                fileSize = pdfFile.length(),
                empi = empi,
                visitId = visitId,
                mrn = mrn,
                jobId = jobEntity.id,
                userId = jobEntity.userId,
            )

            // Clean up the temporary file
            pdfFile.delete()

            val savedReport = reportRepository.save(reportEntity)

            // Final progress update with completed status
            emit(
                JobProgress(
                    progress = 100f,
                    message = "Report generation completed",
                    status = JobStatus.COMPLETED,
                    reportId = savedReport.id,
                )
            )

            log.info { "Generated report PDF and uploaded to S3 with document key: $documentKey" }
        } catch (e: Exception) {
            log.error(e) { "Failed to upload report PDF to S3: $s3ObjectKey" }

            emit(
                JobProgress(
                    progress = 97f,
                    message = "Report generation failed",
                    status = JobStatus.FAILED,
                    errorMessage = "Failed to upload report to S3",
                )
            )

            // Clean up the temporary file even on error
            pdfFile.delete()
        }
    }

    private suspend fun collectFinancialData(
        empi: Int,
        mrn: String,
        visitId: String?,
        includeSections: Set<ReportSections>,
    ): FinancialViewModel {

        val insuranceResults = includeSections
            .takeIf { ReportSections.INSURANCE in it }
            ?.let {
                fetchAllPaginatedData<Insurance>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    financialService.getPatientInsurance(empi, mrn, visitId, pageable)
                }
            }

        val billsResults = includeSections
            .takeIf { ReportSections.BILLS in it }
            ?.let {
                fetchAllPaginatedData<Bill>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    financialService.getPatientBills(empi, mrn, visitId, pageable)
                }
            }

        val invoiceNotesResults = includeSections
            .takeIf { ReportSections.INVOICE_NOTES in it }
            ?.let {
                fetchAllPaginatedData<InvoiceNote>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    financialService.getPatientInvoiceNote(empi, mrn, visitId, pageable)
                }
            }

        val invoicesResults = includeSections
            .takeIf { ReportSections.INVOICES in it }
            ?.let {
                fetchAllPaginatedData<Invoice>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    financialService.getPatientInvoice(empi, mrn, visitId, pageable)
                }
            }

        return FinancialViewModel(
            insurance = insuranceResults,
            bills = billsResults,
            invoiceNotes = invoiceNotesResults,
            invoices = invoicesResults
        )
    }

    private suspend fun collectClinicalData(
        empi: Int,
        mrn: String,
        visitId: String?,
        includeSections: Set<ReportSections>,
    ): ClinicalViewModel {
        // TODO can offload request to a separate IO thread pool if this becomes a bottleneck.
        val allergyResults = includeSections
            .takeIf { ReportSections.ALLERGIES in it }
            ?.let {
                fetchAllPaginatedData<Allergy>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientAllergies(empi, mrn, visitId, pageable)
                }
            }

        val labResults = includeSections
            .takeIf { ReportSections.LABORATORY in it }
            ?.let {
                fetchAllPaginatedData<LabResult>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientLabResults(empi, mrn, visitId, pageable)
                }
            }

        val medicationResults = includeSections
            .takeIf { ReportSections.MEDICATIONS in it }
            ?.let {
                fetchAllPaginatedData<Medication>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientMedications(empi, mrn, visitId, pageable)
                }
            }

        val ordersResults = includeSections
            .takeIf { ReportSections.ORDERS in it }
            ?.let {
                fetchAllPaginatedData<Orders>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientOrders(empi, mrn, visitId, pageable)
                }
            }

        val immunizationResults = includeSections
            .takeIf { ReportSections.IMMUNIZATIONS in it }
            ?.let {
                fetchAllPaginatedData<Immunization>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientImmunizations(empi, mrn, visitId, pageable)
                }
            }

        val procedureResults = includeSections
            .takeIf { ReportSections.PROCEDURES in it }
            ?.let {
                fetchAllPaginatedData<Procedure>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientProcedure(empi, mrn, visitId, pageable)
                }
            }

        val alertsResults = includeSections
            .takeIf { ReportSections.NOTES in it }
            ?.let {
                fetchAllPaginatedData<Alert>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientAlerts(empi, mrn, visitId, pageable)
                }
            }

        val diagnosisResults = includeSections
            .takeIf { ReportSections.DIAGNOSES in it }
            ?.let {
                fetchAllPaginatedData<Diagnosis>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientDiagnosis(empi, mrn, visitId, pageable)
                }
            }

        val notesResults = includeSections
            .takeIf { ReportSections.NOTES in it }
            ?.let {
                fetchAllPaginatedData<Notes>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientNotes(empi, mrn, visitId, pageable)
                }
            }

        val microDetailResults = includeSections
            .takeIf { ReportSections.LABORATORY in it }
            ?.let {
                fetchAllPaginatedData<MicroDetail>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientMicroDetail(empi, mrn, visitId, pageable)
                }
            }

        val microSusceptResults = includeSections
            .takeIf { ReportSections.LABORATORY in it }
            ?.let {
                fetchAllPaginatedData<MicroSuscept>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientMicroSuscept(empi, mrn, visitId, pageable)
                }
            }

        val microCommentResults = includeSections
            .takeIf { ReportSections.LABORATORY in it }
            ?.let {
                fetchAllPaginatedData<MicroComment>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientMicroComment(empi, mrn, visitId, pageable)
                }
            }

        val homeMedicationResults = includeSections
            .takeIf { ReportSections.HOME_MEDICATIONS in it }
            ?.let {
                fetchAllPaginatedData<HomeMedication>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientHomeMedications(empi, mrn, visitId, pageable)
                }
            }

        val labResultReferenceResults = includeSections
            .takeIf { ReportSections.LABORATORY in it }
            ?.let {
                fetchAllPaginatedData<LabResultReference>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientReferenceLabResult(empi, mrn, visitId, pageable)
                }
            }

        val transcriptionResultResults = includeSections
            .takeIf { ReportSections.TRANSCRIPTIONS in it }
            ?.let {
                fetchAllPaginatedData<TranscriptionResult>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
                    clinicalService.getPatientTranscriptionResults(empi, mrn, visitId, pageable)
                }
            }

        return ClinicalViewModel(
            allergies = allergyResults,
            medications = medicationResults,
            orders = ordersResults,
            labResults = labResults,
            immunizations = immunizationResults,
            procedures = procedureResults,
            alerts = alertsResults,
            diagnoses = diagnosisResults,
            notes = notesResults,
            homeMedications = homeMedicationResults,
            labResultReferences = labResultReferenceResults,
            microComments = microCommentResults,
            microSuscept = microSusceptResults,
            microDetail = microDetailResults,
            transcriptionResults = transcriptionResultResults
        )
    }

    private suspend fun fetchPatientInfo(userId: String, empi: Int): PatientInfoViewModel {
        log.debug { "Fetching information for patient $empi" }

        // TODO determine which system to pull the information for.
        val patient = patientService.getPatientInfo(empi)
        return PatientInfoViewModel(
            empi = empi,
            firstName = patient.firstName,
            lastName = patient.lastName,
            middleName = patient.middleName,
            dob = patient.birthdate.toString(),
            sex = patient.sex,
            phone = patient.telephones,
            address = patient.addresses
        )
    }

    private suspend fun fetchVisitInfo(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        dateRange: DateRangeFilter?,
    ): List<Visit> {
        val visits = fetchAllPaginatedData<Visit>(empi, mrn, accountNumber) { empi, mrn, visitId, pageable ->
            if (dateRange != null) {
                visitService.getVisits(empi, mrn, visitId, dateRange.startDate, dateRange.endDate, pageable)
            } else
                visitService.getVisits(empi, mrn, visitId, pageable)
        }

        // Sort visits by admit date in descending order (most recent first)
        return visits.sortedByDescending { it.admitDate }
    }

    private suspend fun fetchHospitalInfo(): HospitalInfoViewModel {
        log.debug { "Fetching hospital information" }
        val hospital = hospitalService.fetchHospitalInfo()

        return HospitalInfoViewModel(
            name = hospital.name,
            address = hospital.address,
            telephone = hospital.telephone,
            fax = hospital.fax,
            website = hospital.website,
            logoUrl = hospital.logoUrl,
            timezone = hospital.timezone, // TODO: format generated time and others to hospital timezone.
        )
    }

    /**
     * Generic function to fetch paginated data. The entity name for logging is
     * derived from the class name of T.
     *
     * @param T The type of the data items (must be a non-null type).
     * @param empi Patient identifier.
     * @param visitId Optional visit identifier.
     * @param pageSize The number of items to fetch per page.
     * @param fetchPageAction A suspending lambda function that takes (empi,
     *    visitId, pageable) and returns a Page<T>.
     * @return A list containing all fetched items.
     */
    private suspend inline fun <reified T : Any> fetchAllPaginatedData(
        empi: Int,
        mrn: String?,
        visitId: String?,
        pageSize: Int = 100, // Default page size, can be overridden
        fetchPageAction: suspend (empi: Int, mrn: String?, visitId: String?, pageable: Pageable) -> Page<T>,
    ): List<T> {
        // Derive entity name from the reified type T for logging
        val entityDisplayName = T::class.simpleName ?: "entities"

        log.debug { "Fetching $entityDisplayName for patient $empi, visit $visitId" }

        val allResults = mutableListOf<T>()
        var currentPage = 0
        var hasMoreData: Boolean

        do {
            val pageable = Pageable.from(currentPage, pageSize)
            val resultPage = fetchPageAction(empi, mrn, visitId, pageable)
            allResults.addAll(resultPage.content)

            currentPage++
            hasMoreData = !resultPage.isEmpty && resultPage.hasNext()
        } while (hasMoreData)

        log.info { "Fetched ${allResults.size} $entityDisplayName for patient $empi, visit $visitId" }
        return allResults
    }

    /**
     * Generates a unique filename for a report that includes the report type
     * and patient EMPI. Format: {reportType}_{empi}_{timestamp}_{uuid}.pdf
     *
     * @param reportType The type of report being generated
     * @param empi The patient EMPI
     * @return A unique filename for the report
     */
    private fun generateUniqueReportFilename(reportType: ReportType, empi: Int): String {
        val timestamp = ZonedDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))
        val uuid = UUID.randomUUID().toString().substring(0, 8) // Use first 8 characters of UUID for brevity
        return "${empi}_${reportType.name.lowercase()}_${timestamp}_${uuid}.pdf"
    }

    private fun generateReportTitle(reportType: ReportType, empi: Int): String {
        return when (reportType) {
            ReportType.VISIT_SUMMARY -> "Visit Summary - $empi"
            ReportType.MRN -> "MRN Report - $empi"
            ReportType.ROI -> "Release of Information Report - $empi"
        }
    }

    private suspend fun getVisits(
        empi: Int,
        mrn: String,
        visitId: String?,
        reportType: ReportType,
        dateRange: DateRangeFilter?,
    ): List<Visit> {
        return when (reportType) {
            ReportType.VISIT_SUMMARY -> {
                // For visit summary, we need the specific visit
                val visitList = fetchVisitInfo(empi, mrn, visitId!!, null)
                if (visitList.isEmpty()) {
                    throw IllegalArgumentException("No visit found with the provided visit ID and system MRN.")
                }
                visitList
            }

            ReportType.MRN -> {
                // For MRN reports, fetch all visits for the patient within date range if provided
                val visitList = fetchVisitInfo(empi, mrn, null, dateRange)
                if (visitList.isEmpty()) {
                    throw IllegalArgumentException("No visits found for the provided MRN")
                }
                visitList
            }

            ReportType.ROI -> {
                // For ROI reports, we only use empi and dateRange, not visitId or MRN
                val visitList = fetchVisitInfo(empi, null, null, dateRange)
                if (visitList.isEmpty()) {
                    throw IllegalArgumentException("No visits found for the provided release of information request")
                }
                visitList
            }
        }
    }

    /**
     * Will retrieve metadata for both visit documents and unassigned
     * documents. We then partition the results based on whether part of a
     * visit or not. If the user didn't request unassigned or visit docs, we
     * just return an empty [DocumentsMetadataViewModel].
     */
    private suspend fun getDocumentMetadata(
        empi: Int,
        mrn: String,
        visitId: String?,
        includeSections: Set<ReportSections>,
    ): DocumentsMetadataViewModel {
        if (ReportSections.UNASSIGNED_DOCUMENTS !in includeSections && ReportSections.DOCUMENTS !in includeSections) {
            return DocumentsMetadataViewModel(null, null)
        }
        val allDocuments = fetchAllPaginatedData<Document>(empi, mrn, visitId) { empi, mrn, visitId, pageable ->
            documentsService.getPatientDocuments(empi, mrn, visitId, pageable)
        }
        val (unassigned, visit) = allDocuments.partition { it.accountNumber.isNullOrEmpty() }
        return DocumentsMetadataViewModel(unassignedDocuments = unassigned, visitDocuments = visit)
    }
}
