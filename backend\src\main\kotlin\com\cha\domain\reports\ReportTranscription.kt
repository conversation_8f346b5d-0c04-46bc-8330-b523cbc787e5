package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.Rectangle
import com.lowagie.text.pdf.PdfPCell
import com.lowagie.text.pdf.PdfPTable

private fun addTranscriptionResults(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.transcriptionResults?.let { transcriptionResults ->
        document.add(createSectionTitle("Transcription Results"))
        if (transcriptionResults.isNotEmpty()) {
            transcriptionResults.forEach { result ->
                val resultTable = PdfPTable(2)
                resultTable.widthPercentage = 100f
                setupDataItemTable(resultTable)
                val headerText = result.orderCode ?: "Transcription Result"
                resultTable.addCell(createDataItemHeaderCell(headerText, 2))

                // Create a cell for the result content that spans both columns
                if (!result.transcriptionText.isNullOrBlank()) {
                    val resultTextCell = PdfPCell(Paragraph(result.transcriptionText, FONT_BODY))
                    resultTextCell.colspan = 2
                    resultTextCell.border = Rectangle.NO_BORDER
                    resultTextCell.setPadding(5f)
                    resultTable.addCell(resultTextCell)
                }

                resultTable.addCell(
                    createLabelValueCell(
                        "Order Description:",
                        result.orderDescription,
                        inTable = true
                    )
                )
                resultTable.addCell(createLabelValueCell("Status:", result.status, inTable = true))
                resultTable.addCell(
                    createLabelValueCell(
                        "Reading Caregiver:",
                        result.readingCaregiver,
                        inTable = true
                    )
                )
                resultTable.addCell(
                    createLabelValueCell(
                        "Signing Caregiver:",
                        result.signingCaregiver,
                        inTable = true
                    )
                )
                resultTable.addCell(createLabelValueCell("Cancel Reason:", result.cancelReason, inTable = true))
                resultTable.addCell(
                    createLabelValueCell(
                        "Entered Date:",
                        result.enterDatetime?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                resultTable.addCell(
                    createLabelValueCell(
                        "Reading Date:",
                        result.readDatetime?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                resultTable.addCell(
                    createLabelValueCell(
                        "Sign-off Date:",
                        result.signDatetime?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                document.add(resultTable)
            }
        } else {
            val paragraph = Paragraph("No transcription results recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}