package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("allergies")
data class AllergyEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val severity: String?,
    val allergyStartDate: LocalDateTime?,
    val allergyEndDate: LocalDateTime?,
    val reaction: String?,
    val allergyCode: String?,
    val allergyType: String?,
    val allergyDescription: String?,
    val admitDate: LocalDateTime?,
    val systemName: String?,
    val securityLevel: Boolean? = false
)
