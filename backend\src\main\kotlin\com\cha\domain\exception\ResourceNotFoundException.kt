package com.cha.domain.exception

/**
 * Exception thrown when a requested resource is not found. This will
 * typically be mapped to a 404 Not Found HTTP response.
 */
class ResourceNotFoundException(
    val resourceType: String,
    val identifier: String,
    message: String = "Resource of type '$resourceType' with identifier '$identifier' not found",
    cause: Throwable? = null,
) : RuntimeException(message, cause)
