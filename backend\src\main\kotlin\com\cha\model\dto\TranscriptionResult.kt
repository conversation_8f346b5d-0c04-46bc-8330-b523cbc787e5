package com.cha.model.dto

import com.cha.model.entity.TranscriptionResultEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(description = "Medical transcription result information")
data class TranscriptionResult(
    @Schema(description = "System identifier", example = "1")
    val systemId: Int?,
    @Schema(description = "Patient's Enterprise Master Patient Index", example = "12345")
    val empi: Int?,
    @Schema(description = "Medical Record Number", example = "MRN123456")
    val mrn: String?,
    @Schema(description = "Account number", example = "ACC789012")
    val accountNumber: String?,
    @Schema(description = "Order code", example = "RAD001")
    val orderCode: String?,
    @Schema(description = "Order description", example = "Chest X-Ray AP/Lateral")
    val orderDescription: String?,
    @Schema(description = "Reading caregiver name", example = "<PERSON><PERSON> <PERSON>")
    val readingCaregiver: String?,
    @Schema(description = "Signing caregiver name", example = "<PERSON><PERSON> <PERSON>")
    val signingCaregiver: String?,
    @Schema(description = "Transcription status", example = "Signed")
    val status: String?,
    @Schema(description = "Entry timestamp", example = "2024-01-15T09:00:00")
    val enterDatetime: LocalDateTime?,
    @Schema(description = "Read timestamp", example = "2024-01-15T10:30:00")
    val readDatetime: LocalDateTime?,
    @Schema(description = "Sign timestamp", example = "2024-01-15T11:15:00")
    val signDatetime: LocalDateTime?,
    @Schema(description = "Cancellation reason if applicable", example = "Patient declined procedure")
    val cancelReason: String?,
    @Schema(description = "Transcription text content", example = "Normal chest x-ray. No acute findings.")
    val transcriptionText: String?,
    @Schema(description = "System name where transcription originates", example = "RIS")
    val systemName: String?,
    @Schema(description = "Security level flag", example = "false")
    val securityLevel: Boolean? = false,
    @Schema(description = "Admission date", example = "2024-01-15T08:00:00")
    val admitDate: LocalDateTime?
)

fun TranscriptionResultEntity.toDto(): TranscriptionResult {
    return TranscriptionResult(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        orderCode = this.orderCode,
        orderDescription = this.orderDescription,
        systemName = this.systemName,
        securityLevel = this.securityLevel ?: false,
        admitDate = this.admitDate,
        status = this.status,
        readingCaregiver = this.readingCaregiver,
        signingCaregiver = this.signingCaregiver,
        enterDatetime = this.enterDatetime,
        readDatetime = this.readDatetime,
        signDatetime = this.signDatetime,
        cancelReason = this.cancelReason,
        transcriptionText = this.transcriptionText
    )
}
