package com.cha.repository

import com.cha.model.entity.VisitEntity
import io.micronaut.data.annotation.Query
import io.micronaut.data.jdbc.annotation.JdbcRepository
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.data.model.query.builder.sql.Dialect
import io.micronaut.data.repository.kotlin.CoroutinePageableCrudRepository
import jakarta.validation.Valid
import java.time.LocalDate

@JdbcRepository(dialect = Dialect.SQL_SERVER)
interface VisitsRepository : CoroutinePageableCrudRepository<@Valid VisitEntity, Long> {

    @Query(
        value = """
            SELECT * FROM visits p
            WHERE p.empi = :empi
            AND (:mrn IS NULL OR p.mrn = :mrn)
            AND (:accountNumber IS NULL OR p.account_number = :accountNumber)
        """,
        countQuery = """
            SELECT COUNT(*) FROM visits p
            WHERE p.empi = :empi
            AND (:mrn IS NULL OR p.mrn = :mrn)
            AND (:accountNumber IS NULL OR p.account_number = :accountNumber)
        """,
        nativeQuery = true
    )
    suspend fun findByEmpiAndMrnAndAccountNumber(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        pageable: Pageable,
    ): Page<VisitEntity>

    @Query(
        value = """
    SELECT * FROM visits
    WHERE empi = :empi
      AND (:mrn IS NULL OR mrn = :mrn)
      AND (:accountNumber IS NULL OR account_number = :accountNumber)
      AND (:startDate IS NULL OR admit_date >= :startDate)
      AND (:endDate IS NULL OR admit_date <= :endDate)
    ORDER BY id ASC
    """,
        countQuery = """
    SELECT COUNT(*) FROM visits 
    WHERE empi = :empi
      AND (:mrn IS NULL OR mrn = :mrn)
      AND (:accountNumber IS NULL OR account_number = :accountNumber)
      AND (:startDate IS NULL OR admit_date >= :startDate)
      AND (:endDate IS NULL OR admit_date <= :endDate)
    """
    )
    suspend fun searchWithDateFilter(
        empi: Int,
        mrn: String?,
        accountNumber: String?,
        startDate: LocalDate?,
        endDate: LocalDate?,
        pageable: Pageable,
    ): Page<VisitEntity>
}
