package com.cha.model.entity

import io.micronaut.data.annotation.DateUpdated
import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("user_patient_history")
data class UserPatientHistoryEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Long? = null,
    val userId: String,
    @field:DateUpdated
    val viewedAt: LocalDateTime = LocalDateTime.now(),
    val empi: String,
)
