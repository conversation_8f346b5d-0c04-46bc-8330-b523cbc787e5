package com.cha.repository

import com.cha.model.entity.UserPatientHistoryEntity
import io.micronaut.data.jdbc.annotation.JdbcRepository
import io.micronaut.data.model.query.builder.sql.Dialect
import io.micronaut.data.repository.kotlin.CoroutinePageableCrudRepository
import jakarta.validation.Valid

@JdbcRepository(dialect = Dialect.SQL_SERVER)
interface UserPatientHistoryRepository : CoroutinePageableCrudRepository<@Valid UserPatientHistoryEntity, Long> {

    suspend fun findByUserIdOrderByViewedAtDesc(userId: String): List<UserPatientHistoryEntity>

    suspend fun deleteByUserId(userId: String): Int
}