package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("procedures")
data class ProcedureEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int? = 0,
    val mrn: String?,
    val accountNumber: String?,
    val cptCode: String?,
    val modifier1: String?,
    val modifier2: String?,
    val modifier3: String?,
    val modifier4: String?,
    val cptDescription: String?,
    val caregiver: String?,
    val timestamp: LocalDateTime?,
    val active: Int? = 1,
    val systemName: String?,
    val admitDate: LocalDateTime?,
    val securityLevel: Boolean? = false
)
