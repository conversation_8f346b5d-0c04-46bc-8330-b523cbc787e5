package com.cha.model.dto

import com.cha.model.entity.JobEntity
import com.cha.model.entity.ReportEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime
import java.util.UUID

/** Represents a background job for report generation. */
@Serdeable
@Schema(
    name = "Job",
    description = "Represents a background job for report generation."
)
data class Job(
    @Schema(
        description = "Unique identifier for the job.",
        example = "a1b2c3d4-e5f6-7890-1234-567890abcdef",
        readOnly = true
    )
    val id: String = UUID.randomUUID().toString(),

    @Schema(
        description = "Current status of the job.",
        implementation = JobStatus::class
    )
    val status: JobStatus,

    @Schema(
        description = "Type of report being generated.",
        implementation = ReportType::class,
        example = "VISIT_SUMMARY"
    )
    val reportType: ReportType,

    @Schema(
        description = "Progress percentage of the job (0-100).",
        minimum = "0",
        maximum = "100",
        defaultValue = "0"
    )
    val progress: Int = 0,

    @Schema(
        description = "Timestamp when the job was created.",
        type = "string",
        format = "date-time",
        example = "2023-10-27T10:00:00Z",
        readOnly = true
    )
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Schema(
        description = "Timestamp when the job was completed (if applicable).",
        type = "string",
        format = "date-time",
        example = "2023-10-27T10:15:30Z",
        nullable = true,
        readOnly = true
    )
    val completedAt: LocalDateTime? = null,

    @Schema(
        description = "Error message if the job failed.",
        example = "Failed to connect to database.",
        nullable = true,
        readOnly = true
    )
    val errorMessage: String? = null,

    @Schema(
        description = "The generated report if the job status is COMPLETED, otherwise null.",
        nullable = true,
        readOnly = true
    )
    val report: Report? = null,

//    @Schema(
//        description = "Parameters used for report generation, structure depends on reportType",
//        oneOf = [
//            VisitSummaryParameters::class,
//            PatientHistoryParameters::class,
//            LabReportParameters::class,
//            CustomReportParameters::class
//        ],
//        discriminatorProperty = "reportType"
//    )
    val parameters: Map<String, Any> = emptyMap(),

    @Schema(
        description = "Most recent progress message or status update.",
        example = "Generating visit summary report",
        nullable = true,
        readOnly = true
    )
    val lastMessage: String, // Most recent progress message
)

/** Possible statuses for a job. */
@Serdeable
@Schema(
    name = "JobStatus",
    description = "Possible statuses for a report generation job."
)
enum class JobStatus {
    @Schema(description = "The job has been received and is waiting to be processed.")
    QUEUED,

    @Schema(description = "The job is currently being processed.")
    PROCESSING,

    @Schema(description = "The job has completed successfully.")
    COMPLETED,

    @Schema(description = "The job encountered an error and failed.")
    FAILED
}

/** Converts this entity to a DTO. */
fun JobEntity.toDto(reportEntity: ReportEntity? = null): Job {
    return Job(
        id = this.id,
        status = this.status,
        reportType = this.reportType,
        progress = this.progress,
        createdAt = this.createdAt,
        completedAt = this.completedAt,
        errorMessage = this.errorMessage,
        report = reportEntity?.toDto(),
        parameters = this.parameters,
        lastMessage = this.lastMessage ?: ""
    )
}
