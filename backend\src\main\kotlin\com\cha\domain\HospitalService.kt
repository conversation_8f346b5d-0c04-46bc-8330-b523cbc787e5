package com.cha.domain

import com.cha.domain.exception.ResourceNotFoundException
import com.cha.model.dto.Hospital
import com.fasterxml.jackson.databind.ObjectMapper
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.multitenancy.tenantresolver.TenantResolver
import jakarta.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.InputStream

private val log = KotlinLogging.logger {}

@Singleton
class HospitalService(
    private val objectMapper: ObjectMapper,
    private val tenantResolver: TenantResolver,
) {

    /**
     * Fetches tenant information with the expectation there is a
     * <tenantId>.json file within the `src/main/resources/tenants/`
     * directory of the project, which makes them available on the classpath.
     */
    suspend fun fetchHospitalInfo(): Hospital {
        val tenant = tenantResolver.resolveTenantId()
        log.debug { "Fetching tenant information for tenant: $tenant" }

        val tenantJsonPath = "/tenants/${tenant}.json"

        return withContext(Dispatchers.IO) {
            val inputStream: InputStream? = this.javaClass.getResourceAsStream(tenantJsonPath)

            if (inputStream == null) {
                log.error { "Cannot find tenant JSON file at $tenantJsonPath for tenant: $tenant" }
                throw ResourceNotFoundException(
                    resourceType = "Tenant",
                    identifier = tenant,
                    message = "The tenant information for '$tenant' could not be found. " +
                            "Please ensure the tenant JSON file exists.",
                )
            }

            inputStream.use { stream ->
                objectMapper.readValue(stream, Hospital::class.java)
            }
        }
    }
}
