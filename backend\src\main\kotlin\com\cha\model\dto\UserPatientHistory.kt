package com.cha.model.dto

import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(name = "UserPatientHistory", description = "User patient history entry")
data class UserPatientHistory(
    @Schema(description = "Unique identifier of the patient history entry", example = "123")
    val id: Long,
    @Schema(description = "When the patient was viewed", example = "2025-06-15T10:30:00")
    val viewedAt: LocalDateTime,
    @Schema(description = "The patient information associated with this history entry")
    val patient: Patient,
)

@Serdeable
@Schema(name = "ClearPatientHistoryResponse", description = "Response after clearing patient history")
data class ClearPatientHistoryResponse(
    @Schema(description = "Success message", example = "Patient history cleared successfully")
    val message: String,
    @Schema(description = "Number of entries that were deleted", example = "15")
    val deletedCount: Int,
)
