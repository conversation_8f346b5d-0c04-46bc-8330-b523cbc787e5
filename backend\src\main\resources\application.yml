micronaut:
  tracing:
    enabled: true
    sampler:
      probability: 1.0

  # Problem JSON configuration (RFC 7807)
  problem:
    enabled: true
    stack-trace: false

  # See: https://micronaut-projects.github.io/micronaut-data/latest/guide/#multitenancy
  data:
    jdbc:
      dialect: SQL_SERVER
    multi-tenancy:
      mode: DATASOURCE
  multitenancy:
    tenantresolver:
      fixed:
        enabled: true
        tenant-id: lrh
  server:
    port: 8080
    context-path: /api
    cors:
      enabled: true
      configurations:
        all:
          allowed-origins-regex: .*
          allowed-methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          allowed-headers:
            - Content-Type
            - Authorization
            - User-Agent
            - Accept
            - Origin
            - Cache-Control
            - X-Amz-Date
            - X-Api-Key
            - X-Amz-Security-Token
            - X-Requested-With
          exposed-headers:
            - Content-Type
            - Authorization
          allow-credentials: true
          max-age: 300
  router:
    #    versioning:
    #      enabled: true
    #      default-version: 1
    #      parameter:
    #        enabled: true
    #        names: 'v,api-version'
    static-resources:
      jsonschema:
        paths: classpath:META-INF/schemas
        mapping: /schemas/**
      swagger:
        paths: classpath:META-INF/swagger
        mapping: /swagger/**
      redoc:
        paths: classpath:META-INF/swagger/views/redoc
        mapping: /redoc/**
      swagger-ui:
        paths: classpath:META-INF/swagger/views/swagger-ui
        mapping: /swagger-ui/**

---
micronaut:
  security:
    authentication: bearer
    token:
      roles-name: cognito:groups # Use Cognito groups for roles
      jwt:
        signatures:
          jwks:
            cognito:
              url: 'https://cognito-idp.${aws.region}.amazonaws.com/${aws.cognito.userPoolId}/.well-known/jwks.json'
        claims-validators:
          expiration: true  # Enforce token expiration checks
    intercept-url-map:
      - access: isAnonymous()
        pattern: /redoc/**
      - access: isAnonymous()
        pattern: /swagger/**
      - access: isAnonymous()
        pattern: /swagger-ui/**
    endpoints:
      login:
        enabled: false

---
datasources:
  lrh:
    url: jdbc:sqlserver://${database.host}:${database.port};databaseName=archive_lrh;encrypt=true;trustServerCertificate=true;loginTimeout=10
    driverClassName: "com.microsoft.sqlserver.jdbc.SQLServerDriver"
    username: ${database.username}
    password: ${database.password}
    db-type: mssql
    #    url: jdbc:postgresql://${database.host}:${database.port}/viewer
    #    driverClassName: org.postgresql.Driver
    #    username: ${database.username}
    #    password: ${database.password}
    #    db-type: postgres
    allow-pool-suspension: true
    hikari:
      maximumPoolSize: 8
      minimumIdle: 2
      idleTimeout: 300000
      maxLifetime: 1800000
      validationTimeout: 5000
  hhs:
    url: jdbc:sqlserver://${database.host}:${database.port};databaseName=archive_hhs;encrypt=true;trustServerCertificate=true;loginTimeout=10
    driverClassName: "com.microsoft.sqlserver.jdbc.SQLServerDriver"
    username: ${database.username}
    password: ${database.password}
    db-type: mssql
    allow-pool-suspension: true
    hikari:
      maximumPoolSize: 8
      minimumIdle: 2
      idleTimeout: 300000
      maxLifetime: 1800000
      validationTimeout: 5000
liquibase:
  enabled: false
  datasources:
    default:
      enabled: false
      change-log: classpath:db/liquibase-changelog.xml

---
# Metrics and Management Endpoints
micronaut:
  metrics:
    enabled: true
    export:
      cloudwatch:
        enabled: true # Enable exporting metrics to CloudWatch
        namespace: ViewerBackend # CloudWatch namespace for metrics
      statsd:
        enabled: false # Disabling for AWS deployments
        step: PT1M # Export metrics every 1 minute
  openapi:
    enabled: true
    filename: latest
    generator:
      extensions:
        enabled: true
    openapi31:
      enabled: true
    views:
      spec: "redoc.enabled=true,swagger-ui.enabled=true,swagger-ui.theme=flattop"
  endpoints:
    metrics:
      enabled: true
      sensitive: false
    health:
      enabled: true
      details-visible: ANONYMOUS
    loggers:
      enabled: true
      sensitive: false # TODO: change to true
    info:
      enabled: true
      sensitive: false
      details-visible: ANONYMOUS # Or AUTHENTICATED

aws:
  region: ${AWS_REGION:us-east-1}
  s3:
    bucket: ${APP_BUCKET_NAME:viewer-documents}
    prefixes:
      imported-ehr-documents: "imported-ehr-documents"
      viewer-generated-reports: "viewer-generated-reports"
  cognito:
    userPoolId: ${COGNITO_POOL_ID:your_user_pool_id}
    clientId: ${OAUTH_CLIENT_ID:**********}
    issuerUrl: https://cognito-idp.${aws.region}.amazonaws.com/${aws.cognito.userPoolId}
