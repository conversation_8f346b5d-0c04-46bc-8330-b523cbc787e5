## Development environment configuration
micronaut:
  #  multitenancy:
  #    tenantResolver:
  #    fixed:
  #      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: false

endpoints:
  all:
    enabled: true
    sensitive: false
  health:
    details-visible: ANONYMOUS
  loggers:
    write-sensitive: true

#dataSources:
#  default:
#    hikari:
#      maximumPoolSize: 4
#      minimumIdle: 1
#      leakDetectionThreshold: 30000   # Aggressive leak detection for debugging
