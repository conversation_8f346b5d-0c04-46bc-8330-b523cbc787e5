package com.cha.repository

import com.cha.model.entity.PatientEntity
import io.micronaut.data.annotation.Query
import io.micronaut.data.jdbc.annotation.JdbcRepository
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.data.model.query.builder.sql.Dialect
import io.micronaut.data.repository.kotlin.CoroutinePageableCrudRepository
import java.time.LocalDate

@JdbcRepository(dialect = Dialect.SQL_SERVER)
interface PatientRepository : CoroutinePageableCrudRepository<PatientEntity, Long> {

    suspend fun findByEmpi(
        empi: Int,
    ): List<PatientEntity>

    @Query(
        value = """
            WITH RankedPatients AS (
                SELECT
                  p.*,
                  ROW_NUMBER() OVER(PARTITION BY p.empi ORDER BY p.name) as rn
                FROM mpi p WHERE p.bth_ts = :birthDate
            )
            SELECT p.* FROM RankedPatients p WHERE p.rn = 1
            ORDER BY p.name
        """,
        countQuery = """
            SELECT COUNT(DISTINCT p.empi) FROM mpi p WHERE p.bth_ts = :birthDate
        """,
        nativeQuery = true
    )
    suspend fun searchDistinctByBthTs(birthDate: LocalDate, pageable: Pageable): Page<PatientEntity>

    @Query(
        value = """
            WITH RankedPatients AS (
                SELECT
                  p.*,
                  ROW_NUMBER() OVER(PARTITION BY p.empi ORDER BY p.name) as rn
                FROM mpi p WHERE 
                 IIF(CHARINDEX(',', p.name) > 0, LOWER(
                            REPLACE(
                                -- Get FirstName (part after comma) + LastName (part before comma)
                                SUBSTRING(p.name, CHARINDEX(',', p.name) + 1, LEN(p.name)) +
                                SUBSTRING(p.name, 1, CHARINDEX(',', p.name) - 1),
                                ' ', ''
                            )
                         ), LOWER(REPLACE(p.name, ' ', ''))) LIKE :namePattern
            )
            SELECT p.* FROM RankedPatients p WHERE p.rn = 1
            ORDER BY p.name
        """,
        countQuery = """
            SELECT COUNT(DISTINCT p.empi) FROM mpi p WHERE 
             IIF(CHARINDEX(',', p.name) > 0, LOWER(
                        REPLACE(
                            SUBSTRING(p.name, CHARINDEX(',', p.name) + 1, LEN(p.name)) +
                            SUBSTRING(p.name, 1, CHARINDEX(',', p.name) - 1),
                            ' ', ''
                        )
                     ), LOWER(REPLACE(p.name, ' ', ''))) LIKE :namePattern
        """,
        nativeQuery = true
    )
    suspend fun searchByName(namePattern: String, pageable: Pageable): Page<PatientEntity>

    @Query(
        value = """
            WITH RankedPatients AS (
                SELECT
                  p.*,
                  ROW_NUMBER() OVER(PARTITION BY p.empi ORDER BY p.name) as rn
                FROM mpi p WHERE p.mrn LIKE :mrn
            )
            SELECT p.* FROM RankedPatients p WHERE p.rn = 1
            ORDER BY p.name
        """,
        countQuery = """
            SELECT COUNT(DISTINCT p.empi) FROM mpi p WHERE p.mrn LIKE :mrn
        """,
        nativeQuery = true
    )
    suspend fun searchByMrn(mrn: String, pageable: Pageable): Page<PatientEntity>
}
