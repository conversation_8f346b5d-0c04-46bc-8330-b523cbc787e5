package com.cha.repository

import com.cha.model.entity.JobEntity
import io.micronaut.data.jdbc.annotation.JdbcRepository
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.data.model.query.builder.sql.Dialect
import io.micronaut.data.repository.kotlin.CoroutinePageableCrudRepository
import jakarta.validation.Valid

/** Micronaut Data JDBC repository for JobEntity. */
@JdbcRepository(dialect = Dialect.SQL_SERVER)
interface JobRepository : CoroutinePageableCrudRepository<@Valid JobEntity, String> {

    /** Finds all jobs for a specific user. */
    suspend fun findByUserId(userId: String, pageable: Pageable): Page<JobEntity>

    /** Finds a specific job by its ID and user ID. */
    suspend fun findByIdAndUserId(id: String, userId: String): JobEntity?
}
