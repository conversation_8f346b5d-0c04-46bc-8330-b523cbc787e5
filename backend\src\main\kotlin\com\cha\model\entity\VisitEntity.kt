package com.cha.model.entity

import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("visits")
data class VisitEntity(
    @field:Id
    val id: Int? = null,
    val systemId: Int,
    val empi: Int,
    val mrn: String?,
    val accountNumber: String?,
    val admitDate: LocalDateTime?,
    val dischargeDate: LocalDateTime?,
    val visitStatus: String?,
    val patientCategory: String?,
    val admitSource: String?,
    val admitType: String?,
    val admitService: String?,
    val admDiag: String?,
    val dischargeService: String?,
    val dischargeStatus: String?,
    val financialClass: String?,
    val patientType: String?,
    val homeTelephone: String?,
    val mobileTelephone: String?,
    val visitType: String?,
    val reason: String?,
    val notes: String?,
    val visitNumber: Int?,
    val provider: String?,
    val systemName: String?,
    val securityLevel: Boolean? = false
)
