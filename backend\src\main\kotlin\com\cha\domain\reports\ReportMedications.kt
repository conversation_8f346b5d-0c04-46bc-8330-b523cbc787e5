package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable

private fun addMedicationsSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.medications?.let { medications ->
        document.add(createSectionTitle("Medications"))
        if (medications.isNotEmpty()) {
            medications.forEach { med ->
                val medTable = PdfPTable(2)
                medTable.widthPercentage = 100f
                setupDataItemTable(medTable)

                val headerText = "${med.brandName} (${med.genericName ?: "N/A"})"
                medTable.addCell(createDataItemHeaderCell(headerText, 2))

                medTable.addCell(createLabelValueCell("Dose:", med.dosage, inTable = true))
                medTable.addCell(createLabelValueCell("Dosage Form:", med.dosageForm, inTable = true))
                medTable.addCell(createLabelValueCell("Frequency:", med.frequency, inTable = true))
                medTable.addCell(createLabelValueCell("Caregiver:", med.caregiver, inTable = true))
                medTable.addCell(
                    createLabelValueCell(
                        "Start Date:",
                        med.startDatetime?.let { formatDateTime(it) },
                        inTable = true
                    )
                )
                medTable.addCell(
                    createLabelValueCell(
                        "End Date:",
                        med.stopDatetime?.let { formatDateTime(it) },
                        inTable = true
                    )
                )

                document.add(medTable)
            }
        } else {
            val paragraph = Paragraph("No medications recorded for this visit.", FONT_BODY)
            paragraph.spacingAfter = 25f
            document.add(paragraph)
        }
    }
}