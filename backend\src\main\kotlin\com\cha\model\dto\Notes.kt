package com.cha.model.dto

import com.cha.model.entity.NotesEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(
    description = "Represents a clinical note for a patient, such as progress notes, discharge summaries, or other documentation."
)
data class Notes(
    @Schema(description = "Enterprise Master Patient Index (EMPI) identifier for the patient.")
    val empi: Int?,
    @Schema(description = "System identifier for the note record.")
    val systemId: Int?,
    @Schema(description = "Medical Record Number (MRN) for the patient.")
    val mrn: String?,
    @Schema(description = "Account number associated with the note record.")
    val accountNumber: String?,
    @Schema(description = "Text content of the note.")
    val noteText: String?,
    @Schema(description = "Date and time when the note was created.")
    val noteDatetime: LocalDateTime?,
    val systemName: String?,
    @Schema(description = "Indicates if the note record is restricted due to security or privacy concerns.")
    val securityLevel: Boolean?,
    @Schema(description = "Admission date associated with the note record, if relevant.")
    val admitDate: LocalDateTime?,
    @Schema(description = "Indicates if the note is private or restricted from general access.")
    val privateNoteFlag: String?
)

fun NotesEntity.toDto(): Notes {
    return Notes(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        noteDatetime = this.noteDatetime,
        privateNoteFlag = this.privateNoteFlag,
        noteText = this.noteText,
        systemName = this.systemName,
        securityLevel = this.securityLevel,
        admitDate = this.admitDate
    )
}