package com.cha

import io.micronaut.openapi.annotation.OpenAPIManagement
import io.micronaut.runtime.Micronaut.run
import io.swagger.v3.oas.annotations.OpenAPIDefinition
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType
import io.swagger.v3.oas.annotations.info.Contact
import io.swagger.v3.oas.annotations.info.Info
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.security.SecurityScheme
import io.swagger.v3.oas.annotations.security.SecuritySchemes
import io.swagger.v3.oas.annotations.tags.Tag

/**
 * Security Sequence for Web App:
 * 1. User logs in using their credentials, receiving a JWT token.
 * 2. The token is included in the Authorization header for subsequent
 *    requests.
 * 3. Endpoints are secured using roles, e.g., ROLE_USER, ROLE_ADMIN.
 * 4. Sensitive operations require elevated roles, verified by the token.
 * 5. Tokens are refreshed using a refresh token mechanism to maintain
 *    session.
 */
@OpenAPIDefinition(
    info = Info(
        title = "Viewer Backend",
        version = BuildConfig.VERSION,
        description = """\
**Viewer Backend API**

This API provides access to the backend services for the Viewer application.
It allows clients to manage and interact with various resources within the system.

**Key Features:**

*   User authentication and authorization.
*   Patient data retrieval such as visits or allergies.
*   Report generation.

**Authentication:**

The API uses JWT Bearer tokens for authentication.
1.  Users log in via the `/v1/auth/login` endpoint to obtain a JWT.
2.  The JWT must be included in the `Authorization` header for all protected endpoints, using the `Bearer` scheme (e.g., `Authorization: Bearer <token>`).
3.  Endpoints are secured based on user roles (e.g., `ROLE_USER`, `ROLE_ADMIN`).
4.  Sensitive operations require elevated roles, which are verified through the token.
5.  Tokens can be refreshed using a dedicated mechanism to maintain user sessions.
""",
        contact = Contact(name = "Cam", email = "<EMAIL>")
    ),
    security = [SecurityRequirement(name = "bearerAuth")],
)
@SecuritySchemes(
    SecurityScheme(
        name = "bearerAuth",
        type = SecuritySchemeType.HTTP,
        scheme = "bearer",
        bearerFormat = "JWT",
        openIdConnectUrl = "https://cognito-idp.us-east-1.amazonaws.com/us-east-1_wa24C8wHJ/.well-known/openid-configuration",
        description = "JWT Bearer token for authentication"
    )
)
@OpenAPIManagement(
    tags = [Tag(name = "Management")],
    security = [SecurityRequirement(name = "bearerAuth", scopes = ["ROLE_ADMIN"])]
)
object Api

fun main(args: Array<String>) {
    run(*args)
}
