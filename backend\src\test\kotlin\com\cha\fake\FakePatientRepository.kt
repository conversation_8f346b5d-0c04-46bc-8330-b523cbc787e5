package com.cha.fake

import com.cha.model.entity.PatientEntity
import com.cha.repository.PatientRepository
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.data.model.Sort
import io.micronaut.data.model.Sort.Order.Direction.DESC
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicLong

/**
 * A fake, in-memory implementation of the [PatientRepository] for testing
 * purposes.
 */
class FakePatientRepository : PatientRepository {
    private val patients = CopyOnWriteArrayList<PatientEntity>()
    private val nextId = AtomicLong(1)

    /**
     * Clears the existing data and seeds the repository with new entities.
     * Assigns sequential IDs to the entities.
     */
    fun seed(vararg entities: PatientEntity) {
        patients.clear()
        nextId.set(1)
        entities.forEach {
            // Use empi as the unique identifier for all operations
            // Remove id usage, as PatientEntity does not have an id property
        }
        patients.addAll(entities)
    }

    override suspend fun findByEmpi(empi: Int): List<PatientEntity> {
        return patients.filter { it.empi == empi }
    }

    override suspend fun searchByName(namePattern: String, pageable: Pageable): Page<PatientEntity> {
        val pattern = namePattern.removePrefix("%")
            .removeSuffix("%")
            .lowercase()
        val filtered = patients.filter { it.name?.lowercase()?.contains(pattern) == true }
        return Page.of(filtered.sortedBy { it.empi }, pageable, filtered.size.toLong())
    }

    override suspend fun searchByMrn(mrn: String, pageable: Pageable): Page<PatientEntity> {
        val filtered = patients.filter { it.mrn == mrn }
        return Page.of(filtered.sortedBy { it.empi }, pageable, filtered.size.toLong())
    }

    override suspend fun searchDistinctByBthTs(birthDate: LocalDate, pageable: Pageable): Page<PatientEntity> {
        val filtered = patients.filter { it.bthTs == birthDate }.distinctBy { it.empi }
        return Page.of(filtered.sortedBy { it.empi }, pageable, filtered.size.toLong())
    }

    override suspend fun <S : PatientEntity> save(entity: S): S {
        // Use empi as the unique identifier
        val index = patients.indexOfFirst { it.empi == entity.empi }
        if (index != -1) {
            patients[index] = entity
        } else {
            patients.add(entity)
        }
        return entity
    }

    override fun <S : PatientEntity> saveAll(entities: Iterable<S>): Flow<S> {
        return entities.asFlow().map { save(it) }
    }

    override suspend fun findById(id: Long): PatientEntity? {
        // Use empi as the unique identifier
        return patients.find { it.empi.toLong() == id }
    }

    override suspend fun existsById(id: Long): Boolean {
        return patients.any { it.empi.toLong() == id }
    }

    override fun findAll(): Flow<PatientEntity> {
        return patients.sortedBy { it.empi }.asFlow()
    }

    override suspend fun findAll(pageable: Pageable): Page<PatientEntity> {
        val content = patients.sortedBy { it.empi }
            .drop(pageable.offset.toInt())
            .take(pageable.size)
        return Page.of(content, pageable, patients.size.toLong())
    }

    override fun findAll(sort: Sort): Flow<PatientEntity> {
        val comparator = sort.orderBy.map { order ->
            val propertyComparator: Comparator<PatientEntity> = when (order.property) {
                "name" -> compareBy(nullsLast()) { it.name }
                "empi" -> compareBy(nullsLast()) { it.empi }
                "mrn" -> compareBy(nullsLast()) { it.mrn }
                "bthTs" -> compareBy(nullsLast()) { it.bthTs }
                else -> throw IllegalArgumentException("Unsupported sort property for FakePatientRepository: ${'$'}{order.property}")
            }
            if (order.direction == DESC) {
                propertyComparator.reversed()
            } else {
                propertyComparator
            }
        }.reduceOrNull { acc, comp -> acc.then(comp) }

        val sortedPatients = if (comparator != null) {
            patients.sortedWith(comparator)
        } else {
            patients.sortedBy { it.empi }
        }
        return sortedPatients.asFlow()
    }

    override suspend fun count(): Long {
        return patients.size.toLong()
    }

    override suspend fun deleteById(id: Long): Int {
        patients.removeIf { it.empi.toLong() == id }
        return 0
    }

    override suspend fun delete(entity: PatientEntity): Int {
        patients.removeIf { it.empi == entity.empi }
        return 0
    }

    override suspend fun deleteAll(): Int {
        patients.clear()
        return 0
    }

    override suspend fun deleteAll(entities: Iterable<PatientEntity>): Int {
        val idsToDelete = entities.map { it.empi }.toSet()
        val before = patients.size
        patients.removeIf { idsToDelete.contains(it.empi) }
        return before - patients.size
    }

    override suspend fun <S : PatientEntity> update(entity: S): S {
        val index = patients.indexOfFirst { it.empi == entity.empi }
        if (index == -1) {
            throw IllegalArgumentException("Cannot update entity with empi ${'$'}{entity.empi} because it does not exist.")
        }
        patients[index] = entity
        return entity
    }

    override fun <S : PatientEntity> updateAll(entities: Iterable<S>): Flow<S> {
        return entities.asFlow().map { update(it) }
    }
}
