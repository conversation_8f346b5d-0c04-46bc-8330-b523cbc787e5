package com.cha.controller

import com.cha.domain.UserService
import com.cha.model.dto.ClearPatientHistoryResponse
import com.cha.model.dto.UserPatientHistory
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.rules.SecurityRule
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.zalando.problem.Problem

private val logger = KotlinLogging.logger { }

@Secured(SecurityRule.IS_AUTHENTICATED)
@Controller("/v1/user")
@Tag(name = "User", description = "User profile and settings operations")
class UserController(
    private val userService: UserService,
) {
    @Get("/patient-history")
    @Operation(
        summary = "Get user patient history",
        description = "Retrieves the authenticated user's patient history. Results are ordered by most recent first. " +
                "A user can have a max of 20 patients in their history."
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "Patient history retrieved successfully",
            content = [Content(schema = Schema(implementation = Array<UserPatientHistory>::class))]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Unauthorized - Authentication required",
            content = [Content(schema = Schema(implementation = Problem::class))]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(schema = Schema(implementation = Problem::class))]
        )
    )
    suspend fun getUserPatientHistory(
        authentication: Authentication,
    ): List<UserPatientHistory> {
        val userId = authentication.attributes["sub"] as String
        logger.debug { "Fetching patient history for user: $userId" }
        return userService.getUserPatientHistory(userId)
    }

    @Delete("/patient-history")
    @Operation(
        summary = "Clear user patient history",
        description = "Clears all patient history entries for the authenticated user. This action cannot be undone."
    )
    @ApiResponses(
        ApiResponse(
            responseCode = "200",
            description = "Patient history cleared successfully",
            content = [Content(schema = Schema(implementation = ClearPatientHistoryResponse::class))]
        ),
        ApiResponse(
            responseCode = "401",
            description = "Unauthorized - Authentication required",
            content = [Content(schema = Schema(implementation = Problem::class))]
        ),
        ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = [Content(schema = Schema(implementation = Problem::class))]
        )
    )
    suspend fun clearUserPatientHistory(
        authentication: Authentication,
    ): ClearPatientHistoryResponse {
        val userId = authentication.attributes["sub"] as String
        logger.info { "Clearing patient history for user: $userId" }
        return userService.clearUserPatientHistory(userId)
    }
}
