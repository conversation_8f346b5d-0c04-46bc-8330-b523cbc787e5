package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("diagnosis")
data class DiagnosisEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int? = 0,
    val mrn: String?,
    val accountNumber: String?,
    val icdCode: String?,
    val icdDescription: String?,
    val rank: Int?,
    val type: String?,
    val sequence: Int?,
    val caregiver: String?,
    val procedureDate: LocalDateTime?,
    val active: Int? = 1,
    val systemName: String?,
    val admitDate: LocalDateTime?,
    val securityLevel: Boolean? = false
)
