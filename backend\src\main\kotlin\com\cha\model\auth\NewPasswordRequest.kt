package com.cha.model.auth

import io.micronaut.core.annotation.Introspected
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size

@Introspected
@Serdeable
@Schema(name = "NewPasswordRequest", description = "New password request for AWS Cognito admin workflow")
data class NewPasswordRequest(
    @field:NotBlank
    @Schema(description = "Username of the user", example = "johndoe")
    val username: String,
    @field:NotBlank
    @field:Size(min = 8, max = 100)
    @Schema(description = "New password for the user", example = "NewPassword123!")
    val password: String,
    @Schema(description = "Session token from challenge response", example = "session-token-from-challenge")
    val session: String? = null,
)
