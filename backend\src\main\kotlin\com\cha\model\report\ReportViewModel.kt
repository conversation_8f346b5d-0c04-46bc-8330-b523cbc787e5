package com.cha.model.report

import com.cha.model.dto.Address
import com.cha.model.dto.Alert
import com.cha.model.dto.Allergy
import com.cha.model.dto.Bill
import com.cha.model.dto.Diagnosis
import com.cha.model.dto.Document
import com.cha.model.dto.HomeMedication
import com.cha.model.dto.Immunization
import com.cha.model.dto.Insurance
import com.cha.model.dto.Invoice
import com.cha.model.dto.InvoiceNote
import com.cha.model.dto.LabResult
import com.cha.model.dto.LabResultReference
import com.cha.model.dto.Medication
import com.cha.model.dto.MicroComment
import com.cha.model.dto.MicroDetail
import com.cha.model.dto.MicroSuscept
import com.cha.model.dto.Notes
import com.cha.model.dto.Orders
import com.cha.model.dto.Procedure
import com.cha.model.dto.ReportType
import com.cha.model.dto.System
import com.cha.model.dto.Telephone
import com.cha.model.dto.TranscriptionResult
import com.cha.model.dto.Visit
import java.time.ZonedDateTime

data class ReportViewModel(
    val hospital: HospitalInfoViewModel,
    val requestUser: RequestUserViewModel,
    val patient: PatientInfoViewModel,
    val system: List<System>,
    val visits: List<Visit>,
    val allClinicalData: List<VisitClinicalData>,
    val allFinancialData: List<VisitFinancialData>,
    val documentsMetadata: DocumentsMetadataViewModel,
    val generatedDateTime: ZonedDateTime,
    val reportTitle: String,
    val reportType: ReportType,
)

data class RequestUserViewModel(
    val id: String,
    val username: String,
    val firstName: String,
    val lastName: String,
    val email: String,
    val role: String?,
)

data class PatientInfoViewModel(
    val empi: Int,
    val firstName: String,
    val lastName: String,
    val middleName: String?,
    val sex: String,
    val dob: String?,
    val phone: List<Telephone>,
    val address: List<Address>,
)

data class HospitalInfoViewModel(
    val name: String,
    val address: Address,
    val telephone: String,
    val fax: String?,
    val website: String,
    val logoUrl: String,
    val timezone: String,
)

data class VisitSectionsViewModel(
    val clinical: ClinicalViewModel?,
    val financial: FinancialViewModel?,
)

data class ClinicalViewModel(
    val allergies: List<Allergy>?,
    val orders: List<Orders>?,
    val medications: List<Medication>?,
    val homeMedications: List<HomeMedication>?,
    val labResults: List<LabResult>?,
    val labResultReferences: List<LabResultReference>?,
    val microComments: List<MicroComment>?,
    val microSuscept: List<MicroSuscept>?,
    val microDetail: List<MicroDetail>?,
    val immunizations: List<Immunization>?,
    val procedures: List<Procedure>?,
    val alerts: List<Alert>?,
    val diagnoses: List<Diagnosis>?,
    val notes: List<Notes>?,
    val transcriptionResults: List<TranscriptionResult>?,
)

data class FinancialViewModel(
    val insurance: List<Insurance>?,
    val bills: List<Bill>?,
    val invoiceNotes: List<InvoiceNote>?,
    val invoices: List<Invoice>?,
)

data class DocumentsMetadataViewModel(
    val visitDocuments: List<Document>?,
    val unassignedDocuments: List<Document>?,
)

/** Data class that associates clinical data with a specific visit ID. */
data class VisitClinicalData(
    val visitId: String,
    val data: ClinicalViewModel,
)

/** Data class that associates financial data with a specific visit ID. */
data class VisitFinancialData(
    val visitId: String,
    val data: FinancialViewModel,
)
