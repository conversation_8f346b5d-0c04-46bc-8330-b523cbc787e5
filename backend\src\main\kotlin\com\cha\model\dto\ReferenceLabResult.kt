package com.cha.model.dto

import com.cha.model.entity.LabResultReferenceEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime

@Serdeable
@Schema(description = "Laboratory result reference information")
data class LabResultReference(
    @Schema(description = "Patient's Enterprise Master Patient Index", example = "12345")
    val empi: Int?,
    @Schema(description = "System identifier", example = "1")
    val systemId: Int?,
    @Schema(description = "Medical Record Number", example = "MRN123456")
    val mrn: String?,
    @Schema(description = "Account number", example = "ACC789012")
    val accountNumber: String?,
    @Schema(description = "Result sequence", example = "001")
    val sequence: String?,
    @Schema(description = "Result type indicator", example = "N")
    val resultType: Char?,
    @Schema(description = "Order code", example = "CBC")
    val orderCode: String?,
    @Schema(description = "Order code description", example = "Complete Blood Count")
    val orderCodeDescription: String?,
    @Schema(description = "Test code", example = "WBC")
    val testCode: String?,
    @Schema(description = "Test description", example = "White Blood Cell Count")
    val testDescription: String?,
    @Schema(description = "Test abbreviation", example = "WBC")
    val testAbbreviation: String?,
    @Schema(description = "Result line", example = "8.5")
    val resultLine: String?,
    @Schema(description = "Reference range", example = "4.0-11.0")
    val referenceRange: String?,
    @Schema(description = "Abnormal flag", example = "N")
    val abnormalFlag: String?,
    @Schema(description = "Test unit", example = "K/uL")
    val testUnit: String?,
    @Schema(description = "Event date", example = "2024-01-15T10:30:00")
    val eventDate: LocalDateTime?,
    @Schema(description = "Release date", example = "2024-01-15T14:30:00")
    val releaseDate: LocalDateTime?,
    @Schema(description = "Performed by", example = "Lab Tech Smith")
    val performedBy: String?,
    @Schema(description = "Entered date", example = "2024-01-15T14:00:00")
    val enteredDate: LocalDateTime?,
    @Schema(description = "Entered by", example = "Tech Jones")
    val enteredBy: String?,
    @Schema(description = "Test status", example = "Final")
    val testStatus: String?,
    @Schema(description = "Corrected result", example = "8.7")
    val correctedResult: String?,
    @Schema(description = "Abnormal code", example = "NORMAL")
    val abnormalCode: String?,
    @Schema(description = "Test result flag", example = "F")
    val testResultFlag: String?,
    @Schema(description = "Normal lookup flag", example = "Y")
    val normalLookupFlag: String?,
    @Schema(description = "System name where result originates", example = "LIS")
    val systemName: String?,
    @Schema(description = "Security level flag", example = "false")
    val securityLevel: Boolean?,
    @Schema(description = "Admission date", example = "2024-01-15T08:00:00")
    val admitDate: LocalDateTime?
)

fun LabResultReferenceEntity.toDto(): LabResultReference {
    return LabResultReference(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        sequence = this.sequence,
        resultType = this.resultType,
        orderCode = this.orderCode,
        orderCodeDescription = this.orderCodeDescription,
        testCode = this.testCode,
        testDescription = this.testDescription,
        testAbbreviation = this.testAbbreviation,
        resultLine = this.resultLine,
        referenceRange = this.referenceRange,
        abnormalFlag = this.abnormalFlag,
        testUnit = this.testUnit,
        eventDate = this.eventDate,
        releaseDate = this.releaseDate,
        performedBy = this.performedBy,
        enteredDate = this.enteredDate,
        enteredBy = this.enteredBy,
        testStatus = this.testStatus,
        correctedResult = this.correctedResult,
        abnormalCode = this.abnormalCode,
        testResultFlag = this.testResultFlag,
        normalLookupFlag = this.normalLookupFlag,
        systemName = this.systemName,
        securityLevel = this.securityLevel,
        admitDate = this.admitDate
    )
}
