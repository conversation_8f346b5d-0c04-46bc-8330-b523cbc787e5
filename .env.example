# Example Environment Variables for viewer-backend
# Copy this file to .env and replace the placeholder values with your actual configuration.
# Do NOT commit the actual .env file to version control.

# General AWS
AWS_ACCOUNT_ID=************
AWS_REGION=us-east-1

APP_BUCKET_NAME=viewer-documents

# Security
COGNITO_POOL_ID=us-east-1_xxxxxxx
OAUTH_CLIENT_ID=xxxxxxxxx

# Application Configuration
MICRONAUT_ENVIRONMENTS=local

# For production, specify exact origins like: http://localhost:3000,https://app.yourdomain.com
# Use * only for development
ALLOWED_CORS_ORIGINS=http://localhost:3000,https://app.yourdomain.com
