package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("invoice_notes")
data class InvoiceNoteEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val systemId: Int?,
    val empi: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val noteDate: LocalDateTime?,
    val patientNote: String?,
    val systemName: String?,
    val securityLevel: Boolean? = false,
    val admitDate: LocalDateTime?
)