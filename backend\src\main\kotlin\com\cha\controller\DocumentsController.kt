package com.cha.controller

import com.cha.domain.DocumentsService
import com.cha.model.dto.Document
import com.cha.util.parsePageableWithSorting
import io.micronaut.data.model.Page
import io.micronaut.data.model.Pageable
import io.micronaut.data.model.Sort
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag

@Secured(SecurityRule.IS_AUTHENTICATED)
@Controller("/v1/patients")
@Tag(name = "Documents", description = "Patient document endpoints")
class DocumentsController(
    private val documentsService: DocumentsService,
) {

    @Operation(
        summary = "Get patient documents",
        description = "Retrieves a paginated list of documents for a specific patient",
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "List of patient documents",
                content = [Content(schema = Schema(implementation = Page::class))]
            ),
            ApiResponse(responseCode = "401", description = "Unauthorized"),
            ApiResponse(responseCode = "404", description = "Patient not found")
        ]
    )
    @Get("/{empi}/documents")
    suspend fun getPatientDocuments(
        @Parameter(description = "Patient's EMPI identifier") @PathVariable empi: Int,
        @Parameter(description = "Pagination parameters", required = false) pageable: Pageable,
        request: HttpRequest<*>,
    ): Page<Document> {
        val finalPageable = request.parsePageableWithSorting(
            pageable = pageable,
            defaultSortProperty = "created",
            defaultSortDirection = Sort.Order.Direction.DESC
        )

        return documentsService.getPatientDocuments(empi, null, null, finalPageable)
    }

    @Operation(
        summary = "Get document",
        description = """
           Retrieves a specific document by its key. Supports both generated reports and imported EHR system documents. 
        """,
        responses = [
            ApiResponse(
                responseCode = "200",
                description = "Document content",
                content = [Content(mediaType = "application/octet-stream")]
            ),
            ApiResponse(responseCode = "401", description = "Unauthorized"),
            ApiResponse(responseCode = "404", description = "Document not found")
        ]
    )
    @Get("/documents/{documentKey:.+}")
    suspend fun getDocument(
        @Parameter(description = "Document unique key/identifier") @PathVariable documentKey: String,
        @Parameter(description = "Whether the document is a generated report")
        @QueryValue(defaultValue = "false")
        generatedReport: Boolean = false,
    ): HttpResponse<*> {
        return documentsService.getDocument(
            filenameWithinTenant = documentKey,
            generatedReport = generatedReport
        )
    }
}
