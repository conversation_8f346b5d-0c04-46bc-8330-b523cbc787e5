[versions]
viewer = "0.0.3-SNAPSHOT"
micronaut = "4.8.3"
logging = "7.0.7"
logbackEncoder = "8.1"
openPdf = "2.0.5"
buildConfigPlugin = "5.6.7"
detekt = "1.23.8"
janino = "3.1.12"

[libraries]
kotlin-logging = { module = "io.github.oshai:kotlin-logging-jvm", version.ref = "logging" }
logback-encoder = { module = "net.logstash.logback:logstash-logback-encoder", version.ref = "logbackEncoder" }
openPdf = { module = "com.github.librepdf:openpdf", version.ref = "openPdf" }
janino = { module = "org.codehaus.janino:janino", version.ref = "janino" }

[plugins]
buildconfig = { id = "com.github.gmazzo.buildconfig", version.ref = "buildConfigPlugin" }
detekt = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
