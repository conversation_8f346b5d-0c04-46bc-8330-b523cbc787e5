package com.cha.repository

import com.cha.model.entity.TelephoneEntity
import io.micronaut.data.annotation.Query
import io.micronaut.data.jdbc.annotation.JdbcRepository
import io.micronaut.data.model.query.builder.sql.Dialect
import io.micronaut.data.repository.kotlin.CoroutineCrudRepository
import jakarta.validation.Valid

@JdbcRepository(dialect = Dialect.SQL_SERVER)
interface TelephoneRepository : CoroutineCrudRepository<@Valid TelephoneEntity, Long> {

    suspend fun findByEmpi(empi: Int): List<TelephoneEntity>

    /**
     * Find unique telephone records for a patient by EMPI, filtering out
     * duplicates by type. Uses the ROW_NUMBER() window function to assign
     * numbers to records within each type partition, then selects only the
     * first record from each partition.
     *
     * Normalizes the type values to match our TelephoneType enum (MOBILE,
     * HOME, WORK, OTHER) to ensure proper grouping and mapping.
     *
     * @param empi The patient's Enterprise Master Patient Index
     * @return List of unique telephone records with no duplicate types
     */
    @Query(
        value = """
        WITH RankedTelephones AS (
          SELECT *, ROW_NUMBER() OVER (
            PARTITION BY empi, TYPE ORDER BY empi
          ) AS row_num
          FROM telephone
          WHERE empi = :empi
        )
        SELECT * FROM RankedTelephones WHERE row_num = 1;
    """, nativeQuery = true
    )
    suspend fun findUniqueByEmpiAndType(empi: Int): List<TelephoneEntity>
}
