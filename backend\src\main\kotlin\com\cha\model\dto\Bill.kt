package com.cha.model.dto

import com.cha.model.entity.BillEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime
import java.util.Currency

@Serdeable
@Schema(description = "Patient billing information")
data class Bill(
    @Schema(description = "Patient's Enterprise Master Patient Index", example = "12345")
    val empi: Int?,
    @Schema(description = "System identifier", example = "1")
    val systemId: Int?,
    @Schema(description = "Medical Record Number", example = "MRN123456")
    val mrn: String?,
    @Schema(description = "Account number", example = "ACC789012")
    val accountNumber: String?,
    @Schema(description = "Service date", example = "2024-01-15T10:30:00")
    val serviceDate: LocalDateTime?,
    @Schema(description = "Posting date", example = "2024-01-16T09:00:00")
    val postingDate: LocalDateTime?,
    @Schema(description = "Charge code", example = "99213")
    val chargeCode: String?,
    @Schema(description = "Charge code description", example = "Office visit - established patient")
    val chargeCodeDescription: String?,
    @Schema(description = "Invoice number", example = "INV2024001")
    val invoiceNumber: String?,
    @Schema(description = "NRV identifier", example = "NRV123")
    val nrv: String?,
    @Schema(description = "Department", example = "Cardiology")
    val department: String?,
    @Schema(description = "Account identifier", example = "ACC001")
    val account: String?,
    @Schema(description = "Charge amount")
    val amount: Money?,
    @Schema(description = "Insurance plan", example = "Blue Cross Blue Shield")
    val insurance: String?,
    @Schema(description = "Total amount")
    val totalAmount: Money?,
    @Schema(description = "Number of units", example = "1")
    val units: Int?,
    @Schema(description = "Financial class", example = "Commercial")
    val financialClass: String?,
    @Schema(description = "Visit status", example = "Discharged")
    val visitStatus: String?,
    @Schema(description = "Transaction type", example = "Charge")
    val transactionType: String?,
    @Schema(description = "Transaction date", example = "2024-01-15T14:30:00")
    val transactionDate: LocalDateTime?,
    @Schema(description = "Admission date", example = "2024-01-15T08:00:00")
    val admitDate: LocalDateTime?,
    @Schema(description = "System name where bill originates", example = "HIS")
    val systemName: String?,
    @Schema(description = "Security level flag", example = "false")
    val securityLevel: Boolean?,
)

fun BillEntity.toDto(): Bill {
    val usd = Currency.getInstance("USD")
    return Bill(
        empi = this.empi,
        systemId = this.systemId,
        mrn = this.mrn,
        accountNumber = this.accountNumber,
        serviceDate = this.serviceDate,
        postingDate = this.postingDate,
        chargeCode = this.chargeCode,
        chargeCodeDescription = this.chargeCodeDescription,
        invoiceNumber = this.invoiceNumber,
        nrv = this.nrv,
        department = this.department,
        account = this.account,
        amount = this.amount?.let { Money(it, usd) },
        insurance = this.insurance,
        totalAmount = this.totalAmount?.let { Money(it, usd) },
        units = this.units,
        financialClass = this.financialClass,
        visitStatus = this.visitStatus,
        transactionType = this.transactionType,
        transactionDate = this.transactionDate,
        admitDate = this.admitDate,
        systemName = this.systemName,
        securityLevel = this.securityLevel
    )
}