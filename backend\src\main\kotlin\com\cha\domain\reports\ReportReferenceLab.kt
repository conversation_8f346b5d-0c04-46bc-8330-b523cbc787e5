package com.cha.domain.reports

import com.cha.model.report.VisitSectionsViewModel
import com.lowagie.text.Document
import com.lowagie.text.Paragraph
import com.lowagie.text.pdf.PdfPTable
import com.lowagie.text.pdf.PdfPCell
import java.awt.Color
import com.lowagie.text.Element

object PdfTableUtils {
    fun createTableHeaderCell(text: String): PdfPCell {
        val cell = PdfPCell(Paragraph(text))
        cell.backgroundColor = Color.LIGHT_GRAY
        cell.horizontalAlignment = Element.ALIGN_CENTER
        return cell
    }

    fun createTableDataCell(content: String): PdfPCell {
        return PdfPCell(Paragraph(content))
    }
}

private fun addLabResultsReferenceSection(
    sections: VisitSectionsViewModel?,
    document: Document,
) {
    sections?.clinical?.labResultReferences?.let { labResultRefs ->
        document.add(Paragraph("Lab Result References"))
        if (labResultRefs.isNotEmpty()) {
            val consolidatedTable = PdfPTable(8)
            consolidatedTable.widthPercentage = 100f
            consolidatedTable.setWidths(floatArrayOf(1.2f, 1.5f, 1.2f, 1.5f, 1f, 1f, 1.2f, 1.2f))
            consolidatedTable.setSpacingBefore(10f)
            consolidatedTable.setSpacingAfter(10f)

            consolidatedTable.addCell(PdfTableUtils.createTableHeaderCell("Order Code"))
            consolidatedTable.addCell(PdfTableUtils.createTableHeaderCell("Test Description"))
            consolidatedTable.addCell(PdfTableUtils.createTableHeaderCell("Test Code"))
            consolidatedTable.addCell(PdfTableUtils.createTableHeaderCell("Result Line"))
            consolidatedTable.addCell(PdfTableUtils.createTableHeaderCell("Test Unit"))
            consolidatedTable.addCell(PdfTableUtils.createTableHeaderCell("Status"))
            consolidatedTable.addCell(PdfTableUtils.createTableHeaderCell("Reference Range"))
            consolidatedTable.addCell(PdfTableUtils.createTableHeaderCell("Abnormal Flag"))

            labResultRefs.forEach { ref ->
                consolidatedTable.addCell(PdfTableUtils.createTableDataCell(ref.orderCode ?: "N/A"))
                consolidatedTable.addCell(PdfTableUtils.createTableDataCell(ref.testDescription ?: "N/A"))
                consolidatedTable.addCell(PdfTableUtils.createTableDataCell(ref.testCode ?: "N/A"))
                consolidatedTable.addCell(PdfTableUtils.createTableDataCell(ref.resultLine ?: "N/A"))
                consolidatedTable.addCell(PdfTableUtils.createTableDataCell(ref.testUnit ?: "N/A"))
                consolidatedTable.addCell(PdfTableUtils.createTableDataCell(ref.testStatus ?: "N/A"))
                consolidatedTable.addCell(PdfTableUtils.createTableDataCell(ref.referenceRange ?: "N/A"))
                consolidatedTable.addCell(PdfTableUtils.createTableDataCell(ref.abnormalFlag ?: "N/A"))
            }

            document.add(consolidatedTable)
        } else {
            document.add(Paragraph("No lab result references recorded for this visit."))
        }
    }
}