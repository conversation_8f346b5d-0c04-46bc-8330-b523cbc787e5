package com.cha.repository

import com.cha.model.entity.ReportEntity
import io.micronaut.data.jdbc.annotation.JdbcRepository
import io.micronaut.data.model.query.builder.sql.Dialect
import io.micronaut.data.repository.kotlin.CoroutineCrudRepository
import jakarta.validation.Valid

@JdbcRepository(dialect = Dialect.SQL_SERVER)
interface ReportRepository : CoroutineCrudRepository<@Valid ReportEntity, String> {

    /**
     * Finds all reports for a specific user by looking through the associated
     * job.
     */
    suspend fun findByUserId(userId: String): List<ReportEntity>

    /** Finds all reports for a specific job. */
    // It will look for ReportEntity where reportEntity.job.id == jobId
    suspend fun findByJobId(jobId: String): List<ReportEntity>

    /** Finds reports by EMPI. */
    suspend fun findByEmpi(empi: Int): List<ReportEntity>

    /** Finds a specific report by its ID and the user ID of the associated job. */
    suspend fun findByIdAndUserId(id: String, userId: String): ReportEntity?
}