package com.cha.model.entity

import io.micronaut.data.annotation.GeneratedValue
import io.micronaut.data.annotation.Id
import io.micronaut.data.annotation.MappedEntity
import java.time.LocalDateTime

@MappedEntity("documents")
data class DocumentEntity(
    @field:Id
    @field:GeneratedValue(GeneratedValue.Type.IDENTITY)
    val id: Int? = null,
    val empi: Int?,
    val systemId: Int?,
    val mrn: String?,
    val accountNumber: String?,
    val created: LocalDateTime?,
    val title: String?,
    val category: String?,
    val systemName: String?,
    val filename: String?,
    val securityLevel: Boolean? = false,
    val progressNote: Boolean? = false,
    val admitDate: LocalDateTime?,
)
