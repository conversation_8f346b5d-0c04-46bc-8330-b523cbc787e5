package com.cha.model.dto

import com.cha.model.entity.PatientEntity
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

/**
 * Base class for patient data that contains common fields for both summary
 * and detailed views.
 */
@Serdeable
open class PatientBase(
    @Schema(description = "Enterprise Master Patient Index", example = "1001")
    open val empi: Int,
    @Schema(description = "First name of the patient", example = "<PERSON>")
    open val firstName: String,
    @Schema(description = "Last name of the patient", example = "Doe")
    open val lastName: String,
    @Schema(description = "Middle name of the patient", example = "A.")
    open val middleName: String?,
    @Schema(description = "Sex of the patient", example = "Male")
    open val sex: String,
    @Schema(description = "Birthdate of the patient", example = "1980-01-01")
    open val birthdate: LocalDate,
)

/**
 * Summary-level patient data for search API responses.
 *
 * This model is used for the patient search API and contains only summary
 * fields. It does NOT include address or telephone fields. For detailed
 * patient info, use [PatientInfo].
 *
 * See also: [PatientInfo] in [PatientInfo.kt].
 */
@Serdeable
data class Patient(
    override val empi: Int,
    override val firstName: String,
    override val lastName: String,
    override val middleName: String?,
    override val sex: String,
    override val birthdate: LocalDate,
) : PatientBase(empi, firstName, lastName, middleName, sex, birthdate)

/**
 * Enum representing patient sex, with a localized string for display.
 *
 * @property localized Localized string representation of the sex.
 */
@Serdeable
enum class Sex(val localized: String) {
    FEMALE("Female"),
    MALE("Male"),
    UNKNOWN("Unknown");
}

/**
 * Data class representing a system (EMR source) for a patient.
 *
 * @property id ID of the system
 * @property name Name of the system
 * @property mrn Medical Record Number in the system
 */
@Serdeable
data class System(
    @Schema(description = "ID of the system", example = "3")
    val id: Int,
    @Schema(description = "Name of the system", example = "ECW LITTLETON")
    val name: String,
    @Schema(description = "Medical Record Number", example = "123456")
    val mrn: String? = null,
)

/** Common utilities for patient data conversion. */
object PatientConverter {
    /** Convert a sex code to the corresponding Sex enum. */
    fun getSexEnum(sexCode: String?): Sex {
        return when (sexCode?.uppercase()) {
            "F" -> Sex.FEMALE
            "M" -> Sex.MALE
            else -> Sex.UNKNOWN
        }
    }

    /** Create a system object from a PatientEntity. */
    fun createSystem(entity: PatientEntity): System {
        return System(
            id = entity.systemId ?: throw IllegalArgumentException("System ID cannot be null"),
            name = entity.systemName ?: throw IllegalArgumentException("System name cannot be null"),
            mrn = entity.mrn
        )
    }

    /** Extract basic patient fields from a PatientEntity. */
    fun extractBasicFields(entity: PatientEntity, systems: List<System>): Map<String, Any?> {
        val sexEnum = getSexEnum(entity.sex)

        return mapOf(
            "empi" to entity.empi,
            "lastName" to entity.lastName.lowercase().replaceFirstChar { it.uppercaseChar() },
            "firstName" to entity.firstName.lowercase().replaceFirstChar { it.uppercaseChar() },
            "middleName" to entity.middleName,
            "sex" to sexEnum.localized,
            "birthdate" to entity.bthTs,
            "systems" to systems
        )
    }

    /**
     * Create a consolidated list of systems from multiple PatientEntity
     * objects.
     */
    fun consolidateSystems(entities: List<PatientEntity>): List<System> {
        return entities.mapNotNull { entity ->
            entity.systemId?.let { nonNullSystemId ->
                System(
                    id = nonNullSystemId,
                    name = entity.systemName ?: throw IllegalArgumentException("System name cannot be null"),
                    mrn = entity.mrn
                )
            }
        }
    }
}

/**
 * Map a PatientEntity to a summary Patient DTO (for search API).
 *
 * @return Patient summary DTO
 * @receiver PatientEntity to convert
 */
fun PatientEntity.toDto(): Patient {
    val systems = listOf(PatientConverter.createSystem(this))
    val fields = PatientConverter.extractBasicFields(this, systems)

    return Patient(
        empi = fields["empi"] as Int,
        lastName = fields["lastName"] as String,
        firstName = fields["firstName"] as String,
        middleName = fields["middleName"] as String?,
        sex = fields["sex"] as String,
        birthdate = fields["birthdate"] as LocalDate,
    )
}

/**
 * Consolidate a List<PatientEntity> into a single summary Patient DTO (for
 * search API).
 *
 * @return Patient summary DTO, or null if the list is empty
 * @receiver List of PatientEntity objects to consolidate
 */
fun List<PatientEntity>.consolidateToPatientDto(): Patient? {
    if (this.isEmpty()) {
        return null
    }

    val firstEntity = this.first()
    val systems = PatientConverter.consolidateSystems(this)
    val fields = PatientConverter.extractBasicFields(firstEntity, systems)

    return Patient(
        empi = fields["empi"] as Int,
        lastName = fields["lastName"] as String,
        firstName = fields["firstName"] as String,
        middleName = fields["middleName"] as String?,
        sex = fields["sex"] as String,
        birthdate = fields["birthdate"] as LocalDate,
    )
}
