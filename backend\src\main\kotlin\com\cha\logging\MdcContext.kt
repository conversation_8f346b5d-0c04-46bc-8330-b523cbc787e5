package com.cha.logging

import org.slf4j.MDC
import java.util.*

/**
 * Utility class for managing MDC context in service methods and background
 * tasks. Provides methods to set and clear MDC values for consistent
 * logging.
 */
object MdcContext {

    /**
     * Sets operation context in MDC for service methods or background tasks.
     *
     * @param operationName Name of the operation being performed
     * @param resourceId Optional ID of the resource being operated on
     * @return AutoCloseable that will clear the MDC context when closed
     */
    fun withOperation(operationName: String, resourceId: String? = null): AutoCloseable {
        MDC.put("operationName", operationName)
        resourceId?.let { MDC.put("resourceId", it) }

        return AutoCloseable {
            MDC.remove("operationName")
            MDC.remove("resourceId")
        }
    }

    /**
     * Sets error context in MDC for logging exceptions.
     *
     * @param errorCode Error code for the exception
     * @param errorType Type of error that occurred
     * @return AutoCloseable that will clear the error context when closed
     */
    fun withError(errorCode: String, errorType: String): AutoCloseable {
        MDC.put("errorCode", errorCode)
        MDC.put("errorType", errorType)

        return AutoCloseable {
            MDC.remove("errorCode")
            MDC.remove("errorType")
        }
    }

    /**
     * Creates a new correlation ID and sets it in the MDC. Useful for
     * background tasks that aren't initiated by HTTP requests.
     *
     * @return The generated correlation ID
     */
    fun newCorrelationContext(): String {
        val correlationId = UUID.randomUUID().toString()
        MDC.put("correlationId", correlationId)
        return correlationId
    }

    /**
     * Sets user context in MDC for background tasks.
     *
     * @param userId ID of the user
     * @param tenantId Optional tenant ID
     * @param userRole Optional user role
     * @return AutoCloseable that will clear the user context when closed
     */
    fun withUser(userId: String, tenantId: String? = null, userRole: String? = null): AutoCloseable {
        MDC.put("userId", userId)
        tenantId?.let { MDC.put("tenantId", it) }
        userRole?.let { MDC.put("userRole", it) }

        return AutoCloseable {
            MDC.remove("userId")
            MDC.remove("tenantId")
            MDC.remove("userRole")
        }
    }

    /**
     * Clears all MDC values. Should be called at the end of request processing
     * or background tasks.
     */
    fun clearContext() {
        MDC.clear()
    }
}
