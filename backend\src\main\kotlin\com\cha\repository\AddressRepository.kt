package com.cha.repository

import com.cha.model.entity.AddressEntity
import io.micronaut.data.jdbc.annotation.JdbcRepository
import io.micronaut.data.model.query.builder.sql.Dialect
import io.micronaut.data.repository.kotlin.CoroutineCrudRepository
import jakarta.validation.Valid

@JdbcRepository(dialect = Dialect.SQL_SERVER)
interface AddressRepository : CoroutineCrudRepository<@Valid AddressEntity, Long> {
    suspend fun findByEmpi(empi: Int): List<AddressEntity>
}
