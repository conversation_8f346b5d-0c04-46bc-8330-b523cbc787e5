package com.cha.handler

import com.cha.domain.exception.AuthenticationException
import com.cha.domain.exception.AuthorizationException
import com.cha.domain.exception.InternalServerException
import com.cha.domain.exception.InvalidRequestException
import com.cha.domain.exception.ResourceNotFoundException
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Error
import io.micronaut.problem.HttpStatusType
import org.slf4j.MDC
import org.zalando.problem.Problem
import org.zalando.problem.ThrowableProblem
import java.net.URI
import io.micronaut.security.authentication.AuthenticationException as MicronautAuthenticationException
import io.micronaut.security.authentication.AuthorizationException as MicronautAuthorizationException

private val logger = KotlinLogging.logger {}

/**
 * Global exception handler for the application. Maps domain exceptions to
 * appropriate HTTP responses with Problem JSON format (RFC 7807).
 */
@Controller
class GlobalExceptionHandler {

    companion object {
        private const val TRACE_ID_MDC_KEY = "traceId"
        private const val PROBLEM_BASE_URI = "https://api.viewer.com/problems"
    }

    /** Handles ResourceNotFoundException and returns a 404 Not Found response. */
    @Error(global = true)
    fun handleResourceNotFound(
        request: HttpRequest<*>,
        exception: ResourceNotFoundException,
    ): HttpResponse<ThrowableProblem> {
        val traceId = MDC.get(TRACE_ID_MDC_KEY)
        logger.warn { "Resource not found: ${exception.message} (traceId: $traceId)" }

        val problem = Problem.builder()
            .withType(URI.create("$PROBLEM_BASE_URI/not-found"))
            .withTitle("Resource Not Found")
            .withStatus(HttpStatusType(HttpStatus.NOT_FOUND))
            .withDetail(exception.message)
            .with("resourceType", exception.resourceType)
            .with("identifier", exception.identifier)
            .with("traceId", traceId)
            .build()

        return HttpResponse.notFound<ThrowableProblem>().body(problem)
    }

    /** Handles InvalidRequestException and returns a 400 Bad Request response. */
    @Error(global = true)
    fun handleInvalidRequest(
        request: HttpRequest<*>,
        exception: InvalidRequestException,
    ): HttpResponse<ThrowableProblem> {
        val traceId = MDC.get(TRACE_ID_MDC_KEY)
        logger.warn { "Invalid request: ${exception.message} (traceId: $traceId)" }

        val problemBuilder = Problem.builder()
            .withType(URI.create("$PROBLEM_BASE_URI/invalid-request"))
            .withTitle("Invalid Request")
            .withStatus(HttpStatusType(HttpStatus.BAD_REQUEST))
            .withDetail(exception.message)
            .with("traceId", traceId)

        // Add field errors if present
        if (exception.fieldErrors.isNotEmpty()) {
            problemBuilder.with("fieldErrors", exception.fieldErrors)
        }

        // Add rejected value if present
        if (exception.rejectedValue != null && exception.field != null) {
            problemBuilder.with("rejectedValue", exception.rejectedValue)
            problemBuilder.with("field", exception.field)
        }

        return HttpResponse.badRequest<ThrowableProblem>().body(problemBuilder.build())
    }

    /** Handles AuthenticationException and returns a 401 Unauthorized response. */
    @Error(global = true)
    fun handleAuthentication(
        request: HttpRequest<*>,
        exception: AuthenticationException,
    ): HttpResponse<ThrowableProblem> {
        val traceId = MDC.get(TRACE_ID_MDC_KEY)
        logger.warn { "Authentication failed: ${exception.message} (traceId: $traceId)" }

        val problemBuilder = Problem.builder()
            .withType(URI.create("$PROBLEM_BASE_URI/unauthorized"))
            .withTitle("Authentication Failed")
            .withStatus(HttpStatusType(HttpStatus.UNAUTHORIZED))
            .withDetail(exception.message)
            .with("traceId", traceId)

        // Add challenge information if present
        if (exception.challengeName != null) {
            problemBuilder.with("challengeName", exception.challengeName)
        }

        // Add session if present
        if (exception.session != null) {
            problemBuilder.with("session", exception.session)
        }

        return HttpResponse.unauthorized<ThrowableProblem>().body(problemBuilder.build())
    }

    /** Handles AuthorizationException and returns a 403 Forbidden response. */
    @Error(global = true)
    fun handleAuthorization(
        request: HttpRequest<*>,
        exception: AuthorizationException,
    ): HttpResponse<ThrowableProblem> {
        val traceId = MDC.get(TRACE_ID_MDC_KEY)
        logger.warn { "Authorization failed: ${exception.message} (traceId: $traceId)" }

        val problemBuilder = Problem.builder()
            .withType(URI.create("$PROBLEM_BASE_URI/forbidden"))
            .withTitle("Access Denied")
            .withStatus(HttpStatusType(HttpStatus.FORBIDDEN))
            .withDetail(exception.message)
            .with("traceId", traceId)

        // Add required role if present
        if (exception.requiredRole != null) {
            problemBuilder.with("requiredRole", exception.requiredRole)
        }

        // Add resource information if present
        if (exception.resourceType != null) {
            problemBuilder.with("resourceType", exception.resourceType)
        }
        if (exception.resourceId != null) {
            problemBuilder.with("resourceId", exception.resourceId)
        }

        return HttpResponse.status<ThrowableProblem>(HttpStatus.FORBIDDEN).body(problemBuilder.build())
    }

    /**
     * Handles InternalServerException and returns a 500 Internal Server Error
     * response.
     */
    @Error(global = true)
    fun handleInternalServer(
        request: HttpRequest<*>,
        exception: InternalServerException,
    ): HttpResponse<ThrowableProblem> {
        val traceId = MDC.get(TRACE_ID_MDC_KEY) ?: exception.traceId
        logger.error(exception) { "Internal server error: ${exception.message} (traceId: $traceId)" }

        val problemBuilder = Problem.builder()
            .withType(URI.create("$PROBLEM_BASE_URI/server-error"))
            .withTitle("Internal Server Error")
            .withStatus(HttpStatusType(HttpStatus.INTERNAL_SERVER_ERROR))
            .withDetail(exception.message)
            .with("traceId", traceId)

        // Add error code if present
        if (exception.errorCode != null) {
            problemBuilder.with("errorCode", exception.errorCode)
        }

        return HttpResponse.serverError<ThrowableProblem>().body(problemBuilder.build())
    }

    /**
     * Handles Micronaut's AuthenticationException and returns a 401
     * Unauthorized response.
     */
    @Error(global = true)
    fun handleMicronautAuthentication(
        request: HttpRequest<*>,
        exception: MicronautAuthenticationException,
    ): HttpResponse<ThrowableProblem> {
        val traceId = MDC.get(TRACE_ID_MDC_KEY)
        logger.warn { "Micronaut authentication failed: ${exception.message} (traceId: $traceId)" }

        val problem = Problem.builder()
            .withType(URI.create("$PROBLEM_BASE_URI/unauthorized"))
            .withTitle("Authentication Failed")
            .withStatus(HttpStatusType(HttpStatus.UNAUTHORIZED))
            .withDetail(exception.message ?: "Invalid credentials or session")
            .with("traceId", traceId)
            .build()

        return HttpResponse.unauthorized<ThrowableProblem>().body(problem)
    }

    /**
     * Handles Micronaut's AuthorizationException and returns a 403 Forbidden
     * response.
     */
    @Error(global = true)
    fun handleMicronautAuthorization(
        request: HttpRequest<*>,
        exception: MicronautAuthorizationException,
    ): HttpResponse<ThrowableProblem> {
        val traceId = MDC.get(TRACE_ID_MDC_KEY)
        logger.warn { "Micronaut authorization failed: ${exception.message} (traceId: $traceId)" }

        val problem = Problem.builder()
            .withType(URI.create("$PROBLEM_BASE_URI/forbidden"))
            .withTitle("Access Denied")
            .withStatus(HttpStatusType(HttpStatus.FORBIDDEN))
            .withDetail(exception.message ?: "Insufficient permissions to access this resource")
            .with("traceId", traceId)
            .build()

        return HttpResponse.status<ThrowableProblem>(HttpStatus.FORBIDDEN).body(problem)
    }

    /** Fallback handler for any unhandled exceptions. */
    @Error(global = true)
    fun handleGenericException(
        request: HttpRequest<*>,
        exception: Throwable,
    ): HttpResponse<ThrowableProblem> {
        val traceId = MDC.get(TRACE_ID_MDC_KEY)
        logger.error(exception) { "Unhandled exception: ${exception.message} (traceId: $traceId)" }

        val problem = Problem.builder()
            .withType(URI.create("$PROBLEM_BASE_URI/server-error"))
            .withTitle("Internal Server Error")
            .withStatus(HttpStatusType(HttpStatus.INTERNAL_SERVER_ERROR))
            .withDetail("An unexpected error occurred")
            .with("traceId", traceId)
            .build()

        return HttpResponse.serverError<ThrowableProblem>().body(problem)
    }
}
