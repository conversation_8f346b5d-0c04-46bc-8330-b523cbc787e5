<?xml version='1.0' encoding='UTF-8'?>
<SmellBaseline>
  <ManuallySuppressedIssues/>
  <CurrentIssues>
    <ID>ForbiddenComment:ApplicationStack.kt$ApplicationStack$// TODO: Revisit health percentages for production</ID>
    <ID>ForbiddenComment:Main.kt$// TODO: Resolve the issues with the AWS Solutions and HIPAA security checks</ID>
    <ID>LongMethod:Main.kt$fun main()</ID>
    <ID>LongParameterList:ApplicationStack.kt$ApplicationStack$( scope: Construct, id: String, props: StackProps? = null, // Original props from caller val vpc: IVpc, documentsBucket: IBucket, loggingBucket: IBucket, // val certificate: ICertificate, envName: String, // envName is already a parameter, used for the description environmentVariables: Map&lt;String, String>, appVersion: String )</ID>
    <ID>MagicNumber:ApplicationStack.kt$ApplicationStack$10</ID>
    <ID>MagicNumber:ApplicationStack.kt$ApplicationStack$1024</ID>
    <ID>MagicNumber:ApplicationStack.kt$ApplicationStack$200</ID>
    <ID>MagicNumber:ApplicationStack.kt$ApplicationStack$2048</ID>
    <ID>MagicNumber:ApplicationStack.kt$ApplicationStack$30</ID>
    <ID>MagicNumber:ApplicationStack.kt$ApplicationStack$443</ID>
    <ID>MagicNumber:ApplicationStack.kt$ApplicationStack$80</ID>
    <ID>MagicNumber:ApplicationStack.kt$ApplicationStack$8080</ID>
    <ID>MagicNumber:ApplicationStack.kt$ApplicationStack$90</ID>
    <ID>MagicNumber:InfrastructureStack.kt$InfrastructureStack$1443</ID>
    <ID>MagicNumber:InfrastructureStack.kt$InfrastructureStack$24</ID>
    <ID>MagicNumber:Main.kt$1443</ID>
    <ID>MaxLineLength:ApplicationStack.kt$ApplicationStack$// .resources(listOf("arn:aws:secretsmanager:${region}:${account}:secret:/config/application/*")) // Restrict to secrets under /config/application/</ID>
    <ID>MaxLineLength:ApplicationStack.kt$ApplicationStack$// Instead of adding an ingress rule directly to the rdsSecurityGroup, we'll just export the fargateSecurityGroup</ID>
    <ID>MaxLineLength:ApplicationStack.kt$ApplicationStack.Companion$"Manages the Viewer backend application deployment for the '$envNameForDescription' environment. Includes ECS Fargate service, ALB, ECR image deployment, IAM roles, SGs, and service integrations."</ID>
    <ID>MaxLineLength:InfrastructureStack.kt$InfrastructureStack$.</ID>
    <ID>MaxLineLength:InfrastructureStack.kt$InfrastructureStack$// Note: Since we don't know the route table IDs directly, we create an output that can help in manual configuration</ID>
    <ID>NewLineAtEndOfFile:ViewerAppStackTest.kt$com.cha.infrastructure.ViewerAppStackTest.kt</ID>
    <ID>UnusedPrivateProperty:InfrastructureStack.kt$InfrastructureStack$environmentVariables: Map&lt;String, String></ID>
    <ID>UnusedPrivateProperty:Main.kt$val awsSolutionsChecks = AwsSolutionsChecks.Builder .create() .verbose(true) .build()</ID>
    <ID>UnusedPrivateProperty:Main.kt$val hIPPASecurityChecks = HIPAASecurityChecks.Builder .create() .verbose(true) .build()</ID>
  </CurrentIssues>
</SmellBaseline>
