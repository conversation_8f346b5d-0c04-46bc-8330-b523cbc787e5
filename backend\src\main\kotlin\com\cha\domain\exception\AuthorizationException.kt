package com.cha.domain.exception

/**
 * Exception thrown when a user is authenticated but not authorized to
 * access a resource. This will typically be mapped to a 403 Forbidden HTTP
 * response.
 */
class AuthorizationException(
    val requiredRole: String? = null,
    val resourceType: String? = null,
    val resourceId: String? = null,
    message: String = "You are not authorized to access this resource",
    cause: Throwable? = null,
) : RuntimeException(message, cause)
