# CHA Viewer Backend

## About

This is a Micronaut-based application that hosts the backend API for the CHA Viewer web application.

---

## Development

TODO: Development instructions below need to be updated.

### Prerequisites

Ensure the following are installed on your local machine before starting development:

- [Java 21](https://jdk.java.net/21/)
- [IntelliJ IDEA Community Edition](https://www.jetbrains.com/idea/download/) or another preferred IDE

### Running Locally

1. Clone the repository.
2. Navigate to the root directory of the project.
3. Run the application using Gradle:
   ```bash
   ./gradlew run
   ```

### Running Locally

These instructions explain how to build and run the backend application as a Docker container locally for testing.

**Prerequisites:**

* [Git](https://git-scm.com/downloads) installed.
* [Docker Desktop](https://www.docker.com/products/docker-desktop/) or Docker Engine installed and running.
* Java JDK (e.g., Temurin 21) installed to build the JAR (or let <PERSON><PERSON><PERSON> handle it via wrapper).

**Steps:**

1. **Clone the Repository:**
   ```bash
   git clone <repository_url>
   cd <repository_name>
   ```

2. **Build the Application JAR:**
   The Docker build process requires the application's fat JAR. Generate it using Gradle:
   ```bash
   ./gradlew -p backend shadowJar
   ```
   This will create the JAR file in `backend/build/libs/`.

3. **Configure Local Environment:**
   The application requires environment variables to run.
    * Copy the example environment file:
      ```bash
      cp .env.example .env
      ```
    * Edit the `.env` file in the project root. This file provides essential configuration for both local development and for deploying the AWS infrastructure using CDK.
        * Ensure the following database variables are correctly set for your local or accessible database:
            - `DATABASE_HOST`
            - `DATABASE_PORT`
            - `DATABASE_NAME`
            - `DATABASE_USERNAME`
            - `DATABASE_PASSWORD`
        * Note: The application and CDK stack now directly use these environment variables for database credentials. AWS Secrets Manager is no longer used by default for the RDS password.
        * Update other variables like `AWS_ACCOUNT_ID`, `AWS_REGION`, `COGNITO_POOL_ID`, `APP_BUCKET_NAME`, etc., as needed for your local testing and deployment target.
        * **Note:** For basic application startup without a functional database connection (e.g., to test API endpoints
          that don't hit the DB), you can use placeholder database credentials and ensure the following variable is
          present in your `.env` file (it's also in `.env.example`):
          ```
          DATASOURCES_DEFAULT_INITIALIZATION_FAIL_TIMEOUT=-1
          ```
          This allows the application to start even if the database is not immediately reachable.

4. **Build the Docker Image:**
   From the project root directory (where the `Dockerfile` is located):
   ```bash
   docker build -t viewer-backend:latest .
   ```

5. **Run the Docker Container:**
    * Using the `.env` file for environment variables:
      ```bash
      docker run -d -p 8080:8080 --env-file .env --name viewer-backend-local viewer-backend:latest
      ```
      The `-d` flag runs the container in detached mode. `--name` assigns a convenient name to the container.
    * Alternatively, you can pass individual environment variables using the `-e` flag for each:
      ```bash
      docker run -d -p 8080:8080 \
        -e DATABASE_HOST=your_host \
        -e DATABASE_USERNAME=your_user \
        -e DATABASE_PASSWORD=your_pass \
        # ... other variables ...
        -e DATASOURCES_DEFAULT_INITIALIZATION_FAIL_TIMEOUT=-1 \
        --name viewer-backend-local \
        viewer-backend:latest
      ```

6. **Check Container Logs:**
   To see the application startup logs or troubleshoot issues:
   ```bash
   docker logs viewer-backend-local
   ```
   To follow the logs in real-time:
   ```bash
   docker logs -f viewer-backend-local
   ```

7. **Test the Application:**
   Once the container is running and the application has started, you can access it. For example, try the health
   endpoint:
   ```
   http://localhost:8080/health
   ```

8. **Stopping and Removing the Container:**
    * To stop the container:
      ```bash
      docker stop viewer-backend-local
      ```
    * To remove the container (after it's stopped):
      ```bash
      docker rm viewer-backend-local
      ```

---

## Deployment

### Prerequisites

To deploy the application, you need:

- [AWS CLI](https://aws.amazon.com/cli/) installed and configured.
- [AWS CDK CLI](https://docs.aws.amazon.com/cdk/v2/guide/cli.html) installed.

### Deployment Instructions

* **Important:** Ensure you have a `.env` file at the root of the project, copied from `.env.example` and populated with the correct values for your target deployment environment (AWS Account ID, region, database credentials, VPC details, etc.). The CDK deployment process relies on these environment variables.

1. Generate the deployable artifact:
   ```bash
   ./gradlew :app:shadowJar
   ./gradlew test
   ```
2. Navigate to the `infrastructure` directory:
   ```bash
   cd infrastructure
   ```
3. Run the following commands to manage the deployment:
    - Synthesize the CloudFormation template:
      ```bash
      cdk synth
      ```
    - Deploy the application:
      ```bash
      cdk deploy
      ```
4. Navigate back to the root directory:
   ```bash
   cd ..
   ```

#### Additional CDK Commands

- Compare the deployed stack with the current state:
  ```bash
  cdk diff
  ```
- Open CDK documentation:
  ```bash
  cdk docs
  ```

#### Cleanup

To clean up and destroy the deployed resources:

```bash
cd infrastructure
cdk destroy
cd ..
```

---

## Documentation

### Micronaut Documentation

- [User Guide](https://docs.micronaut.io/4.7.6/guide/index.html)
- [API Reference](https://docs.micronaut.io/4.7.6/api/index.html)
- [Configuration Reference](https://docs.micronaut.io/4.7.6/guide/configurationreference.html)
- [Micronaut Guides](https://guides.micronaut.io/index.html)

### Gradle Plugin Documentation

- [Micronaut Gradle Plugin](https://micronaut-projects.github.io/micronaut-gradle-plugin/latest/)
- [Shadow Gradle Plugin](https://plugins.gradle.org/plugin/com.github.johnrengelman.shadow)
- [GraalVM Gradle Plugin](https://graalvm.github.io/native-build-tools/latest/gradle-plugin.html)

---

## Features and Related Documentation

### AWS-Specific Features

- **AWS Lambda**
    - [Micronaut AWS Lambda Documentation](https://micronaut-projects.github.io/micronaut-aws/latest/guide/index.html)
- **AWS Lambda SnapStart**
    - [AWS Lambda SnapStart Documentation](https://docs.aws.amazon.com/lambda/latest/dg/snapstart.html)
- **AWS Lambda Events Serialization**
    - [Micronaut AWS Lambda Events Serde](https://micronaut-projects.github.io/micronaut-aws/snapshot/guide/#eventsLambdaSerde)
    - [AWS Lambda Events Library](https://github.com/aws/aws-lambda-java-libs/tree/main/aws-lambda-java-events)
- **AWS Object Storage (S3)**
    - [Micronaut AWS Object Storage Documentation](https://micronaut-projects.github.io/micronaut-object-storage/latest/guide/)
    - [Amazon S3 Documentation](https://aws.amazon.com/s3/)
- **AWS CDK**
    - [AWS CDK Documentation](https://docs.aws.amazon.com/cdk/v2/guide/home.html)
- **AWS SDK v2**
    - [Micronaut AWS SDK v2 Documentation](https://micronaut-projects.github.io/micronaut-aws/latest/guide/)
    - [AWS SDK for Java v2 Guide](https://docs.aws.amazon.com/sdk-for-java/v2/developer-guide/welcome.html)

### Micronaut Features

- **Serialization with Jackson**
    - [Micronaut Serialization Jackson Core Documentation](https://micronaut-projects.github.io/micronaut-serialization/latest/guide/)
- **Kotlin Symbol Processing (KSP)**
    - [Micronaut KSP Documentation](https://docs.micronaut.io/latest/guide/#kotlin)
    - [Kotlin KSP Guide](https://kotlinlang.org/docs/ksp-overview.html)

### CI/CD Workflow

- **AWS CodeBuild**
    - [AWS CodeBuild Workflow Documentation](https://docs.aws.amazon.com/codebuild/latest/userguide)

- **OpenAPI Generation and Viewer-App Integration**
    - Automated workflow that generates OpenAPI specifications and triggers downstream processes
    - Located at: `.github/workflows/generate-openapi-and-trigger.yml`
    - **Triggers**: Push to `main`/`develop` branches, manual workflow dispatch
    - **Process**:
      1. Builds the backend application using `./gradlew :backend:build`
      2. Generates `openapi.yml` file automatically (via `generateOpenApiSpec` task)
      3. Commits any changes to the `openapi.yml` file
      4. Triggers the `viewer-app` repository workflow via repository dispatch
    - **Required Permissions**: 
      - `contents: write` (for committing OpenAPI changes)
      - `actions: read` (for workflow execution)
      - GitHub token must have access to trigger workflows in `commhospital/viewer-app` repository

---

## Notes

1. Deployment scripts currently rely on `./test_lambda.sh`. Ensure your local environment meets the prerequisites for
   this method.
2. If applicable, update this README with future deployment automation improvements.

---
