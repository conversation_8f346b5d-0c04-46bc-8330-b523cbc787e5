package com.cha.model.auth

import io.micronaut.core.annotation.Introspected
import io.micronaut.serde.annotation.Serdeable
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank

@Introspected
@Serdeable
@Schema(name = "ResendVerificationRequest", description = "Request to resend email verification")
data class ResendVerificationRequest(
    @field:NotBlank
    @field:Email
    @Schema(description = "Email address to resend verification to", example = "<EMAIL>")
    val email: String
)